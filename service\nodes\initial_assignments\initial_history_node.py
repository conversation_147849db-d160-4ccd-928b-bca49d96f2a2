from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.initial_assignments.initial_history_assignments import generate_initial_history_assignment,generate_initial_history_passage


def generate_initial_history_passage_node(state: GeneratePipelineContext):
    """Extracts inputs from state and passes them to the generator function."""
    logger.info("---Initial Assignment---")

    grade = state.student.grade
    first_name = state.student.first_name
    working_style = state.student.working_style
    reading_level = state.student.reading_level
    life_skills = state.request.life_skills
    passage_style = state.request.passage_style
    interest = state.student.interest
    state_standard = state.request.state_standard
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    add_info = state.request.add_info
    difficulty = state.request.difficulty
    strengths=state.student.strengths
    main_topic=state.request.main_topic
    try:        
        passage,tools = generate_initial_history_passage(
            grade=grade,
            first_name=first_name,
            working_style=working_style,
            reading_level=reading_level,
            life_skills=life_skills,
            passage_style=passage_style,
            interest=interest,
            state_standard=state_standard,
            standard=standard,
            assignment_type=assignment_type,
            add_info=add_info,
            difficulty=difficulty,
            strengths=strengths,
            main_topic=main_topic
            )
        if 'passage' in passage:
            passage=passage['passage']
        
        return {"ai_passage": passage, "tools": tools,'assignment_state': 'processing_passage','attempts': 0}
    
    except Exception as e:
        logger.error(f"Error in history intial_passage_node: {e}")
        return {"error": "Failed to generate initial passage."}
def generate_initial_history_assignment_node(state: GeneratePipelineContext):
    """Extracts inputs from state and passes them to the generator function."""
    logger.info("---Initial Assignment---")
    logger.info("Extract all inputs needed by this function")

    grade = state.student.grade
    first_name = state.student.first_name
    working_style = state.student.working_style
    reading_level = state.student.reading_level
    strengths = state.student.strengths
    state_standard = state.request.state_standard
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    nb_of_questions = state.request.nb_of_questions
    difficulty = state.request.difficulty
    interest=state.student.interest
    add_info=state.request.add_info
    main_topic=state.request.main_topic
    ai_passage=state.ai_passage
    enriched_task=state.enriched_tasks

    logger.info("Students data extracted")
    if state.request.assignment_type=='history_fact':
        passage=state.ai_passage
    else:
        passage=state.request.passage
    try:
        output, tools = generate_initial_history_assignment(
        first_name=first_name,
        grade=grade,
        reading_level=reading_level,
        interests=interest,
        strengths=strengths,
        standard=standard,
        state_standard=state_standard,
        difficulty=difficulty,
        add_info=add_info,
        assignment_type=assignment_type,
        nb_of_questions=nb_of_questions,
        main_topic=main_topic,
        passage=passage,
        ai_passage=ai_passage,
        enriched_task=enriched_task
    )
        if 'homework' in output and hasattr(state,'challenges'):
            output['homework']['challenges']=state.challenges
        elif hasattr(state,'challenges'):
            output['challenges']=state.challenges
        else:
            logger.error("Challenges not found in state")
        return {"assignment": output, "tools": tools, 'assignment_state': 'processing_assignment', 'attempts': 0}

    except Exception as e:
        logger.error("Error in generate_initial_history_assignment_node extract_assignment_inputs")
        return {"error": f"Error in generate_initial_history_assignment_node extract_assignment_inputs :{e}"}
