import unittest
from unittest.mock import patch, MagicMock
from service.common_agents.aggregator_agent import aggregator_agent, aggregator_agent_passage

class TestAggregatorAgent(unittest.TestCase):

    @patch('service.common_agents.aggregator_agent.make_openai_langfuse')
    @patch('service.common_agents.aggregator_agent.langfuse.get_prompt')
    def test_aggregator_agent(self, mock_get_prompt, mock_make_openai_langfuse):
        mock_messages = MagicMock()
        mock_messages.compile.return_value = "compiled_prompt"
        mock_messages.config = {
            "openai": {
                "temperature": 0.7,
                "model": "gpt-4",
                "max_tokens": 2048
            }
        }
        mock_get_prompt.return_value = mock_messages
        mock_make_openai_langfuse.return_value = {"revised_assignment": "Updated content"}

        result = aggregator_agent(
            original_assignment="Write an essay on climate change.",
            adjustment_instructions={"grammar": "Fix passive voice"},
            tools=[],
            assignment_type="essay",
            nb_of_questions=3
        )

        self.assertIn("revised_assignment", result)

    @patch('service.common_agents.aggregator_agent.make_openai_langfuse')
    @patch('service.common_agents.aggregator_agent.langfuse.get_prompt')
    def test_aggregator_agent_passage(self, mock_get_prompt, mock_make_openai_langfuse):
        mock_messages = MagicMock()
        mock_messages.compile.return_value = "compiled_passage_prompt"
        mock_messages.config = {
            "openai": {
                "temperature": 0.6,
                "model": "gpt-4",
                "max_tokens": 3000
            }
        }
        mock_get_prompt.return_value = mock_messages
        mock_make_openai_langfuse.return_value = {"revised_passage": "Updated passage"}

        result = aggregator_agent_passage(
            original_passage="The sun is the primary source of energy...",
            adjustment_instructions={"clarity": "Simplify technical jargon"},
            tools=[]
        )

        self.assertIn("revised_passage", result)


if __name__ == '__main__':
    unittest.main()
