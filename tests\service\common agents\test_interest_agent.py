import pytest
from unittest.mock import patch, MagicMock
@patch('service.common_agents.interest_agent.aisdk_object.cache_result', lambda *a, **kw: lambda f: f)
@patch('service.common_agents.interest_agent.make_openai_langfuse')
@patch('service.common_agents.interest_agent.make_openai_langfuse_websearch')
@patch('service.common_agents.interest_agent.langfuse.get_prompt')
@patch('service.common_agents.interest_agent.random.choice')
def test_interest_search_agent(mock_random_choice, mock_get_prompt, mock_websearch, mock_langfuse):
    from service.common_agents.interest_agent import interest_search_agent

    # Mock subcategories and random selection
    mock_random_choice.return_value = "sports"

    # Mock <PERSON> prompts
    mock_prompt_subcat = MagicMock()
    mock_prompt_subcat.compile.return_value = "compiled_prompt"
    mock_prompt_subcat.config = {
        "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
    }

    mock_prompt_search = MagicMock()
    mock_prompt_search.compile.return_value = "search_prompt"
    mock_prompt_search.config = {
        "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
    }

    mock_get_prompt.side_effect = [mock_prompt_subcat, mock_prompt_search]

    mock_langfuse.return_value = {
        "sub_category_list": ["sports", "technology"]
    }

    mock_websearch.return_value = {
        "result": "Interesting article about sports."
    }

    result = interest_search_agent("technology", "5")
    assert "result" in result
    assert result["result"] == "Interesting article about sports."
