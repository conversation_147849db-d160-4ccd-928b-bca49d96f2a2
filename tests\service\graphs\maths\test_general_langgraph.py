# import pytest
# from unittest.mock import patch, MagicMock
# from service.graphs.maths.general_langgraph import new_math_langgraph
# from service.gen_data_models import GeneratePipeline<PERSON>ontex<PERSON>, Student, GenerateRequests


# class MockLangfuseHandler:
#     def __init__(self):
#         pass


# @pytest.fixture
# def mock_context():
#     student = Student(
#         first_name="<PERSON>",
#         last_name="<PERSON><PERSON>",
#         student_id="123",
#         age=10,
#         is_dyslexic=False,
#         strengths=["curiosity"],
#         working_style="analytical",
#         reading_level="grade_4",
#         grade="5"
#     )
#     request = GenerateRequests(assignment_type="math", add_info=True)
#     return GeneratePipelineContext(
#         student=student,
#         request=request,
#         assignment_state="",
#         previous_assignments=[],
#         request_id="test-id"
#     )


# @patch("service.graphs.maths.general_langgraph.StateGraph.compile")
# def test_math_langgraph_success(mock_compile, mock_context):
#     mock_chain = MagicMock()
#     mock_chain.invoke.return_value = {"assignment": {"title": "Mock Math Homework"}}
#     mock_compile.return_value = mock_chain

#     result = new_math_langgraph(mock_context, MockLangfuseHandler())
#     assert result == {"homework": {"title": "Mock Math Homework"}}


# @patch("service.graphs.maths.general_langgraph.StateGraph.compile")
# def test_math_langgraph_missing_assignment_key(mock_compile, mock_context):
#     mock_chain = MagicMock()
#     mock_chain.invoke.return_value = {"unexpected": "data"}
#     mock_compile.return_value = mock_chain

#     result = new_math_langgraph(mock_context, MockLangfuseHandler())
#     assert result == {"unexpected": "data"}


# @patch("service.graphs.maths.general_langgraph.StateGraph.compile", side_effect=Exception("Graph compile failed"))
# def test_math_langgraph_compile_failure(mock_compile, mock_context):
#     with pytest.raises(Exception) as exc_info:
#         new_math_langgraph(mock_context, MockLangfuseHandler())
#     assert "Graph compile failed" in str(exc_info.value)


# @patch("service.graphs.maths.general_langgraph.StateGraph.compile")
# def test_math_langgraph_invoke_failure(mock_compile, mock_context):
#     mock_chain = MagicMock()
#     mock_chain.invoke.side_effect = Exception("Invoke failed")
#     mock_compile.return_value = mock_chain

#     result = new_math_langgraph(mock_context, MockLangfuseHandler())
#     assert result["homework"] is None
#     assert "Invoke failed" in result["error"]
