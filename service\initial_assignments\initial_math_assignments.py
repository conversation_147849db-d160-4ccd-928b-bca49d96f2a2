from service.common_functions import contingent_working_style, reading_level_example, character_strength_explanations, difficulty_examples, guidelines
from service.context_pipeline import langfuse
from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import aisdk_object, logger
import copy

def generate_initial_math_assignment(first_name, grade, reading_level, interests, working_style, strengths, standard, state_standard, difficulty, assignment_type, nb_of_questions,tasks_enriched_with_interest):
    """
    Generates the necessary prompt and tools to create assignments or questions tailored to a student's profile, educational standards, 
    and interests. The function dynamically adjusts the generated content based on math assignment type (math_fact, math_story)
    and customizes the text to suit the student's working style, reading level, and character strengths.
    """
    tools = []

    if nb_of_questions == 1:
        challenge_schema = {
            "type": "object",
            "description": "A single math challenge to be solved",
            "properties": {
                "index": {"type": "string", "description": "The challenge number"},
                "title": {"type": "string", "description": "The title of the challenge"},
                "task": {"type": "string", "description": "Task to be completed in the challenge"},
                "skill_number": {"type": "string", "description": "The skill number"}
                #"answerKey": {"type": "string", "description": "Answer key for the challenge"}
            },
            "required": ["index", "title", "task", "skill_number"]
        }
    else:
        challenge_schema = {
            "type": "array",
            "description": "A list of math challenges to be solved",
            "items": {
                "type": "object",
                "properties": {
                    "index": {"type": "string", "description": "The challenge number"},
                    "title": {"type": "string", "description": "The title of the challenge"},
                    "task": {"type": "string", "description": "Task to be completed in the challenge"},
                    "skill_number": {"type": "string", "description": "The skill number"}
                    #"answerKey": {"type": "string", "description": "Answer key for the challenge"}
                },
                "required": ["index", "title", "task", "skill_number"]
            }
        }

    standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')
    cs_explanations = character_strength_explanations(strengths)
    working_style_data = contingent_working_style(working_style)
    reading_level_example_text = reading_level_example(reading_level)
    difficulty_examples_text = difficulty_examples(difficulty)

    if assignment_type == "math_story":
        prompt_template = langfuse.get_prompt("math_story", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            difficulty=difficulty,
            difficulty_examples=difficulty_examples_text,
            standard=standard_data,
            guidelines=guidelines,
            interests=interests,
            working_style=working_style,
            working_style_data=working_style_data,
            strengths=strengths,
            cs_explanations=cs_explanations,
            reading_level=reading_level,
            reading_level_example=reading_level_example_text,
            enriched_tasks_with_interest=tasks_enriched_with_interest
        )
    
    elif assignment_type == "math_word_problem":
        prompt_template = langfuse.get_prompt("math_word_problem", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            difficulty=difficulty,
            difficulty_examples=difficulty_examples_text,
            interests=interests,
            working_style=working_style,
            working_style_data=working_style_data,
            strengths=strengths,
            cs_explanations=cs_explanations,
            reading_level=reading_level,
            reading_level_example=reading_level_example_text,
            standard=standard_data,
            enriched_tasks_with_interest=tasks_enriched_with_interest

        )

    elif assignment_type == "math_partner":
        prompt_template = langfuse.get_prompt("math_partner", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            difficulty_level=difficulty,
            difficulty_examples=difficulty_examples_text,
            interests=interests,
            working_style=working_style,
            working_style_data=working_style_data,
            strengths=strengths,
            cs_explanations=cs_explanations,
            reading_level=reading_level,
            reading_level_example=reading_level_example_text,
            standard=standard_data,
            enriched_tasks_with_interest=tasks_enriched_with_interest
        )
        
    elif assignment_type == "math_home_dynamic":
        prompt_template = langfuse.get_prompt("math_home_dynamic", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            difficulty=difficulty,
            difficulty_examples=difficulty_examples_text,
            interests=interests,
            working_style=working_style,
            working_style_data=working_style_data,
            strengths=strengths,
            cs_explanations=cs_explanations,
            reading_level=reading_level,
            reading_level_example=reading_level_example_text,
            standard=standard_data,
            enriched_tasks_with_interest=tasks_enriched_with_interest
        )
        
    elif assignment_type == "math_fact":
        prompt_template = langfuse.get_prompt("math_fact", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            standard=standard_data,
            working_style=working_style,
            reading_level=reading_level,
            character_strengths=strengths,
            enriched_tasks_with_interest=tasks_enriched_with_interest
        )
        
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "create_challenge",
                    "description": f"Creates {nb_of_questions} number of challenges for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "homework": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the homework assignment"
                                    },
                                    "introduction": {
                                        "type": "string",
                                        "description": "An introduction to the homework scenario"
                                    },
                                    "challenges": challenge_schema,
                                    "conclusion": {
                                        "type": "string",
                                        "description": "The conclusion of the homework scenario"
                                    },
                                    "reflection": {
                                        "type": "string",
                                        "description": "Reflection question for the student"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the homework",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the homework"
                                    }
                                },
                                "required": ["title", "introduction", "challenges", "conclusion", "reflection", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
        
    elif assignment_type == "math_worked_example":
        prompt_template = langfuse.get_prompt("math_worked_example", label="latest")
        
        complete_chat_prompt = prompt_template.compile(
            state_standard=state_standard,
            nb_of_questions=nb_of_questions,
            first_name=first_name,
            grade=grade,
            difficulty=difficulty,
            difficulty_examples=difficulty_examples_text,
            interests=interests,
            working_style=working_style,
            working_style_data=working_style_data,
            strengths=strengths,
            cs_explanations=cs_explanations,
            reading_level=reading_level,
            reading_level_example=reading_level_example_text,
            standard=standard_data,
            enriched_tasks_with_interest=tasks_enriched_with_interest
        )

    if not tools:
        if nb_of_questions == 1:
            challenge_schema = {
                "type": "object",
                "description": "A single math challenge to be solved",
                "properties": {
                    "index": {"type": "string", "description": "The challenge number"},
                    "title": {"type": "string", "description": "The title of the challenge"},
                    "task": {"type": "string", "description": "Task to be completed in the challenge. Max 2 sentences."},
                    # "answerKey": {"type": "string", "description": "Answer key for the challenge"},
                    "scenario": { "type": "string", "description": "The scenario for the challenge that fits into the homework narrative. Max 2 sentences." },
                    "skill_number": {"type": "string", "description": "The skill number"}
                },
                "required": ["index", "title", "task", "scenario", "skill_number"]
            }
        else:
            challenge_schema = {
            "type": "array",
            "description": "A list of math challenges to be solved",
            "items": {
                "type": "object",
                "properties": {
                    "index": {"type": "string", "description": "The challenge number"},
                    "title": {"type": "string", "description": "The title of the challenge"},
                    "task": {"type": "string", "description": "Task to be completed in the challenge. Max 2 sentences."},
                    # "answerKey": {"type": "string", "description": "Answer key for the challenge"},
                    "scenario": { "type": "string", "description": "The scenario for the challenge that fits into the homework narrative. Max 2 sentences" },
                    "skill_number": {"type": "string", "description": "The skill number"}
                },
                "required": ["index", "title", "task", "scenario", "skill_number"]
            }
        }
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "create_challenge",
                    "description": f"Creates {nb_of_questions} number of challenges for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "homework": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the homework assignment"
                                    },
                                    "introduction": {
                                        "type": "string",
                                        "description": "An introduction to the homework scenario"
                                    },
                                    "challenges": challenge_schema,
                                    "conclusion": {
                                        "type": "string",
                                        "description": "The conclusion of the homework scenario"
                                    },
                                    "reflection": {
                                        "type": "string",
                                        "description": "Reflection question for the student"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the homework",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the homework"
                                    }
                                },
                                "required": ["title", "introduction", "challenges", "conclusion", "reflection", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    temperature = prompt_template.config.get("openai", {}).get("temperature",0.5)
    model = prompt_template.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = prompt_template.config.get("openai", {}).get("max_tokens",4096)
    logger.info("Making OpenAI API call with langfuse tracking")
    temp_tools = copy.deepcopy(tools)
    temp_tools[0]['function']['parameters']['properties']['homework']['properties'].pop('challenges', None)
    logger.info(f"tools used: {temp_tools}")
    if nb_of_questions==1:
        assignment = make_openai_langfuse(complete_chat_prompt, tools=temp_tools, model=model, temperature=temperature, max_tokens=max_tokens)
        if "homework" in assignment and isinstance(assignment["homework"].get("challenges"), dict):
            assignment["homework"]["challenges"] = [assignment["homework"]["challenges"]]
    else:
        assignment = make_openai_langfuse(complete_chat_prompt, tools=temp_tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb_of_questions)
        
    return assignment, tools
    
