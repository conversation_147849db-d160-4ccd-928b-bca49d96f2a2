from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object
from concurrent.futures import ThreadPoolExecutor, as_completed

def answer_key_verification(json_response: dict, assignment_type: str, nb: int, tools: dict, passage: str = "") -> dict:
    """
    Core agent functionality for verifying and correcting answer keys in assignments.

    Checks the accuracy and completeness of answer keys based on the assignment type
    and makes corrections as needed.

    Args:
        json_response: The JSON object containing assignment data
        assignment_type: Type of assignment ('math', 'vocab_fill', or others)
        nb: The number of problems required in the assignment
        tools: Tools required for processing the OpenAI request

    Returns:
        dict: Updated assignment with verified answer keys
    """
    logger.info(f"Starting answer_key_verification for {assignment_type} with {nb} problems")
    try:
        if 'homework' in json_response:
            logger.debug(f"Processing homework with {nb} problems")
            challenges=json_response['homework']['challenges']
        elif 'challenges' in json_response:
            logger.debug(f"Processing challenges with {nb} problems")
            challenges=json_response['challenges']
        else:
            logger.debug(f"Processing non-homework assignment: {json_response}")
            logger.debug(f"Using general verification prompt for {assignment_type}")
        if 'essay' in assignment_type.lower():
            logger.debug("Essay assignment type detected, wrapping challenges in list")
            challenges=[challenges]
        if type(challenges)==dict:
            logger.debug("Challenges is a dict, converting to list")
            challenges=[challenges]
        logger.debug("Retrieving answer_key_verification_general prompt from langfuse")
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "generate_answer_keys",
                    "description": "Generates a clear, concise, and measurable list of student-friendly answer keys based on a provided Common Core Standard. The agent must also provide reasoning justifying why the generated answer keys accurately represent the original standard.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "answerKey": {
                                "type": "string",
                                "description": "A clearly written, concise, and measurable answer key that directly reflects the core intent of the provided Common Core Standard."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": "A clear explanation justifying why the answer key is accurate and complete."
                            }
                        },
                        "required": ["answerKey", "reasoning"]
                    }
                }
            }
        ]

        logger.info(f"Starting parallel processing for {len(challenges)} challenges")
        with ThreadPoolExecutor() as executor:
            futures = {}
            for index, challenge_data in enumerate(challenges):
                logger.debug(f"Setting up challenge {index+1} for verification")
                additional_instructions=""
                logger.info(f"challenges data:{challenge_data}")
                if passage:
                    logger.debug(f"Adding passage context to instructions for challenge {index+1}")
                    additional_instructions += f"Context for the question{passage}"
                if 'math' in assignment_type:
                    logger.debug(f"Adding math scenario context to instructions for challenge {index+1}")
                    if 'scenario' in challenge_data:
                        additional_instructions += f"Context for the question{challenge_data['scenario']}"
                    messages=langfuse.get_prompt('answer_key_verification_math', type='chat', label="latest")
                    logger.debug(f"Retrieved math verification prompt from langfuse for challenge {index+1}")
                elif 'vocab_fill' == assignment_type:
                    messages=langfuse.get_prompt('answer_key_verification_vocab_fill', type='chat', label="latest")
                    logger.debug(f"Retrieved vocab_fill verification prompt from langfuse for challenge {index+1}")
                elif 'history' in assignment_type:
                    messages=langfuse.get_prompt('answer_key_verification_history', type='chat', label="latest")
                    logger.debug(f"Retrieved history verification prompt from langfuse for challenge {index+1}")
                else:
                    messages=langfuse.get_prompt('answer_key_verification_ELA', type='chat', label="latest")
                    logger.debug(f"Retrieved ELA verification prompt from langfuse for challenge {index+1}")
                
                logger.debug(f"Compiling chat prompt for challenge {index+1}")
                complete_chat_prompt = messages.compile(task=challenge_data['task'],additional_instructions=additional_instructions)

                temperature = messages.config.get("openai", {}).get("temperature",0.5)
                model = messages.config.get("openai", {}).get("model","gpt-4.1")
                max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
                
                logger.debug(f"Submitting OpenAI request for challenge {index+1} with model={model}, temp={temperature}")
                try:
                    future = executor.submit(make_openai_langfuse, messages=complete_chat_prompt, tools=tools, temperature=temperature, model=model, max_tokens=max_tokens)
                    futures[future] = index
                    logger.debug(f"Successfully submitted challenge {index+1} to thread pool")
                except Exception as e:
                    logger.error(f"Failed to submit challenge {index+1} to thread pool: {e}", exc_info=True)
                    raise

            logger.info(f"All {len(futures)} challenges submitted to thread pool, waiting for completion")
            completed = 0
            for future in as_completed(futures):
                index = futures[future]
                try:
                    logger.debug(f"Processing result for challenge {index+1}")
                    answer_key = future.result()
                    logger.info("results generated for answer key")
                    challenges[index]['answerKey'] = answer_key['answerKey']
                    completed += 1
                    logger.debug(f"Successfully verified answer key for challenge {index+1} ({completed}/{len(futures)} complete)")
                except Exception as e:
                    logger.error(f"Error verifying answer key for challenge {index+1}: {e}", exc_info=True)
        
        logger.info(f"Parallel processing complete. {completed}/{len(futures)} challenges successfully processed")
        logger.debug(f"Updated assignment: {json_response}")
        logger.info("Successfully verified and corrected answer keys")
        return json_response
    except Exception as e:
        logger.error(f"Error in answer_key_verification: {e}", exc_info=True)
        return json_response
