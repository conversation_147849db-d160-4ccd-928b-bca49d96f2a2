from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json

def develop_tasks_agent(extract_skills_output: dict, grade: str, nb_of_questions: int, assignment_type: str, main_topic: str) -> dict:
    """
    Generates `nb_of_questions` tasks with per-task solvability checks and targeted retries.
    Only retries the specific failed task for the same sub-standard; previously accepted tasks are preserved.
    Uses the 'question_eligibility_confirmation' agent with empty context for solvability checks.
    """

    # Retry/safety configuration
    MAX_RETRIES_PER_TASK = 2  # Number of targeted retries for a sub-standard when solvability fails
    MAX_TOTAL_ATTEMPTS_MULTIPLIER = 6  # Safety cap to prevent infinite loops

    logger.info(f"DEVELOP_TASK step-1: Starting develop_tasks_agent with {nb_of_questions} questions, grade: {grade}, assignment_type: {assignment_type}, main_topic: {main_topic}")

    try:
        # Normalize nb_of_questions
        logger.info(f"DEVELOP_TASK step-2: Normalizing nb_of_questions input: {nb_of_questions} (type: {type(nb_of_questions)})")
        if isinstance(nb_of_questions, tuple):
            nb_of_questions = nb_of_questions[0]
            logger.info(f"DEVELOP_TASK step-2a: Converted tuple to single value: {nb_of_questions}")

        # Grade info for prompt
        grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
        logger.info(f"DEVELOP_TASK step-3: Grade info prepared: '{grade_info}'")
 

        # Convert to list if passed as JSON string
        logger.info(f"DEVELOP_TASK step-4: Processing extract_skills_output (type: {type(extract_skills_output)})")
        if isinstance(extract_skills_output, str):
            logger.info(f"DEVELOP_TASK step-4a: Converting JSON string to dictionary")
            try:
                extract_skills_output = json.loads(extract_skills_output)
                logger.info(f"DEVELOP_TASK step-4b: Successfully converted to dictionary with {len(extract_skills_output)} items")
            except Exception as e:
                logger.error(f"DEVELOP_TASK step-4c: Failed to convert extract_skills_output to dictionary: {e}")
                extract_skills_output = []

        # Build a skill map (optional enrichment not used in output, kept for compatibility)
        logger.info(f"DEVELOP_TASK step-5: Building skill map from {len(extract_skills_output)} skills")
        try:
            skill_map = {
                item["skill_number"]: {
                    "skill": item.get("skill"),
                    "demonstration_example": item.get("demonstration_example")
                }
                for item in extract_skills_output
            }
            logger.info(f"DEVELOP_TASK step-5a: Successfully created skill_map with {len(skill_map)} entries")
        except Exception as e:
            logger.error(f"DEVELOP_TASK step-5b: Failed to create skill_map: {e}")
            skill_map = {}

        # Prepare prompt template & OpenAI config once
        logger.info(f"DEVELOP_TASK step-6: Preparing prompt template and OpenAI configuration")
        try:
            messages = langfuse.get_prompt("develop tasks", type="chat", label="latest")
            logger.info(f"DEVELOP_TASK step-6a: Successfully retrieved 'develop tasks' prompt from langfuse")

            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "generate_tasks",
                        "description": (
                            "Produces an ordered list of skill objects, each containing "
                            "a skill_number and a list of tasks containing 2 task"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "skills": {
                                    "type": "object",
                                    "properties": {
                                        "skill_number": {
                                            "type": "string",
                                            "description": "The number of the skill"
                                        },
                                        "tasks": {
                                            "type": "array",
                                            "description": "A list of tasks that measures the skill.",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "task": {
                                                        "type": "string",
                                                        "description": "A task that measures the skill."
                                                    },
                                                },
                                                "required": ["task"]
                                            }
                                        }
                                    },
                                    "required": ["skill_number", "tasks"]
                                }
                            }
                            },
                            "required": ["skills"]
                        }
                    }
            ]
            logger.info(f"DEVELOP_TASK step-6b: Tools configuration prepared")

            temperature = messages.config.get("openai", {}).get("temperature", 0.5)
            model = messages.config.get("openai", {}).get("model", "gpt-4.1")
            max_tokens = 800
            logger.info(f"DEVELOP_TASK step-6c: OpenAI config - model: {model}, temperature: {temperature}, max_tokens: {max_tokens}")
        except Exception as e:
            logger.error(f"DEVELOP_TASK step-6d: Failed to prepare prompt template or OpenAI config: {e}")
            return []
        
        additional_instruction = ""
        # Assignment-type specific instructions
        logger.info(f"DEVELOP_TASK step-7: Processing assignment-type specific instructions for '{assignment_type}'")
        if 'history' in assignment_type:
            logger.info(f"DEVELOP_TASK step-7a: Processing history assignment type")
            if main_topic:
                additional_instruction += f"The main topic of the assignment is {main_topic}."
                logger.info(f"DEVELOP_TASK step-7b: Added main topic instruction: {main_topic}")
        elif 'math' in assignment_type:
            logger.info(f"DEVELOP_TASK step-7c: Processing math assignment type")
            additional_instruction = """
            GRAPHICS LIMITATIONS FOR ALL TASKS

            - No pre-made or uploaded pictures can be handed to students.
            - You may refer to any shape, but only those that can be created using simple outline-only code (not to scale, not editable) can be generated.
            - Students have only a small free-hand draw box with one pen tool; they cannot move, resize, or rotate shapes that appear.
            - Do NOT require rulers, protractors, folding, cutting, or precise measurement.
            - Tasks may ask students to: • draw their own shapes or symmetry lines in the draw box.
            - Avoid wording like “look at the picture above” or “trace over the shape provided.” Replace with “In your draw box, sketch…” 
            - All drawing actions must be limited to the website’s draw box.
            - Do not instruct students to draw anywhere else (e.g., notebooks, paper, etc.).
            - For each task, also generate a suitable and concise title.
            IMPORTANT:  Clearly enclose math expressions if any exactly as provided, within dollar signs ($), e.g., $2x + 3 = 7$. You are not allowed to use the $ sign for anything else including currency instead spell it out as 'dollars'. This instruction is non-negotiable.
            """
            logger.info(f"DEVELOP_TASK step-7d: Added math graphics limitations instructions")

            skill_items = tools[0]['function']['parameters']['properties']['skills']['properties']['tasks']['items']
            skill_items['properties']['title'] = {
                "type": "string",
                "description": "A concise title for the task."
            }
            required_list = skill_items.get('required', [])
            required_list.append('title')
            skill_items['required'] = required_list
            logger.info(f"DEVELOP_TASK step-7e: Added title field requirement for math tasks")

            if assignment_type!='math_fact':
                logger.info(f"DEVELOP_TASK step-7f: Adding scenario field for non-math_fact assignment")
                additional_instruction += "- For each task, provide a concise context in a field called 'scenario'. This context should be a brief story setting relevant to the task, written in no more than two sentences. It must introduce the scenario without directly repeating or rephrasing the task as a question, and should serve as a background or setup for the upcoming task. Do not include any questions in the scenario; instead, use it to establish a natural situation from which the task logically follows."
                #skill_items = tools[0]['function']['parameters']['properties']['skills']['items']
                skill_items['properties']['scenario'] = {
                    "type": "string",
                    "description": "A proper contextual scenario for the task."
                }
                required_list = skill_items.get('required', [])
                required_list.append('scenario')
                skill_items['required'] = required_list
                logger.info(f"DEVELOP_TASK step-7g: Added scenario field requirement for non-math_fact tasks")
        # Solvability confirmation agent (context is always empty string here)
        logger.info(f"DEVELOP_TASK step-8: Setting up solvability confirmation agent")
        confirmation_prompt = langfuse.get_prompt("question_eligibility_confirmation", type="chat", label="latest")
        logger.info(f"DEVELOP_TASK step-8a: Retrieved question_eligibility_confirmation prompt")
        confirmation_tool = [
            {
                "type": "function",
                "function": {
                    "name": "assess_solvability",
                    "description": (
                        "Assess whether a given student question is solvable using only the provided context. "
                        "Do not solve the question. Return only a boolean 'solvable' and a concise 'reasoning' (2–6 sentences) "
                        "explaining why it is or isn't solvable."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "solvable": {
                                "type": "boolean",
                                "description": "True if the question can be uniquely and verifiably solved with the provided context; false otherwise."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": (
                                    "A concise justification (1–5 sentences) for the solvability assessment. "
                                    "Do not provide solutions or solution steps."
                                )
                            }
                        },
                        "required": ["solvable", "reasoning"],
                    }
                }
            }
        ]

        def _extract_candidates(result_obj: dict):
            """
            From the tool result, extract a list of candidate tasks for the first skill object:
            returns a list of dicts: [{ 'skill_number': <str>, 'task': <str> }, ...]
            """
            logger.info(f"DEVELOP_TASK step-extract: Extracting candidates from result_obj")
            try:
                skills = result_obj.get("skills", [])
                #first_item = skills[0]
                if not isinstance(skills, dict):
                    logger.warning(f"DEVELOP_TASK step-extract-3: skills is not a dictionary")
                    return []
                sn = skills.get("skill_number")
                tasks_list = skills.get("tasks", [])
                logger.info(f"DEVELOP_TASK step-extract-4: Processing skill_number '{sn}' with {len(tasks_list) if isinstance(tasks_list, list) else 0} tasks")
                if not sn or not isinstance(tasks_list, list):
                    logger.warning(f"DEVELOP_TASK step-extract-5: Invalid skill_number or tasks_list")
                    return []
                candidates = []
                for t in tasks_list:
                    if isinstance(t, dict):
                        to_update = {
                            "skill_number": sn,
                            "task": t.get("task", "").strip(),
                        }
                        # Only add scenario if present
                        if "scenario" in t:
                            to_update["scenario"] = t["scenario"].strip()
                        # Only add title if present
                        if "title" in t:
                            to_update["title"] = t["title"].strip()
                        candidates.append(to_update)

                logger.info(f"DEVELOP_TASK step-extract-7: Extracted {len(candidates)} valid candidates")
                logger.info(f"DEVELOP_TASK step-extract-8: Exiting _extract_candidates with :{candidates}")
                return candidates
            except Exception as e:
                logger.error(f"DEVELOP_TASK step-extract-8: Exception in _extract_candidates: {e}")
                return []

        total_substandards = len(extract_skills_output)
        logger.info(f"DEVELOP_TASK step-9: Processing {total_substandards} sub-standards")
        if total_substandards == 0:
            logger.warning("DEVELOP_TASK step-9a: No sub-standards provided; returning empty list.")
            return []

        tasks_generated = []
        seen_tasks = set()  # to avoid duplicates across attempts
        logger.info(f"DEVELOP_TASK step-10: Beginning generation with per-task solvability checks, target: {nb_of_questions} questions")

        # Rotating pointer over sub-standards
        sub_idx = 0
        logger.info(f"DEVELOP_TASK step-11: Starting with sub-standard index: {sub_idx}")

        # Safety stop to avoid infinite loops
        max_total_attempts = max(nb_of_questions * MAX_TOTAL_ATTEMPTS_MULTIPLIER, nb_of_questions + 5)
        attempts = 0
        logger.info(f"DEVELOP_TASK step-12: Safety limits - max_total_attempts: {max_total_attempts}, max_retries_per_task: {MAX_RETRIES_PER_TASK}")

        while len(tasks_generated) < nb_of_questions and attempts < max_total_attempts:
            attempts += 1
            logger.info(f"DEVELOP_TASK step-13: Starting attempt {attempts}/{max_total_attempts}, current progress: {len(tasks_generated)}/{nb_of_questions} tasks")

            current_sub = extract_skills_output[sub_idx]
            logger.info(f"DEVELOP_TASK step-13a: Processing sub-standard {sub_idx}: skill_number '{current_sub.get('skill_number', 'N/A')}'")
            sub_idx = (sub_idx + 1) % total_substandards
            logger.info(f"DEVELOP_TASK step-13b: Next sub_idx will be: {sub_idx}")

            # Compile prompt for a single sub-standard to generate a small set of candidates
            logger.info(f"DEVELOP_TASK step-14: Compiling prompt for current sub-standard")
            try:
                develop_chat = messages.compile(
                    extract_skills_output=[current_sub],  # single sub-standard
                    grade_info=grade_info,
                    additional_instructions=additional_instruction
                )
                logger.info(f"DEVELOP_TASK step-14a: Successfully compiled chat prompt")
            except Exception as e:
                logger.error(f"DEVELOP_TASK step-14b: Failed to compile chat prompt: {e}")
                break

            # Initial generation for this sub-standard
            logger.info(f"DEVELOP_TASK step-15: Making initial OpenAI API call for task generation")
            try:
                gen_result = make_openai_langfuse(
                    develop_chat,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    tools=tools
                )
                logger.info(f"DEVELOP_TASK step-15a: Successfully received generation result from OpenAI")
            except Exception as e:
                logger.error(f"DEVELOP_TASK step-15b: Failed to call OpenAI API for generation: {e}")
                continue

            candidates = _extract_candidates(gen_result)
            if not candidates:
                logger.warning("DEVELOP_TASK step-16: No candidates returned by model for current sub-standard; moving on.")
                continue

            logger.info(f"DEVELOP_TASK step-17: Processing {len(candidates)} candidates for solvability check")
            accepted = False

            # Try each candidate returned in this call before retrying the model
            for i, candidate in enumerate(candidates):
                logger.info(f"DEVELOP_TASK step-18: Evaluating candidate {i+1}/{len(candidates)}: '{candidate['task'][:50]}...'")
                task_text_norm = candidate["task"].strip().lower()
                if task_text_norm in seen_tasks:
                    logger.info(f"DEVELOP_TASK step-18a: Skipping duplicate task")
                    continue

                if 'math' in assignment_type:
                    context="The student is given a canva draw box as well as a text box to complete their answers so they can solve any question that requires drawing."
                    logger.info(f"DEVELOP_TASK step-18b: Using math context for solvability check")
                else:
                    context=""
                    logger.info(f"DEVELOP_TASK step-18c: Using empty context for solvability check")

                confirm_chat = confirmation_prompt.compile(
                    question=candidate["task"],
                    context=context
                )
                logger.info(f"DEVELOP_TASK step-19: Making solvability check API call")
                try:
                    decision = make_openai_langfuse(
                        confirm_chat,
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        tools=confirmation_tool
                    )
                    logger.info(f"DEVELOP_TASK step-19a: Successfully received solvability decision")
                except Exception as e:
                    logger.error(f"DEVELOP_TASK step-19b: Failed to call OpenAI API for solvability: {e}")
                    continue

                solvable = decision.get("solvable") if isinstance(decision, dict) else None
                reasoning = decision.get("reasoning", "") if isinstance(decision, dict) else ""
                logger.info(f"DEVELOP_TASK step-20: Solvability decision: solvable={solvable}, reasoning={reasoning}")

                if solvable is True:
                    tasks_generated.append(candidate)
                    seen_tasks.add(task_text_norm)
                    accepted = True
                    logger.info(f"DEVELOP_TASK step-20a: Task accepted! Total tasks now: {len(tasks_generated)}")
                else:
                    logger.info("DEVELOP_TASK step-20b: Question is not solvable; will attempt targeted retries if available.")

            # If none of the candidates from the first call were solvable, perform targeted retries
            retries = 0
            while not accepted and retries < MAX_RETRIES_PER_TASK:
                retries += 1
                logger.info(f"DEVELOP_TASK step-21: Starting retry {retries}/{MAX_RETRIES_PER_TASK} for current sub-standard.")

                logger.info(f"DEVELOP_TASK step-21a: Making retry OpenAI API call")
                try:
                    gen_result = make_openai_langfuse(
                        develop_chat,  # same single-substandard prompt
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        tools=tools
                    )
                    logger.info(f"DEVELOP_TASK step-21b: Successfully received retry generation result")
                except Exception as e:
                    logger.error(f"DEVELOP_TASK step-21c: Retry generation failed: {e}")
                    break

                candidates = _extract_candidates(gen_result)
                if not candidates:
                    logger.warning("DEVELOP_TASK step-22: Retry produced no candidates; continuing retries if any remain.")
                    continue

                logger.info(f"DEVELOP_TASK step-23: Processing {len(candidates)} retry candidates")

                for j, candidate in enumerate(candidates):
                    logger.info(f"DEVELOP_TASK step-24: Evaluating retry candidate {j+1}/{len(candidates)}: '{candidate['task'][:50]}...'")
                    task_text_norm = candidate["task"].strip().lower()
                    if task_text_norm in seen_tasks:
                        logger.info(f"DEVELOP_TASK step-24a: Skipping duplicate retry task")
                        continue

                    if 'math' in assignment_type:
                        context="The student is given a canva draw box as well as a text box to complete their answers so they can solve any question that requires drawing."
                        logger.info(f"DEVELOP_TASK step-24b: Using math context for retry solvability check")
                    else:
                        context=""
                        logger.info(f"DEVELOP_TASK step-24c: Using empty context for retry solvability check")

                    confirm_chat = confirmation_prompt.compile(
                        question=candidate["task"],
                        context=context
                    )
                    logger.info(f"DEVELOP_TASK step-25: Making retry solvability check API call")
                    try:
                        decision = make_openai_langfuse(
                            confirm_chat,
                            model=model,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            tools=confirmation_tool
                        )
                        logger.info(f"DEVELOP_TASK step-25a: Successfully received retry solvability decision")
                    except Exception as e:
                        logger.error(f"DEVELOP_TASK step-25b: Failed to call OpenAI API for solvability (retry): {e}")
                        continue

                    solvable = decision.get("solvable") if isinstance(decision, dict) else None
                    reasoning = decision.get("reasoning", "") if isinstance(decision, dict) else ""
                    logger.info(f"DEVELOP_TASK step-26: [Retry] Solvability decision: solvable={solvable}, reasoning={reasoning}")

                    if solvable is True:
                        tasks_generated.append(candidate)
                        seen_tasks.add(task_text_norm)
                        accepted = True
                        logger.info(f"DEVELOP_TASK step-26a: Retry task accepted! Total tasks now: {len(tasks_generated)}")
                        break  # exit retry loop for this sub-standard

                # If still not accepted, loop will retry again (up to MAX_RETRIES_PER_TASK)
                if not accepted:
                    logger.info(f"DEVELOP_TASK step-27: Retry {retries} did not produce acceptable task, continuing to next retry if available")

        logger.info(f"DEVELOP_TASK step-28: Main generation loop completed after {attempts} attempts")
        if attempts >= max_total_attempts and len(tasks_generated) < nb_of_questions:
            logger.warning(f"DEVELOP_TASK step-28a: Stopped after {attempts} attempts with {len(tasks_generated)} tasks. Consider increasing limits or reviewing prompts.")

        # Trim to exactly nb_of_questions and return
        final_tasks = tasks_generated[:nb_of_questions]
        logger.info(f"DEVELOP_TASK step-29: Trimming to exactly {nb_of_questions} tasks, returning {len(final_tasks)} tasks")
        logger.info(f"DEVELOP_TASK step-30: Final task summary:")
        for i, task in enumerate(final_tasks):
            logger.info(f"DEVELOP_TASK step-30-{i+1}: Task {i+1} - Skill: {task.get('skill_number', 'N/A')}, Task: '{task.get('task', 'N/A')[:100]}...'")

        logger.info(f"DEVELOP_TASK step-31: develop_tasks_agent completed successfully, returning {len(final_tasks)} tasks")
        return final_tasks

    except Exception as e:
        logger.error(f"DEVELOP_TASK step-ERROR: Unexpected error in develop_tasks_agent: {e}")
        return []