from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json

def develop_tasks_agent(extract_skills_output: dict, grade: str, nb_of_questions: int, assignment_type: str, main_topic: str) -> dict:
    """
    Generates `nb_of_questions` tasks with per-task solvability checks and targeted retries.
    Only retries the specific failed task for the same sub-standard; previously accepted tasks are preserved.
    Uses the 'question_eligibility_confirmation' agent with empty context for solvability checks.
    """

    # Retry/safety configuration
    MAX_RETRIES_PER_TASK = 2  # Number of targeted retries for a sub-standard when solvability fails
    MAX_TOTAL_ATTEMPTS_MULTIPLIER = 6  # Safety cap to prevent infinite loops

    logger.info(f"Starting develop_tasks_agent with {nb_of_questions} questions")

    try:
        # Normalize nb_of_questions
        if isinstance(nb_of_questions, tuple):
            nb_of_questions = nb_of_questions[0]

        # Grade info for prompt
        grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
 

        # Convert to list if passed as JSON string
        if isinstance(extract_skills_output, str):
            try:
                extract_skills_output = json.loads(extract_skills_output)
            except Exception as e:
                logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
                extract_skills_output = []

        # Build a skill map (optional enrichment not used in output, kept for compatibility)
        try:
            skill_map = {
                item["skill_number"]: {
                    "skill": item.get("skill"),
                    "demonstration_example": item.get("demonstration_example")
                }
                for item in extract_skills_output
            }
        except Exception as e:
            logger.error(f"Failed to create skill_map: {e}")
            skill_map = {}

        # Prepare prompt template & OpenAI config once
        try:
            messages = langfuse.get_prompt("develop tasks", type="chat", label="latest")
            tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "generate_tasks",
                        "description": (
                            "Produces an ordered list of skill objects, each containing "
                            "a skill_number and a list of tasks containing 2 task"
                        ),
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "skills": {
                                    "type": "array",
                                    "description": (
                                        "A list of skill objects. Each object must include a "
                                        "'skill_number' field and a 'tasks' field."
                                    ),
                                    "items": {
                                        "type": "object",
                                        "properties": {
                                            "skill_number": {
                                                "type": "string",
                                                "description": "The number of the skill"
                                            },
                                            "tasks": {
                                                "type": "array",
                                                "description": "A list of tasks that measures the skill.",
                                                "items": {
                                                    "type": "string",
                                                    "description": "A task that measures the skill."
                                                }
                                            }
                                        },
                                        "required": ["skill_number", "tasks"]
                                    }
                                }
                            },
                            "required": ["skills"]
                        }
                    }
                }
            ]
            temperature = messages.config.get("openai", {}).get("temperature", 0.5)
            model = messages.config.get("openai", {}).get("model", "gpt-4.1")
            max_tokens = 800
            logger.info(f"model used in develop_tasks_agent: {model}")
        except Exception as e:
            logger.error(f"Failed to prepare prompt template or OpenAI config: {e}")
            return []
        
        additional_instruction = ""
        # Assignment-type specific instructions
        if 'history' in assignment_type:
            if main_topic:
                additional_instruction += f"The main topic of the assignment is {main_topic}."
        elif 'math' in assignment_type:
            additional_instruction = """
            GRAPHICS LIMITATIONS FOR ALL TASKS

            - No pre-made or uploaded pictures can be handed to students.
            - You may refer to any shape, but only those that can be created using simple outline-only code (not to scale, not editable) can be generated.
            - Students have only a small free-hand draw box with one pen tool; they cannot move, resize, or rotate shapes that appear.
            - Do NOT require rulers, protractors, folding, cutting, or precise measurement.
            - Tasks may ask students to: • draw their own shapes or symmetry lines in the draw box.
            - Avoid wording like “look at the picture above” or “trace over the shape provided.” Replace with “In your draw box, sketch…” 
            - All drawing actions must be limited to the website’s draw box.
            - Do not instruct students to draw anywhere else (e.g., notebooks, paper, etc.).
            - For each task, also generate a suitable and concise title.
            IMPORTANT:  Clearly enclose math expressions if any exactly as provided, within dollar signs ($), e.g., $2x + 3 = 7$. You are not allowed to use the $ sign for anything else including currency instead spell it out as 'dollars'. This instruction is non-negotiable.
            """
            skill_items = tools[0]['function']['parameters']['properties']['skills']['items']
            skill_items['properties']['title'] = {
                "type": "string",
                "description": "A concise title for the task."
            }
            required_list = skill_items.get('required', [])
            required_list.append('title')
            skill_items['required'] = required_list
            if assignment_type!='math_fact':
                additional_instruction += "- For each task, provide a concise context in a field called 'scenario'. This context should be a brief story setting relevant to the task, written in no more than two sentences. It must introduce the scenario without directly repeating or rephrasing the task as a question, and should serve as a background or setup for the upcoming task. Do not include any questions in the scenario; instead, use it to establish a natural situation from which the task logically follows."                
                skill_items = tools[0]['function']['parameters']['properties']['skills']['items']
                skill_items['properties']['scenario'] = {
                    "type": "string",
                    "description": "A proper contextual scenario for the task."
                }
                required_list = skill_items.get('required', [])
                required_list.append('scenario')
                skill_items['required'] = required_list
        # Solvability confirmation agent (context is always empty string here)
        confirmation_prompt = langfuse.get_prompt("question_eligibility_confirmation", type="chat", label="latest")
        confirmation_tool = [
            {
                "type": "function",
                "function": {
                    "name": "assess_solvability",
                    "description": (
                        "Assess whether a given student question is solvable using only the provided context. "
                        "Do not solve the question. Return only a boolean 'solvable' and a concise 'reasoning' (2–6 sentences) "
                        "explaining why it is or isn't solvable."
                    ),
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "solvable": {
                                "type": "boolean",
                                "description": "True if the question can be uniquely and verifiably solved with the provided context; false otherwise."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": (
                                    "A concise justification (1–5 sentences) for the solvability assessment. "
                                    "Do not provide solutions or solution steps."
                                )
                            }
                        },
                        "required": ["solvable", "reasoning"],
                    }
                }
            }
        ]

        def _extract_candidates(result_obj: dict):
            """
            From the tool result, extract a list of candidate tasks for the first skill object:
            returns a list of dicts: [{ 'skill_number': <str>, 'task': <str> }, ...]
            """
            try:
                skills = result_obj.get("skills", [])
                if not isinstance(skills, list) or len(skills) == 0:
                    return []
                first_item = skills[0]
                if not isinstance(first_item, dict):
                    return []
                sn = first_item.get("skill_number")
                tasks_list = first_item.get("tasks", [])
                if not sn or not isinstance(tasks_list, list):
                    return []
                candidates = []
                for t in tasks_list:
                    if isinstance(t, str) and t.strip():
                        candidates.append({"skill_number": sn, "task": t.strip()})
                return candidates
            except Exception:
                return []

        total_substandards = len(extract_skills_output)
        if total_substandards == 0:
            logger.warning("No sub-standards provided; returning empty list.")
            return []

        tasks_generated = []
        seen_tasks = set()  # to avoid duplicates across attempts
        logger.info("Beginning generation with per-task solvability checks")

        # Rotating pointer over sub-standards
        sub_idx = 0

        # Safety stop to avoid infinite loops
        max_total_attempts = max(nb_of_questions * MAX_TOTAL_ATTEMPTS_MULTIPLIER, nb_of_questions + 5)
        attempts = 0

        while len(tasks_generated) < nb_of_questions and attempts < max_total_attempts:
            attempts += 1

            current_sub = extract_skills_output[sub_idx]
            sub_idx = (sub_idx + 1) % total_substandards

            # Compile prompt for a single sub-standard to generate a small set of candidates
            try:
                develop_chat = messages.compile(
                    extract_skills_output=[current_sub],  # single sub-standard
                    grade_info=grade_info,
                    additional_instructions=additional_instruction
                )
            except Exception as e:
                logger.error(f"Failed to compile chat prompt: {e}")
                break

            # Initial generation for this sub-standard
            try:
                gen_result = make_openai_langfuse(
                    develop_chat,
                    model=model,
                    temperature=temperature,
                    max_tokens=max_tokens,
                    tools=tools
                )
            except Exception as e:
                logger.error(f"Failed to call OpenAI API for generation: {e}")
                continue

            candidates = _extract_candidates(gen_result)
            if not candidates:
                logger.warning("No candidates returned by model for current sub-standard; moving on.")
                continue

            accepted = False

            # Try each candidate returned in this call before retrying the model
            for candidate in candidates:
                task_text_norm = candidate["task"].strip().lower()
                if task_text_norm in seen_tasks:
                    continue

                if 'math' in assignment_type:
                    context="The student is given a canva draw box as well as a text box to complete their answers so they can solve any question that requires drawing."
                else:
                    context=""
                confirm_chat = confirmation_prompt.compile(
                    question=candidate["task"],
                    context=context
                )
                try:
                    decision = make_openai_langfuse(
                        confirm_chat,
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        tools=confirmation_tool
                    )
                except Exception as e:
                    logger.error(f"Failed to call OpenAI API for solvability: {e}")
                    continue

                solvable = decision.get("solvable") if isinstance(decision, dict) else None
                reasoning = decision.get("reasoning", "") if isinstance(decision, dict) else ""
                logger.info(f"Solvability decision: solvable={solvable}, reasoning={reasoning}")

                if solvable is True:
                    tasks_generated.append(candidate)
                    seen_tasks.add(task_text_norm)
                    accepted = True
                    break  # proceed to next sub-standard
                else:
                    logger.info("Question is not solvable; will attempt targeted retries if available.")

            # If none of the candidates from the first call were solvable, perform targeted retries
            retries = 0
            while not accepted and retries < MAX_RETRIES_PER_TASK:
                retries += 1
                logger.info(f"Retry {retries}/{MAX_RETRIES_PER_TASK} for current sub-standard.")

                try:
                    gen_result = make_openai_langfuse(
                        develop_chat,  # same single-substandard prompt
                        model=model,
                        temperature=temperature,
                        max_tokens=max_tokens,
                        tools=tools
                    )
                except Exception as e:
                    logger.error(f"Retry generation failed: {e}")
                    break

                candidates = _extract_candidates(gen_result)
                if not candidates:
                    logger.warning("Retry produced no candidates; continuing retries if any remain.")
                    continue

                for candidate in candidates:
                    task_text_norm = candidate["task"].strip().lower()
                    if task_text_norm in seen_tasks:
                        continue

                    confirm_chat = confirmation_prompt.compile(
                        question=candidate["task"],
                        context=""
                    )
                    try:
                        decision = make_openai_langfuse(
                            confirm_chat,
                            model=model,
                            temperature=temperature,
                            max_tokens=max_tokens,
                            tools=confirmation_tool
                        )
                    except Exception as e:
                        logger.error(f"Failed to call OpenAI API for solvability (retry): {e}")
                        continue

                    solvable = decision.get("solvable") if isinstance(decision, dict) else None
                    reasoning = decision.get("reasoning", "") if isinstance(decision, dict) else ""
                    logger.info(f"[Retry] Solvability decision: solvable={solvable}, reasoning={reasoning}")

                    if solvable is True:
                        tasks_generated.append(candidate)
                        seen_tasks.add(task_text_norm)
                        accepted = True
                        break  # exit retry loop for this sub-standard

                # If still not accepted, loop will retry again (up to MAX_RETRIES_PER_TASK)

        if attempts >= max_total_attempts and len(tasks_generated) < nb_of_questions:
            logger.warning(f"Stopped after {attempts} attempts with {len(tasks_generated)} tasks. Consider increasing limits or reviewing prompts.")

        # Trim to exactly nb_of_questions and return
        logger.info(f"develop_tasks_agent returning: {tasks_generated[:nb_of_questions]}")
        return tasks_generated[:nb_of_questions]

    except Exception as e:
        logger.error(f"Unexpected error in develop_tasks_agent: {e}")
        return []