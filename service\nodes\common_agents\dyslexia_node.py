from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.dyslexia_agent import dyslexia_agent
from service.context_pipeline import logger

def dyslexia_agent_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles enhancing assignment readability for students with dyslexia.
    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting dyslexia_agent_node")

    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type

    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif state.assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")

    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    try:
        content = dyslexia_agent(
            initial_assignment=initial_content,
            nb_of_questions=nb,
            assignment_type=assignment_type
            )
        
        next_steps = content.get('next_steps', '').strip()
        if next_steps:
            state.adjustment_instructions['dyslexia'] = next_steps
            logger.info("Working style adjustment instructions updated.")

        else:
            logger.warning("Adjustment indicated but no 'next_steps' provided.")
        
        return {"adjustment_instructions": state.adjustment_instructions}
    
    except Exception as e:
        logger.error(f"Error in dyslexia_agent_node: {e}")
        return {}
