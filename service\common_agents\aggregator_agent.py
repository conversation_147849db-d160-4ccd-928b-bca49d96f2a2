from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger

def aggregator_agent(original_assignment: str, adjustment_instructions: dict, tools: list, assignment_type: str, nb_of_questions: int, assignment_state: str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
    
    additional_instructions=""
    if 'essay' in assignment_type:
        additional_instructions += """ **Bullet Formatting**: Format the essay prompt in clear bullet points. Each point must be separated by '\\n\\n' for readability. Ensure both instructions and the main essay prompt are formatted as bullets. Use this structure: - Take a clear stance on the issue.\n\n - Provide specific evidence to support your argument.\n\n - Organize your ideas effectively and logically.\n\n - Express your thoughts clearly and persuasively. """
    if 'math' in assignment_type:
        additional_instructions += " MANDATORY: The Task and Scenario cannot be longer than 2 sentences each."
    messages = langfuse.get_prompt('aggregator_agent', type='chat', label="latest")
    if 'homework' in original_assignment:
        original_assignment=original_assignment['homework']
    if 'challenges' in original_assignment:
        if nb_of_questions==1 and type (original_assignment['challenges'])==list:
            original_assignment['challenges']=original_assignment['challenges'][0]
    complete_chat_prompt = messages.compile(
        original_assignment=original_assignment,
        adjustment_instructions=adjustment_instructions,
        additional_instructions=additional_instructions,
        nb_of_questions= nb_of_questions
    )

    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    if nb_of_questions==1:
        result = make_openai_langfuse(complete_chat_prompt, tools, model=model, temperature=temperature, max_tokens=max_tokens)
        if "homework" in result and isinstance(result["homework"].get("challenges"), dict):
            result["homework"]["challenges"] = [result["homework"]["challenges"]]
    else:
        logger.info(f"tools used in aggregator: {tools}")
        result = make_openai_langfuse(complete_chat_prompt, tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb_of_questions)
        
    return result

def aggregator_agent_passage(original_passage: str, adjustment_instructions: dict,grade:str, tools: list) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.
    """
    additional_instructions=""

    messages = langfuse.get_prompt("aggregator_agent_passage", label="latest")
    if grade == "kindergarten" or '1' in grade:
        additional_instructions += "\nPlease ensure the reading passage is specifically written for kindergarten or first-grade students, using only age-appropriate vocabulary and simple sentence structure. The passage must not exceed 100 words in total. It should be highly engaging for young children and must be limited to no more than 2 short paragraphs. Absolutely avoid any content, concepts, or language that could be confusing or unsuitable for early readers. Do not go over the 100-word limit or more than 2 paragraphs under any circumstances."
    else:
        additional_instructions += f"\nPlease ensure the reading passage is suitable for {grade} grade. Ensure that the generated passage is at least 100 words."
    complete_chat_prompt = messages.compile(
        original_passage=original_passage,
        adjustment_instructions=adjustment_instructions,
        additional_instructions=additional_instructions
    )

    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    result = make_openai_langfuse(complete_chat_prompt, tools=tools, temperature=temperature, model=model, max_tokens=max_tokens)

    return result