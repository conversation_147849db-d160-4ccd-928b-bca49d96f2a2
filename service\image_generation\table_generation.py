# import matplotlib
# matplotlib.use('Agg')  # Set non-GUI backend for cloud

# import matplotlib.pyplot as plt
# import pandas as pd
# from io import StringIO, BytesIO
# import base64
from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger

def generate_table(assignment_question, decision, grade_level):
    system_prompt = """
    Role:
    You are a mathematics education expert specialized in creating clear, structured LaTeX-formatted tables (tabular format) for mathematics assignments.

    Explicit Inputs Provided:
    - "assignment_question" explicitly containing:
        - "scenario"
        - "task"
        - "title"
        - "answerKey"
        - "index"
    - "grade_level": explicitly provides the student's grade level.
    - "description": clearly defines the data structure and representation the LaTeX-formatted table should exhibit.

    Explicit Steps:
    1. Carefully read and understand the provided "scenario" and "description".
    2. Generate a structured LaTeX-formatted table ("table_str") explicitly matching the provided "description".
        - Explicitly include ALL numeric and textual data provided directly from the "scenario".
        - NEVER explicitly highlight or indicate solutions or final answers to the assignment task.
    3. Explicitly confirm the table's accuracy, alignment, readability, correctness of the LaTeX tabular environment formatting, and adherence to provided guidelines.

    Explicit Output (JSON structure):
    {
        "table_str": "\\begin{tabular}{|c|c|}\\hline\nColumn 1 & Column 2 \\\\\\hline\ndata11 & data12 \\\\\\hline\ndata21 & data22 \\\\\\hline\n\\end{tabular}"
    }

    (The above structure is illustrative only; explicitly generate your table based on provided description and scenario inputs.)

    Important Explicit Clarification (read carefully):
    - ✅ ALWAYS explicitly INCLUDE numeric and textual data from the "scenario".
    - 🚨 NEVER explicitly indicate or highlight answers or solutions related explicitly to the provided "task" or "answerKey".
    """

    user_prompt = f"""
    assignment_question: {assignment_question}
    grade_level: {grade_level}
    description: {decision['description']}

    Task:
    Generate "table_str" explicitly as a LaTeX-formatted table (tabular environment). Carefully adhere to the provided description, explicitly including numeric and textual data provided directly within "scenario".

    Important Explicit Reminders:
    - ✅ ALWAYS explicitly include numeric/textual data explicitly provided within "scenario".
    - 🚨 NEVER explicitly indicate or highlight solutions or conclusions related explicitly to the provided assignment task.
    """

    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_table",
                "description": "Generate a LaTeX-formatted (tabular) table based on the provided description.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "table_data": {
                            "type": "string",
                            "description": "The table data explicitly formatted as a LaTeX tabular environment string."
                        }
                    },
                    "required": ["table_data"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    response = make_openai_langfuse(messages=messages, tools=tools, model="gpt-4.1-nano", temperature=0.7)
    logger.info(f"response: {response}")
    return response['table_data']
    # image_as_base64=table_string_to_base64_png(response['table_data'])
    # return image_as_base64



# def table_string_to_base64_png(table_str: str) -> str:
#     df = pd.read_csv(StringIO(table_str), sep='|', skipinitialspace=True)
#     df = df.dropna(axis=1, how='all')
#     df.columns = df.columns.str.strip()
#     df = df.applymap(lambda x: x.strip() if isinstance(x, str) else x)

#     fig, ax = plt.subplots()
#     ax.axis('off')
#     table = ax.table(cellText=df.values, colLabels=df.columns, loc='center')
#     table.auto_set_font_size(False)
#     table.set_fontsize(12)
#     table.scale(1.2, 1.2)

#     buf = BytesIO()
#     plt.savefig(buf, format='png', bbox_inches='tight', dpi=200)
#     plt.close(fig)
#     buf.seek(0)
#     return base64.b64encode(buf.read()).decode('utf-8')
