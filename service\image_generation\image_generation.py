# from service.image_generation.image_generation_prompt import generate_direct_image, inspect_generated_image, edit_image
from service.context_pipeline import aisdk_object
from service.context_pipeline import logger
from concurrent.futures import ThreadPoolExecutor, as_completed
import time
# from service.image_generation.decision_making import decision_maker
# from service.image_generation.diagram_generation import generate_diagram
# from service.image_generation.table_generation import generate_table
from service.image_generation.decision_making import visual_necessity_decision_maker
from service.image_generation.visual_aider import generate_visual_aid_instructions
from service.image_generation.question_adjustment_agent import adjust_question_for_graphic
from service.image_generation.diagram_generation import python_graphic_code_generator, run_python_and_get_png,python_graphic_error_fixer,python_graphic_alignment_checker

# def generate_images_for_assignment_parallel( assignment: dict, assignment_id: str, standard: str, grade: str):
#     """
#     Generate images for every challenge in *parallel* and add the result
#     to each question dict as `image_url`.
#     Returns the (possibly nested) `assignment` object, mirroring the
#     structure of the input.
#     """
#     assignment = assignment.get("homework", assignment)

#     challenges = assignment["challenges"]
#     if type(challenges)==dict:
#         challenges=[challenges]

#     def _task(question):
#         """Small wrapper so we can attach extra args cleanly."""
#         return generate_and_validate_graphics(question, assignment_id, standard, grade)

#     with ThreadPoolExecutor() as pool:
#         future_to_question = {
#             pool.submit(_task, q): q for q in challenges
#         }

#         for future in as_completed(future_to_question):
#             question = future_to_question[future]
#             try:
#                 updated_question=future.result()
#                 logger.info(f"Updated question {updated_question}")
#                 question = updated_question
#             except Exception as exc:                    
#                 print(f"[error] {question=}: {exc}")
#                 question = None

#     return assignment
def generate_images_for_assignment_parallel( assignment: dict, assignment_id: str, standard: str, grade: str, processed_standard: list):
    """
    Generate images for every challenge in parallel and write them back
    into the assignment.  A matching “sub-standard” (coming from
    processed_standard) is passed to generate_and_validate_graphics,
    selected by identical skill_number.
    """
    logger.info(f'assignment in generate_images_for_assignment_parallel:{assignment}')
    assignment_root = assignment.get("homework", assignment)

    challenges = assignment_root["challenges"]
    if isinstance(challenges, dict):
        challenges = [challenges]

    skill_to_substandard = {
        substd["skill_number"]: substd
        for substd in processed_standard
        if "skill_number" in substd
    }
    logger.info("skill_to_substandard: %s", skill_to_substandard)

    def _task(question, sub_standard,assignment_id, standard, grade):
        return generate_and_validate_graphics(
            question,
            assignment_id,
            standard,
            grade,
            sub_standard     # ← NEW PARAM
        )

    results = [None] * len(challenges)
    with ThreadPoolExecutor() as executor:
        future_to_idx = {}

        for idx, challenge in enumerate(challenges):
            skill_num = challenge.get("skill_number")
            sub_standard = skill_to_substandard.get(str(skill_num))
            future = executor.submit(_task, challenge, sub_standard,assignment_id, standard, grade)
            future_to_idx[future] = idx

        try:
            for future in as_completed(future_to_idx):
                idx = future_to_idx[future]
                try:
                    results[idx] = future.result()
                except Exception as exc:
                    logger.exception(
                        "Error while generating image for challenge %r: %s",
                        challenges[idx], exc
                    )
                    raise
        except Exception:
            raise  


    logger.info("results in generate_images_for_assignment_parallel: %s", results)
    assignment_root["challenges"] = results
    return assignment

# def generate_and_validate_image(question, assignment_id, standard, grade, env='staging'):
#     try:
#         decision = decision_maker(question, standard, grade)
#     except Exception as e: 
#         logger.error("image_generation_condition failed: %s", e)
#         decision = {}
#     # try:
#     #     decision = decision_maker(question, standard, grade)
#     # except Exception as e: 
#     #     logger.error("image_generation_condition failed: %s", e)
#     #     decision = {}
#     try:
#         diagram_code = generate_diagram(question, decision, grade)
#     except Exception as e: 
#         logger.error("generate_diagram failed: %s", e)
#         diagram_code = None
#     # try:
#     #     table_latex = generate_table(question, decision, grade)
#     # except Exception as e:
#     #     logger.error("generate_table failed: %s", e)
#     #     table_latex = None
#     # try:
#     #     generated_image = generate_direct_image(question,decision,grade)
#     #     inspection = inspect_generated_image(generated_image, question)
#     #     if inspection['image_passed']:
#     #         filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
#     #         image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, generated_image)
#     #         logger.info(f'image_bucket_url: {image_bucket_url}')
#     #     elif not inspection['image_passed'] and inspection['editable']:
#     #         edited_image = edit_image(generated_image, inspection, question)
#     #         #edited_inspection = inspect_generated_image(edited_image, question)
#     #         #if edited_inspection['image_passed']:
#     #         filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
#     #         image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, edited_image)
#     #         logger.info(f'image_bucket_url (edited): {image_bucket_url}')
#     # except Exception as e:  # noqa: BLE001, WPS429
#     #     logger.error("generate_direct_image failed: %s", e)
#     #     image_bucket_url = None
#     # question['image_url'] = image_bucket_url
#     # question['decision'] = decision
#     question['diagram_code'] = diagram_code
#     # question['table_latex'] = table_latex
#     return question
    
    

def generate_and_validate_graphics(question, assignment_id, standard, grade,sub_standard, env='staging'):
    logger.info(f"generate_and_validate_graphics: {question}")
    try:
        decision = visual_necessity_decision_maker(question, standard, grade,sub_standard)
    except Exception as e: 
        logger.error("visual_necessity_decision_maker failed: %s", e)
        decision = {}
    # try:
    #     if decision.get("adjust_question", False) and decision.get("use_graphic", False):
    #         question = adjust_question_for_graphic(question, standard, grade, decision)
    #     else:
    #         logger.info('No question adjustment needed')
    # except Exception as e: 
    #     logger.error("adjust_question_for_graphic failed: %s", e)
    #     question = question
    if decision.get("use_graphic", False):
        try:
            visual_instructions = generate_visual_aid_instructions(question, standard, grade, decision)
        except Exception as e: 
            logger.error("generate_visual_aid_instructions failed: %s", e)
            visual_instructions = []
        try:
            python_code = python_graphic_code_generator(question, visual_instructions, grade, decision)
        except Exception as e: 
            logger.error("python_graphic_code_generator failed: %s", e)
            python_code = None
        try:
            python_code=python_graphic_alignment_checker(question, visual_instructions, grade, decision, python_code)
        except Exception as e: 
            logger.error("python_graphic_alignment_checker failed: %s", e)
            python_code = None
        try:
            if python_code:
                png_b64 = run_python_and_get_png(python_code)
                filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
                image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, png_b64)
                logger.info(f'image_bucket_url: {image_bucket_url}')
            else:
                image_bucket_url = None
        except Exception as e:
            logger.error("run_python_and_get_png failed: %s", e)
            try:
                question['failed_python_code'] = python_code
                question['failed_python_error'] = str(e)
                python_code = python_graphic_error_fixer(question, visual_instructions, grade, decision, python_code, e)
                png_b64 = run_python_and_get_png(python_code)
                filename = f'{env}/generated_images/{assignment_id}/image_{question.get("index", "0")}_{int(time.time())}.png'
                image_bucket_url = aisdk_object.storage.upload_image_to_bucket(filename, png_b64)
                logger.info(f'image_bucket_url: {image_bucket_url}')
            except Exception as e:
                logger.error("python_graphic_error_fixer failed: %s", e)
            logger.error("run_python_and_get_png failed: %s", e)
            image_bucket_url = None

        # try:
        #     javascript_code = javascript_graphic_code_generator(question, visual_instructions, grade, decision)
        # except Exception as e: 
        #     logger.error("javascript_graphic_code_generator failed: %s", e)
        #     javascript_code = None
    else:
        logger.info('No graphic needed')
        python_code = None
        visual_instructions = ""
        image_bucket_url = None
        #javascript_code = None

    question['visual_instructions'] = visual_instructions
    question['decision'] = decision
    question['python_code'] = python_code
    question['image_url'] = image_bucket_url
    #question['javascript_code'] = javascript_code
    return question

