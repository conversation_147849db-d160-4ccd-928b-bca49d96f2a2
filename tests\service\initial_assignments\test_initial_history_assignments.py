# redis.exceptions.TimeoutError
import pytest
from unittest.mock import patch, MagicMock
from service.initial_assignments import initial_history_assignments

@pytest.fixture
def mock_history_inputs():
    return {
        "first_name": "Jordan",
        "grade": "6",
        "reading_level": "grade_6",
        "interests": ["civil rights"],
        "strengths": ["resilience"],
        "standard": {"common_core_standard": "RH.6-8.1"},
        "state_standard": "CA-HIS-6.1",
        "difficulty": "medium",
        "add_info": "Jordan is interested in activism",
        "assignment_type": "history_fact",
        "nb_of_questions": 3,
        "main_topic": "Civil Rights Movement",
        "passage": "A brief passage on civil rights."
    }

@patch("service.initial_assignments.initial_history_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_history_assignments.make_openai_langfuse")
def test_generate_history_fact_assignment_success(mock_openai, mock_prompt, mock_history_inputs):
    mock_prompt.return_value.compile.return_value = "compiled history_fact prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "History Assignment"}}

    output, tools = initial_history_assignments.generate_initial_history_assignment(**mock_history_inputs)
    assert isinstance(output, dict)
    assert "homework" in output
    assert output["homework"]["title"] == "History Assignment"

@patch("service.initial_assignments.initial_history_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_history_assignments.make_openai_langfuse")
def test_generate_history_vocab_assignment_success(mock_openai, mock_prompt, mock_history_inputs):
    mock_history_inputs["assignment_type"] = "history_vocab"
    mock_prompt.return_value.compile.return_value = "compiled history_vocab prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Vocabulary Assignment"}}

    output, tools = initial_history_assignments.generate_initial_history_assignment(**mock_history_inputs)
    assert output["homework"]["title"] == "Vocabulary Assignment"

@patch("service.initial_assignments.initial_history_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_history_assignments.make_openai_langfuse")
def test_generate_history_critical_assignment_success(mock_openai, mock_prompt, mock_history_inputs):
    mock_history_inputs["assignment_type"] = "history_critical"
    mock_prompt.return_value.compile.return_value = "compiled history_critical prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Critical Thinking Assignment"}}

    output, tools = initial_history_assignments.generate_initial_history_assignment(**mock_history_inputs)
    assert output["homework"]["title"] == "Critical Thinking Assignment"

@patch("service.initial_assignments.initial_history_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_history_assignments.make_openai_langfuse")
def test_generate_history_essay_assignment_success(mock_openai, mock_prompt, mock_history_inputs):
    mock_history_inputs["assignment_type"] = "history_essay"
    mock_prompt.return_value.compile.return_value = "compiled history_essay prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Essay Assignment"}}

    output, tools = initial_history_assignments.generate_initial_history_assignment(**mock_history_inputs)
    assert output["homework"]["title"] == "Essay Assignment"

def test_generate_history_assignment_invalid_type(mock_history_inputs):
    mock_history_inputs["assignment_type"] = "unknown_type"
    with pytest.raises(ValueError, match="Unknown assignment type: unknown_type"):
        initial_history_assignments.generate_initial_history_assignment(**mock_history_inputs)
