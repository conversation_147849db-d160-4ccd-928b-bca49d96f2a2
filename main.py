# Generate Assignment endpoint setup
# Handles incoming requests and parses request parameters
# Generates the assignment and returns the response
import os
from uuid import uuid4
#from service.ela.ela_graph import reading_comp_gen_flow
from service.pre_process_gen import preprocess_gen_input
from service.context_pipeline import aisdk_object,logger
from service.gen_flow_manager import route_assignment
import time
start_time = time.time()

#@aisdk_object.auth_decorator
def generate_assignment(request):
    # Define CORS headers at the very beginning to ensure they're always available
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type,Authorization',
        'Access-Control-Max-Age': '3600'
    }
    
    # Handle preflight requests first
    if request.method == 'OPTIONS':
        return ('', 204, headers)
    
    try:
        global logger
        logger.info(f"Starting generate_assignment with request:{request.json}")
        default_ctx=aisdk_object.set_pipeline_context(request.json)
        logger=aisdk_object.get_logging_handler()
        
        # Extract Request ID (Trace Context)
        request_id = request.headers.get("X-Cloud-Trace-Context", str(uuid4()))
        if '/' in request_id:
            request_id = request_id.split('/')[0]
        else:
            logger.info(f"Request ID format different{request_id}")
        logger.info(f"request_id: {request_id}")

        langfuse_handler=aisdk_object.get_langfuse_handler(request_id)
        
        # Set CORS headers for all responses
        allowed_origins = ['https://uniqlearn.web.app']
        origin = request.headers.get('Origin')
        
        logger.info(f'Beginning of generate_assignment:{request}')

        data = request.json
        
        try:
            Ptx = preprocess_gen_input(data, request_id,default_ctx)
        
        except Exception as e:
            logger.error(f"Error in preprocess_gen_input: {e}")
            try: 
                aisdk_object.slack_logging.log(Ptx,start_time,error_flag=True)
            except Exception as ex:
                logger.error(f"Error in slack_logging: {ex}")
            return (f"Error in preprocessing: {str(e)}", 500, headers)
            
        try:
            response = route_assignment(Ptx, langfuse_handler)
            logger.info(f"generated assignment:{response}")
            try:
                aisdk_object.firestore.save_assignment_data(data['student_id'],data['assignment_type'],request_id,response['data'])
                logger.info(f"assignment saved to firestore")
                standard_id=data['standard'].get('id',data['standard'].get('standard_code',''))
                logger.info(f"student_id:{data['student_id']},standard_id:{standard_id},substandards_data:{response['data']['substandards_data']}")
                aisdk_object.firestore.save_standard_for_student(data['student_id'],standard_id,response['data']['substandards_data'])
                logger.info(f"standard saved to firestore")
            except Exception as e:
                logger.error(f"Error saving the assignment to firestore {e}")
            response['student_id']=data['student_id']
            response['teacher_id']=data['teacher_id']
            response['session_id']=data['session_id']
            response['assignment_id']=request_id
            if 'passage' in data:
                response['passage']=data['passage']
            
            try: 
                aisdk_object.slack_logging.log({"response":  response, "request": Ptx.dict()},start_time)
                logger.info(f"slack logging successful")
            except Exception as ex:
                logger.error(f"Error in slack_logging: {ex}")

            return (response, 200, headers)
            
        except Exception as e:
            logger.error(f"Error in generating flow: {e}")
            try: 
                aisdk_object.slack_logging.log(Ptx,start_time,error_flag=True)
            except Exception as ex:
                logger.error(f"Error in slack_logging: {ex}")
            return (f"Error in generating assignment: {str(e)}", 500, headers)
    
    except Exception as main_e:
        logger.error(f"Error in generate_assignment: {main_e}")
        return (f"Error processing request: {str(main_e)}", 500, headers)


