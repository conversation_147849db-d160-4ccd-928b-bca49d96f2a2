from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger
def enrich_task_enhancement_agent(enriched_tasks: list, interest: str,grade: str,assignment_type: str) -> list:
    system_prompt=f"""
        - You are a Creative Task Rewriter. You restyle assignment prompts for kids of grade {grade} so they feel fresh and not repetitive, while preserving the exact meaning, skill, and difficulty.

        - Input:
        - A JSON array of task objects containing at least the key "enriched_task" (and possibly others like "skill_number").
        - You will only receive the initial versions of the enriched_task texts.

        - Your job:
        - Rewrite only the text in each "enriched_task" to reduce repetition in language and structure.
        - Keep the intent, steps, and difficulty identical. Do not add or remove requirements.
        - Do not modify any other fields (e.g., skill_number) or their values.
        - Return only JSON with the same structure, keys, order, and number of items. No extra commentary.

        - Rewriting rules:
        - Preserve meaning: keep all required actions, concepts, and any essential terms present in the original (e.g., volume, rectangular prism).
        - Keep length roughly similar to the original; if the original has multiple steps or sentences, mirror the count but vary the phrasing.
        - Reduce repetition across tasks in the same batch:
            - Vary theme/setting (e.g., kitchen, sports, pets, space, nature, construction).
            - Vary point of view (you/we/they) and sentence starters.
            - Vary verbs and sentence structures (mix instructions and questions when appropriate).
            - Avoid reusing distinctive phrases (e.g., don’t repeat “Pretend you’re…” across tasks).
        - Language: age-appropriate, concrete, and clear; default to general elementary tone unless the input suggests otherwise.
        - Safety: keep content school-appropriate; avoid sensitive topics.

        - Quality checks before returning:
        - Same number of items; same keys; same order.
        - Each enriched_task keeps the same meaning and difficulty, with diversified language.
        - Minimize lexical overlap with the original phrasing and between tasks in the same batch.
        IMPORTANT CONSTRAINT:
        - Preserve the main interest: reflect that the student interest theme in every rewritten “enriched_task” is preserved; do not replace, generalize, or omit it.

        - Output format:
        - Return a JSON array with the exact same structure.
        """
    user_prompt=f"""Rewrite the “enriched_task” in each object so the wording and structure feel different and not repetitive, but the meaning and difficulty stay the same. Do not change any keys or values except the text of “enriched_task.” Return only JSON with the same structure and order.
    Important Constraint: Student interest to preserve verbatim in every rewritten “enriched_task” (do not change or remove): {interest}.
    Input: {enriched_tasks}
    """
    tools = [
        {
            "type": "function",
            "function": {
                "name": "refined_enriched_tasks",
                "description": "Generate a list of enriched tasks with diversified language and structure.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "enriched_tasks": {
                            "type": "array",
                            "description": "A list of enriched tasks with diversified language and structure.",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "enriched_task": {
                                        "type": "string",
                                        "description": "A creative, interest-based rewrite of the original mathematical task which should be engaging, relatable, "
                                        "and clearly grounded in both the mathematical skill and the provided interest context."
                                    },
                                    "skill_number": {
                                        "type": "string",
                                        "description": "The skill number"
                                    }
                                },
                                "required": ["enriched_task", "skill_number"]
                            }
                        }
                    },
                    "required": ["enriched_tasks"]
                }
            }
        }
    ]
    additional_instruction=""
    if 'math' in assignment_type:
        additional_instruction += "- For each task, also rewrite the title to be suitable and concise to the rewritten enriched_task."
        skill_items = tools[0]['function']['parameters']['properties']['enriched_tasks']['items']
        skill_items['properties']['title'] = {
            "type": "string",
            "description": "A concise title for the task."
        }
        required_list = skill_items.get('required', [])
        required_list.append('title')
        skill_items['required'] = required_list
        if assignment_type!='math_fact':
            additional_instruction += "- For each task, also rewrite the scenario to be suitable and concise to the rewritten enriched_task."
            skill_items = tools[0]['function']['parameters']['properties']['enriched_tasks']['items']
            skill_items['properties']['scenario'] = {
                "type": "string",
                "description": "A proper contextual scenario for the task."
            }
            required_list = skill_items.get('required', [])
            required_list.append('scenario')
            skill_items['required'] = required_list
    user_prompt += additional_instruction
    messages=[{"role": "system", "content": system_prompt},
              {"role": "user", "content": user_prompt}]
    response = make_openai_langfuse(
        messages=messages,
        model="gpt-4.1",
        temperature=0.5,
        max_tokens=4096,
        tools=tools
    )
    result=response['enriched_tasks']
    logger.info(f"result after new enrich enhancement agent: {result}")
    
    return result