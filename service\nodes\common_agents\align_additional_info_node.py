from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.align_add_info_agent import check_additional_info_assignment_agent
from service.context_pipeline import logger

def check_additional_info_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles checking if an assignment effectively incorporates additional information.

    Extracts necessary inputs from the state and calls the core agent functionality.
    Updates state.adjustment_instructions if adjustments are required.
    """
    logger.info("Starting check_additional_info_node")

    initial_assignment = state.assignment
    add_info = state.request.add_info
    grade = state.student.grade
    nb_of_questions= state.request.nb_of_questions
    assignment_type= state.request.assignment_type

    response = check_additional_info_assignment_agent(
        grade=grade,
        add_info=add_info,
        initial_assignment=initial_assignment,
        nb_of_questions=nb_of_questions,
        assignment_type=assignment_type
    )

    logger.debug(f"Additional info response: {response}")

    next_steps = response.get('next_steps', '').strip()
    if next_steps:
        # Properly update the adjustment_instructions field in state
        state.adjustment_instructions['additional_information'] = next_steps
        logger.info("Additional information adjustment instructions updated.")
    else:
        logger.warning("Adjustment indicated but no 'next_steps' provided.")

    return {"adjustment_instructions": state.adjustment_instructions}
