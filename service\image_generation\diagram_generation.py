from service.openai_utils.gpt_call import make_openai_langfuse,make_openai_langfuse_reasoning
from service.context_pipeline import logger,GRAPHICS_DICT
import json
from copy import deepcopy
import re, types, base64, io
from typing import Tuple, Dict, Any

def generate_diagram(assignment_question, decision, grade_level):
    system_prompt = """
    You are an educational content generation expert specialized explicitly in generating mathematics diagrams programmatically using Python visualization tools, especially matplotlib.

    Input (Explicitly provided):

    "assignment_question" explicitly containing:
    "scenario"
    "task"
    "title"
    "answerKey"
    "index"
    "grade_level" explicitly indicating the student's grade.
    "description" explicitly providing detailed instructions defining exactly how the diagram should appear WITHOUT explicitly revealing solutions.

    Steps (Clearly and explicitly defined):

    Carefully read and explicitly understand the provided "description".

    Clearly define a Python function named generate_diagram() explicitly:

    This function explicitly produces the required mathematical diagram according precisely to the provided "description".
    This function explicitly handles and successfully implements the necessary logic for diagram visualization. (Do NOT explicitly state diagram type separately. The diagram type is implicitly depicted directly through the clearly written Python code itself.)
    Explicitly ensure NO diagram labels, numerical values, or elements EVER explicitly reveal answers or solutions.
    Explicitly include clear in-line comments explaining the essential steps (axes configuration, labels, limits, colors, shapes, dimensions, styles, data points, etc.).
    Explicitly return the generated diagram as a base64-encoded PNG image from within the generate_diagram() Python function.
    Explicitly avoid saving locally or writing directly to disk.
    Correctly encode the diagram directly into the base64 string and explicitly return this as the function’s output.
    Explicitly validate and clearly confirm the accuracy and executable reliability of the base64-encoded PNG output.
    Expectation (Explicit required JSON structure):
    Always explicitly respond using the following JSON structure:
    {
    "python_code": "<Explicit Python function named generate_diagram() that directly generates the described diagram using matplotlib, clearly commented, robust and executable. The function explicitly returns the generated diagram as a base64-encoded PNG image string (without explicitly saving it locally)>"
    }
    🚨 Explicit Constraints and Reminders (Repeatedly emphasized):
    NEVER explicitly reveal solutions or answers through the generated diagram, explicitly ensuring labels, values, shapes, or visual clues remain strictly non-revealing.
    Explicitly confirm the generated Python function is robust, executes correctly, and explicitly returns a valid base64-encoded PNG image string.
    Explicitly include descriptive and clear inline code comments.
    Explicitly avoid saving or writing the file locally; the diagram must explicitly only be returned as a base64-encoded PNG image.
    """
    user_prompt = f"""
    Assignment question:
    {assignment_question}

    Grade level:
    {grade_level}

    Description:
    {decision['description']}

    Main Task:
    Carefully analyze the provided math assignment question, standard used, and targeted student's grade level. Determine whether including one—and only one—of these visual enhancements ("image", "table", "diagram", "none") would significantly enhance student understanding, clarify the content, or increase engagement.
    """
    tools=[
        {
            "type": "function",
            "function": {
                "name": "generate_diagram",
                "description": "Generate a diagram based on the provided description.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "python_code": {
                            "type": "string",
                            "description": "The Python code for generating the diagram as a base64-encoded PNG image string."
                        }
                    },
                    "required": ["python_code"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt}
    ]
    response = make_openai_langfuse(messages=messages,tools=tools,model="gpt-4.1-nano", temperature=0.7)
    
    logger.info(f"response: {response}")
    return response['python_code']


def _libs_for(category: str, lang: str):
    """Return list of library names for a given category & language."""
    cat_dict = GRAPHICS_DICT.get(category, {})
    return cat_dict.get(lang.lower(), [])

def python_graphic_code_generator(assignment_question: dict,
                                  visual_instructions: list,
                                  grade_level: str,
                                  visual_decision_response: dict):
    """
    Produce JSON with keys: imports, code, description.
    ─ imports      : Python import lines.
    ─ code         : A complete function draw_graphic() (no import lines inside).
    ─ description  : One-sentence summary of what the code draws.
    """

    logger.info("python_graphic_code_generator")

    category    = visual_decision_response.get("recommended_category", "NONE")
    python_libs = _libs_for(category, "python")

    sanitized_question = deepcopy(assignment_question)
    sanitized_question.pop("answerKey", None)
    visual_instructions = "\n".join(visual_instructions)

    system_prompt = f"""
    ROLE:
    You are a Senior Python Developer specializing precisely in creating educational math visual graphics for K-12 students.

    INPUT PROVIDED:
    - Math assignment question (answers removed explicitly) tailored for K-12 students.
    - Detailed instructional steps describing the visual graphic precisely required.
    - Student’s specific grade level clearly defined.

    ALLOWED PYTHON LIBRARIES for the CATEGORY "{category}":
    {python_libs}

    STEPS TO FOLLOW (STRICT ORDER):
    1. Review thoroughly the provided question scenario, displayed grade level, and visual instructions.
    2. Choose suitable and explicitly defined Python libraries from the allowed list above OR if none listed explicitly DEFAULT to using Matplotlib.
    3. In the provided JSON "imports" field, explicitly write and return all necessary Python "import <lib>" statements required clearly.
    4. Create exactly a single Python function named draw_graphic() explicitly performing the described visual instructions precisely. 
    5. Import statements must explicitly NOT be included inside the function.
    6. Function must explicitly end with a clear `return` statement.
    7. Ensure your visualization explicitly avoids computing, hinting, or revealing academic solutions or final answers to the math problem in any way.
    8. Provide a short, single clear sentence describing concisely exactly what the generated visualization achieves.

    STRICT EXPECTED JSON OUTPUT:
    {{
    "imports": "<Explicit Python import statements only>",
    "code": "<Complete Python function draw_graphic() explicitly without import lines inside and explicitly ending with a return statement>",
    "description": "<Single concise sentence clearly defining what graphic precisely depicts>"
    }}

    STRICT CONSTRAINTS:
    - NEVER compute, hint at, or inadvertently reveal mathematical answers or solutions.
    - NEVER place import statements inside your provided function block.
    - Use libraries explicitly provided above OR default explicitly to using Matplotlib if needed.
    - Output must be valid UTF-8 text.  
    - Do NOT include the NUL character (\x00 or \u0000) anywhere.  
    - If you need the degree symbol, write it as ° or as the escape \u00B0.  
    After producing the code, compile it yourself (virtually) to ensure it runs without syntax errors.
    """.strip()

    user_prompt = f"""
    Provided Grade Level:
    {grade_level}

    Assignment Question provided:
    {sanitized_question}

    Provided GRAPHIC INSTRUCTIONS:
    {visual_instructions}

    YOUR TASK:
    Strictly follow your defined Role, Inputs, clearly numbered Steps, Strict Constraints and the Expected JSON Output schema provided in the system prompt explicitly to construct the required JSON response exactly. Avoid all unnecessary commentary or explanations.
    """.strip()

    # (Variables above must be dynamically substituted within your code execution logic explicitly:)

    tools = [
        {
            "type": "function",
            "function": {
                "name": "python_graphic_code_output",
                "description": (
                    "Return the Python import statements, the full "
                    "draw_graphic() function, and a short human description."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "imports": {
                            "type": "string",
                            "description": "Required Python import lines."
                        },
                        "code": {
                            "type": "string",
                            "description": (
                                "The complete definition of draw_graphic(), "
                                "excluding import statements, ending with a "
                                "return statement."
                            )
                        },
                        "description": {
                            "type": "string",
                            "description": (
                                "One concise sentence describing what the "
                                "graphic depicts."
                            )
                        }
                    },
                    "required": ["imports", "code", "description"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user",   "content": user_prompt}
    ]

    response = make_openai_langfuse_reasoning(
        messages=messages,
        tools=tools,
        model="o3"
    )

    logger.info(f"python_graphic_code_response: {response}")
    return response


# def javascript_graphic_code_generator(assignment_question: dict,
#                                       visual_instructions: list,
#                                       grade_level: str,
#                                       visual_decision_response: dict):
#     """
#     Produce JSON with keys: imports, code, description.
#     ─ imports      : <script> tags / ES-module imports needed.
#     ─ code         : Function drawGraphic() that renders the visual.
#     ─ description  : One-sentence summary of what the code draws.
#     """

#     logger.info("javascript_graphic_code_generator")

#     category = visual_decision_response.get("recommended_category", "NONE")
#     js_libs  = _libs_for(category, "javascript")
#     visual_instructions = "\n".join(visual_instructions)
#     # strip answerKey
#     sanitized_question = deepcopy(assignment_question)
#     sanitized_question.pop("answerKey", None)

#     system_prompt = f"""
#     ROLE:
#     You are an experienced front-end JavaScript developer specializing specifically in interactive educational math visualizations.

#     INPUT:
#     - You will receive a math assignment question (without answerKey) relevant to K-12 students.
#     - Detailed visual instructions clearly describing the required graphic visual.
#     - Grade level to tailor appropriateness of visual complexity.

#     ALLOWED JS LIBRARIES FOR CATEGORY "{category}":
#     {js_libs}

#     STEPS TO FOLLOW:
#     1. Review the math assignment question, identified grade level, and provided visual instructions thoroughly.
#     2. Select and explicitly include all necessary JS libraries from the ALLOWED JS LIBRARIES listed above.
#     3. Provide corresponding CDN <script> tags or ES-module import statements strictly within the "imports" output field.
#     4. Write a single, executable JavaScript function named specifically drawGraphic(). This function must render a visual precisely aligned with provided instructions, suitable for the student’s grade level.
#     5. Ensure your visualization helps visual understanding clearly WITHOUT calculating, hinting, or revealing the math problem’s final numeric or algebraic solution.
#     6. Provide a concise, clear one-sentence description in the ‘description’ field summarizing the visual being constructed.

#     EXPECTED JSON OUTPUT FORMAT:
#     {{
#     "imports": "<script/CDN tags or ES module import statements only>",
#     "code": "<Full JavaScript function drawGraphic() ONLY>",
#     "description": "<Single sentence clearly stating what the graphic visually depicts>"
#     }}

#     STRICT CONSTRAINTS:
#     - Do NOT compute, hint, or reveal answers or detailed solutions in your visuals.
#     - Imports must strictly use allowed libraries or JavaScript vanilla (Canvas/SVG) if no libraries available.
#     - Provided code snippet must be clear, executable without external dependencies other than explicitly defined imports.
#     """.strip()

#     user_prompt = f"""
#     Grade Level Provided:
#     {grade_level}

#     Sanitized Assignment Question (no answers included):
#     {sanitized_question}

#     GRAPHIC INSTRUCTIONS:
#     {visual_instructions}

#     YOUR TASK:
#     Strictly follow your role, input details, clearly outlined steps, and explicit output constraints from the system prompt above to produce your structured JSON response exactly as specified. No additional commentary or deviations allowed.
#     """.strip()


#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "javascript_graphic_code_output",
#                 "description": (
#                     "Return script/import tags, the full drawGraphic() "
#                     "function, and a short description of the rendered graphic."
#                 ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "imports": {
#                             "type": "string",
#                             "description": (
#                                 "HTML <script> tags or ES-module import lines "
#                                 "required to load the chosen JS library."
#                             )
#                         },
#                         "code": {
#                             "type": "string",
#                             "description": (
#                                 "The complete drawGraphic() function or "
#                                 "HTML+JS snippet that renders the visual."
#                             )
#                         },
#                         "description": {
#                             "type": "string",
#                             "description": (
#                                 "One concise sentence describing what the "
#                                 "graphic depicts."
#                             )
#                         }
#                     },
#                     "required": ["imports", "code", "description"]
#                 }
#             }
#         }
#     ]

#     messages = [
#         {"role": "system", "content": system_prompt},
#         {"role": "user",   "content": user_prompt}
#     ]

#     response = make_openai_langfuse(
#         messages=messages,
#         tools=tools,
#         model="gpt-4.1"",
#         temperature=0.25
#     )

#     logger.info(f"javascript_graphic_code_response: {response}")
#     return response

def python_graphic_error_fixer(assignment_question: dict,
                               visual_instructions: list,
                               grade_level: str,
                               visual_decision_response: dict,
                               previous_python_code: dict,
                               execution_error: str):
    """
    Returns corrected imports & code plus a description of concrete fixes.
    """

    clean_q = deepcopy(assignment_question)
    clean_q.pop("answerKey", None)

    system_prompt = f"""
    ROLE:
    You are a Senior Python Debugger specializing explicitly in fixing and optimizing Python code used to create educational graphics for math assignments.

    INPUT PROVIDED:
    - Python code (`draw_graphic` function) previously generated but which failed explicitly during execution.
    - The exact Python error traceback encountered.
    - Detailed graphic instruction steps it intended to implement.
    - The sanitized original math assignment (explicitly without answerKey).
    - Target student’s explicit Grade Level.

    STEPS TO FOLLOW (STRICT ORDER):
    1. Analyze explicitly the provided Python code line-by-line against the explicit error traceback clearly provided.
    2. Clearly determine the cause(s) of the explicit execution error.
    3. Apply precise targeted fixes explicitly to the provided Python import statements and the provided Python code, fully addressing the execution error.
    4. Clearly ensure that your fixed Python function still aligns explicitly to the original visual instructions without computing or hinting any solving clues or answers.
    5. Clearly summarize applied fixes succinctly (maximum FIVE bullet points) clearly in the `description` output field.

    STRICT JSON OUTPUT FORMAT REQUIRED (NO EXTRA KEYS OR COMMENTARY):
    {
    "imports": "<Corrected, explicit Python import statements>",
    "code": "<Explicitly corrected & complete Python draw_graphic() function without internal import statements>",
    "description": "<Exactly five or fewer succinct bullet points clearly outlining explicitly each fix applied>"
    }

    STRICT CONSTRAINTS TO FOLLOW & MAINTAIN EXPLICITLY:
    - Maintain original objective strictly without inadvertently computing, showing, or hinting at mathematical answers.
    - Avoid explicitly adding import lines within the corrected draw_graphic() function code block.
    - Provide clearly formatted and executable corrected Python code strictly adhering to provided instructions.
    - Output must be valid UTF-8 text.  
    - Do NOT include the NUL character (\x00 or \u0000) anywhere.  
    - If you need the degree symbol, write it as ° or as the escape \u00B0.  
""".strip()


    user_prompt = f"""
    Provided Grade Level:
    {grade_level}

    Original Math Assignment (no answers explicitly included):
    {assignment_question.get('task', '')}
    {assignment_question.get('scenario', '')}

    PROVIDED GRAPHIC INSTRUCTIONS:
    {visual_instructions}

    PREVIOUS PROVIDED FAULTY PYTHON CODE (Imports & function code):
    {previous_python_code}

    EXECUTION ERROR MESSAGE EXPLICITLY PROVIDED:
    {execution_error}

    YOUR TASK:
    Strictly follow your explicitly stated Role, provided Inputs, Steps, Constraints and the strictly defined Expected JSON Output from the system prompt above to explicitly construct and return your JSON response exactly as instructed. Avoid any extra comments or explanations in your response.
    """.strip()

    tools = [
        {
            "type": "function",
            "function": {
                "name": "python_error_fix_output",
                "description": (
                    "Return corrected imports, corrected executable code, "
                    "and a description of the fixes."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "imports": {
                            "type": "string",
                            "description": "Correct Python import lines."
                        },
                        "code": {
                            "type": "string",
                            "description": "Fully repaired executable Python code."
                        },
                        "description": {
                            "type": "string",
                            "description": "Bullet list summarising the fixes."
                        }
                    },
                    "required": ["imports", "code", "description"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user",   "content": user_prompt}
    ]

    response = make_openai_langfuse_reasoning(
        messages=messages,
        tools=tools,
        model="o3"
    )

    logger.info(f"python_graphic_error_fix_response: {response}")
    return response


def python_graphic_alignment_checker(assignment_question: dict,
                                     visual_instructions: list,
                                     grade_level: str,
                                     visual_decision_response: dict,
                                     current_python_code: dict):
    """
    Verifies that the graphic code satisfies all visual instructions and
    assignment context; returns possibly improved code plus reasoning when the
    code is insufficient.
    """

    clean_q = deepcopy(assignment_question)
    clean_q.pop("answerKey", None)

    system_prompt = """
    ROLE:
    You are a meticulous Python graphical code reviewer specializing precisely in validating educational math graphic implementations for alignment with clear visual instructions.

    EXPLICITLY PROVIDED INPUT:
    - Original math assignment context clearly outlined, explicitly without solutions.
    - Explicitly stated visual graphic instructions that must be stringently followed.
    - Explicitly provided Python implementation (imports and draw_graphic() function included explicitly).
    - Explicitly identified student’s Grade Level.

    STEPS TO FOLLOW STRICTLY in EXACT ORDER:
    1. Thoroughly analyze and explicitly compare provided Python graphics code with every discrete visual instruction provided (labels, axes, colors, layout, spacing and numeric or textual data directly mentioned or implied explicitly).
    2. Explicitly identify and document mismatches or quality issues explicitly and concisely.
    3. Decide clearly if the provided Python code FULLY meets all visual graphic instructions explicitly. If met fully, set `code_matches_instructions` = true.
    - Clearly state reasoning very concisely: "All instructions satisfied."
    4. If ANY instruction, detail, or visual quality explicitly missing or unclear set `code_matches_instructions` = false.
    - Clearly and succinctly document specific discrepancies as reasoning.
    - Explicitly provide adjusted (improved or corrected) executable Python code explicitly addressing clearly identified discrepancies.
    - Clearly enumerate concise descriptive fixes explicitly used in bullet form (≤5 bullets clearly).

    STRICT OUTPUT JSON SCHEMA MANDATORY (NO EXTRA KEYS OR COMMENTARY):
    {
    "code_matches_instructions": <true | false>,
    "adjusted_code": {
        "imports": "<Correct & explicit Python import statements>",
        "code": "<Explicitly adjusted complete & executable Python draw_graphic() function explicitly excluding internal import lines>",
        "description": "<Concise explicit bullet-style fixes summary clearly (<=5 bullets)>"
    },
    "reasoning": "<If true explicitly affirm; if false, explicitly list clear discrepancies>"
    }

    FOLLOW ITERATIVELY THESE STRICT EXPLICIT CONSTRAINTS IDENTICALLY:
    - NEVER reveal, compute, or indirectly imply math solutions explicitly.
    - Explicitly NEVER include import statements inside the Python draw_graphic() function code provided.
    - Provide clear, accurately executable code strictly adhering explicitly to provided visual instructions.
    """.strip()

    user_prompt = f"""
    Explicitly Provided Student Grade Level:
    {grade_level}

    Original Assignment Task (no answers explicitly included):
    {assignment_question.get('task', '')}
    {assignment_question.get('scenario', '')}

    Explicitly Provided GRAPHIC INSTRUCTIONS:
    {visual_instructions}

    CURRENT PROVIDED PYTHON GRAPHIC CODE (imports and function explicitly provided):
    {current_python_code}

    YOUR TASK:
    Strictly follow your explicitly stated Role, clearly identified Inputs, numbered Steps, explicit constraints, and explicit Expected JSON structure in the system prompt explicitly. Return solely the strictly defined JSON format required explicitly and exactly. Do not add extra comments or deviations.
    """.strip()

    tools = [
        {
            "type": "function",
            "function": {
                "name": "python_alignment_check_output",
                "description": (
                    "State whether the code matches the instructions, provide "
                    "reasoning, and supply the final version of the code."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "code_matches_instructions": {
                            "type": "boolean",
                            "description": "True if code fully aligns; false otherwise."
                        },
                        "adjusted_code": {
                            "type": "object",
                            "properties": {
                                "imports": {
                                    "type": "string",
                                    "description": "Correct Python import lines."
                                },
                                "code": {
                                    "type": "string",
                                    "description": "Fully repaired executable Python code."
                                },
                                "description": {
                                    "type": "string",
                                    "description": "Bullet list summarising the fixes."
                                }
                            },
                        "required": ["imports", "code", "description"]
                        },
                        "reasoning": {
                            "type": "string",
                            "description": (
                                "If true: affirm all instructions met. "
                                "If false: list the specific discrepancies."
                            )
                        }
                    },
                    "required": ["code_matches_instructions",
                                 "adjusted_code",
                                 "reasoning"]
                }
            }
        }
    ]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user",   "content": user_prompt}
    ]

    response = make_openai_langfuse_reasoning(
        messages=messages,
        tools=tools,
        model="o3"
    )
    logger.info(f"python_graphic_alignment_check_response: {response}")
        
    if type(response['code_matches_instructions'])==bool and response['code_matches_instructions']==True:
        return current_python_code
    elif type(response['code_matches_instructions'])==str and response['code_matches_instructions'].lower=='true':
        return current_python_code
    else:
        return response['adjusted_code']




def run_python_and_get_png(py_block: Dict[str, str],
                           size: Tuple[int, int] = (800, 600)) -> str:
    """
    Execute a python code-block that draws a Matplotlib figure and return a
    base64-encoded PNG of that figure.

    Parameters
    ----------
    py_block : dict
        {
          'imports':  <str>  # import lines (optional)
          'code'   :  <str>  # code that defines *one* function we should call
        }
    size : (int, int), optional
        Default figure size in pixels (only used if the code doesn’t set its
        own).  Matplotlib works in inches, so we convert using 100 dpi.

    Returns
    -------
    str
        Base-64 encoded PNG string (e.g. ready for `<img src="data:image/png;base64,...">`)
    """
    logger.info(f"run_python_and_get_png:{py_block}")
    import matplotlib
    matplotlib.use("Agg")                 
    import matplotlib.pyplot as plt

    plt.figure(figsize=(size[0]/100, size[1]/100), dpi=100)

    namespace: Dict[str, Any] = {}
    if py_block.get("imports"):
        exec(py_block["imports"], namespace, namespace)

    exec(py_block["code"], namespace, namespace)

    m = re.search(r"def\s+([A-Za-z_]\w*)\s*\(", py_block["code"])
    if not m:
        raise RuntimeError("No function definition found in code block.")

    func_name = m.group(1)
    f = namespace.get(func_name)
    if not isinstance(f, types.FunctionType):
        raise RuntimeError(f"Function '{func_name}' not found after exec().")

    print(f"→ running {func_name}() ...")
    maybe_fig = f()                     

    if maybe_fig is None:
        fig = plt.gcf()
    else:
        fig = maybe_fig

    buf = io.BytesIO()
    fig.savefig(buf, format="png", bbox_inches="tight")
    plt.close(fig)                        
    buf.seek(0)

    b64_png = base64.b64encode(buf.read()).decode("ascii")
    logger.info(f"b64_png image generated: {b64_png}")
    return b64_png

# test={'imports': 'import matplotlib.pyplot as plt\nimport numpy as np', 'code': 'def draw_graphic():\n    # Create the figure and axis\n    fig, ax = plt.subplots(figsize=(10, 2))\n\n    # Define the time range from 0 to 15 minutes\n    minutes = np.arange(0, 16)\n    ax.set_xlim(-0.5, 15.5)\n    ax.set_ylim(-1, 1)\n\n    # Draw the horizontal number line\n    ax.hlines(0, 0, 15, color=\'black\')\n\n    # Plot tick marks and labels for each minute\n    for m in minutes:\n        ax.vlines(m, -0.1, 0.1, color=\'black\')\n        time_label = f"3:{m:02d}"\n        ax.text(m, -0.25, time_label, ha=\'center\', va=\'top\', fontsize=8)\n\n    # Mark the starting time at 3:05 PM\n    start_minute = 5\n    ax.plot(start_minute, 0.15, marker=\'o\', color=\'blue\', markersize=8)\n    ax.text(start_minute, 0.3, \'Start: 3:05 PM\', ha=\'center\', va=\'bottom\', color=\'blue\', fontsize=9)\n\n    # Draw a red arrow representing 7 minutes of travel\n    arrow_length = 7\n    ax.annotate(\'\', xy=(start_minute + arrow_length, 0.15), xytext=(start_minute, 0.15),\n                arrowprops=dict(arrowstyle=\'->\', color=\'red\', linewidth=2))\n\n    # Mark the stop time and label it\n    stop_minute = start_minute + arrow_length\n    ax.plot(stop_minute, 0.15, marker=\'o\', color=\'green\', markersize=8)\n    ax.text(stop_minute, 0.3, \'Stop Time\', ha=\'center\', va=\'bottom\', color=\'green\', fontsize=9)\n\n    # Add title and remove axes for clarity\n    ax.set_title(\'Rover Driving Time\', fontsize=12, pad=20)\n    ax.axis(\'off\')\n\n    plt.tight_layout()\n    return', 'description': ''}
# b64_string=run_python_and_get_png(test)

# # If the string has a data URL header, remove it
# if b64_string.startswith('data:image'):
#     b64_string = b64_string.split(',')[1]

# # Decode base64 to binary
# image_data = base64.b64decode(b64_string)

# # Write to a PNG file
# with open("output.png", "wb") as f:
#     f.write(image_data)

# print("Image saved as output.png")