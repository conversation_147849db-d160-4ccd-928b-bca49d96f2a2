from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.translate_agent import translate_assignment_agent
from service.context_pipeline import logger

def translate_assignment_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles translation of assignments to different languages.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting translate_assignment_node")

    # Extract required inputs from state
    json_response = state.assignment
    tools = state.tools
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type
    ai_passage = state.ai_passage
    language = getattr(state.request, 'language', 'english')
    if 'passage' in ai_passage:
        ai_passage=ai_passage['passage']
    try:
        content,passage = translate_assignment_agent(
            json_response=json_response,
            nb=nb,
            assignment_type=assignment_type,
            tools=tools,
            language=language,
            ai_passage=ai_passage
        )
        if passage:
            logger.info(f"Updated passage after translation: {passage}")
            state.ai_passage=passage
        state.assignment = content
        state.adjustments_done["translated"] = True
        logger.info(f"Completed translate_assignment_node successfully to {language}")
    except Exception as e:
        logger.error(f"Error in translate_assignment_node: {e}")
        state.assignment = json_response
        state.adjustments_done["translated"] = False 

    return state
