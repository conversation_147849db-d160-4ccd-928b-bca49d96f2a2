from service.gen_data_models import GeneratePipelineContext
from service.common_agents.understand_standard_agent import understand_standard_agent
from service.context_pipeline import logger

def understand_standard_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting understand_standard_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    cluster_statement=state.request.standard.get('cluster_statement','')
    common_core_standard=state.request.standard.get('common_core_standard','')
    grade=state.student.grade
    subject=state.request.standard.get('subject','')
    topic=state.request.standard.get('topic','')
    standard_code=state.request.standard.get('standard_code','')
    try:
        response = understand_standard_agent(
            cluster_statement=cluster_statement,
            common_core_standard=common_core_standard,
            grade=grade,
            subject=subject,
            topic=topic,
            standard_code=standard_code
        )

        logger.info(f"understand_standard response: {response}")
        adjustment_instructions['understand_standard'] = response
    except Exception as e:
        logger.error(f"Error in understand_standard_node: {e}")

    return {"adjustment_instructions": adjustment_instructions}