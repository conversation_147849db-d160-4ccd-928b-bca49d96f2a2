from service.openai_utils.gpt_call import make_openai_langfuse
import json
import copy
from concurrent.futures import ThreadPoolExecutor, as_completed
from service.context_pipeline import langfuse
from service.reading_level.assess_reading_level_agent import extract_and_convert_grade,kincaid_grade_level_test_english
from service.context_pipeline import logger, aisdk_object
from service.common_functions import vocab_examples,reading_level_example
import ast

def get_guideline_by_level(level,questions=False,standard=False):
    """
    This function retrieves the guideline for a given educational level. The guidelines include a description, examples, 
    and keywords tailored to different grade ranges. The function returns the corresponding guideline based on the 
    specified level.

    Parameters:
    - level (str): The educational level for which the guideline is needed (e.g., '1', '2', '9').

    Returns:
    - dict or None: The guideline containing description, examples, and keywords if the level is found; otherwise, returns None.
    """
    if standard:
        guidelines= {
        ('', 'kindergarten'): {
            "questions": "Very simple, direct questions requiring 1-step answers. Focus on concrete objects/actions from text. Use basic question words (who/what/where). Avoid abstract concepts. Example: 'What color is the apple?' or 'Where is the cat?'"
        },
        ('1', '2'): {
            "questions": "Simple direct questions requiring 1-step answers. Focus on concrete objects/actions from text. Use basic question words (who/what/where) with visual supports. Avoid abstract concepts. Example: 'How many apples did Emma count?' or 'Where did the puppy chase balls?'"
        },
        ('3', '4'): {
            "questions": "Two-part questions using 'and/but' connectors. Focus on sequence/compare elements. Include basic inference questions. Example: 'What did archaeologists find AND how did they document it?' or 'Why did farmers want rain BUT others might worry?'"
        },
        ('5', '6'): {
            "questions": "Questions requiring cause-effect explanations using 'because/while'. Include technical term definitions. Ask for process descriptions. Example: 'WHY do students wear goggles during experiments?' or 'HOW do elevation changes affect map updates?'"
        },
        ('7', '8'): {
            "questions": "Analytical questions using 'however/therefore'. Ask for comparisons between abstract concepts. Include ethical implications. Example: 'How do biometric systems ENHANCE security HOWEVER CREATE privacy issues?' or 'WHY might blockchain evolution CONTINUE DESPITE market fluctuations?'"
        },
        ('9', '10'): {
            "questions": "Critical evaluation questions with conditional phrasing. Require synthesis of technical concepts. Example: 'UNDER WHAT CONDITIONS might biomimetic architecture OPTIMIZE energy efficiency?' or 'HOW do quantum physics frameworks CHALLENGE traditional causality models?'"
        },
        ('11', '12'): {
            "questions": "Multi-layered questions with implicit subparts. Require meta-analysis of specialized concepts. Example: 'ANALYZE how quantum decoherence theory's ontological implications both CHALLENGE Cartesian dualism AND INFORM contemporary neuroscience models through non-local entanglement phenomena.' Follow-up: 'HOW might this impact philosophical approaches to consciousness?'"
        }
    }

    
    else:
        guidelines = {
    ('','kindergarten') : {
        "description": "Introduce basic concepts with very simple sentences. Focus on building foundational skills like counting, identifying shapes, and recognizing letters.",
        "examples": [
            "Aidan is happy. How many apples does he have?",
            "Sara sees a red ball. What shape is it?",
            "Tom has 3 pencils. How many more does he need to make 5?",
            "Jenny likes the colour yellow"
        ],
        "keywords": ["basic", "simple", "everyday"]
    }, 
    ('1', '2'): {
        "description": "Use short, predictable sentences (8-10 words) with 1 main clause. Limit to 1-2 common nouns/verbs per sentence. Avoid complex adjectives. Maintain simple subject-verb-object structure.",
        "examples": [
            "The energetic puppy chases seven bouncing tennis balls around the grassy backyard.",
            "Emma carefully counts twelve shiny red apples in her grandmother's woven basket.", 
            "Morning sunlight filters through six classroom windows, creating patterns on the tiled floor."  
        ],
        "questions":"Use very simple, direct vocabulary in your questions. Keep them short and clear. ",
        "keywords": ["short sentences", "single clause"]
    },
    ('3', '4'): {
        "description": "Compound sentences (10-13 words) with 2 clauses using 'and/but'. Include 2-3 concrete nouns/action verbs. Use basic descriptive adjectives. Introduce simple prepositional phrases.",
        "examples": [
            "Archaeologists discovered ancient pottery fragments, and they carefully documented each intricate design.",  # 13 words
            "Meteorologists predict heavy rainfall, but farmers welcome it for their thirsty crops.",  # 13 words
            "Urban planners propose new bike lanes, while considering existing traffic flow patterns."  # 13 words
        ],
        "questions": "Formulate slightly longer questions with simple connecting words. Use everyday terms but add a bit more detail. ",
        "keywords": ["compound sentences", "basic adjectives"]
    },
    ('5', '6'): {
        "description": "Multi-clause sentences (13-16 words) with 2-3 clauses. Use 'because/while' conjunctions. Include 3-4 specific nouns/verbs with descriptive adjectives. Introduce technical terms with context.",
        "examples": [
            "While conducting pH experiments, students wear protective goggles because chemical reactions can be unpredictable.",  # 16 words
            "Cartographers update topographic maps whenever geological surveys reveal new elevation measurements.",  # 12 words → expanded
            "Although renewable energy costs decrease annually, implementation requires substantial infrastructure investments."  # 14 words
        ],
        "questions": "Encourage brief explanations or reasons using 'why' or 'because.' Introduce a bit of complexity or context. ",
        "keywords": ["multi-clause", "contextual terms"]
    },
    ('7', '8'): {
        "description": "Complex structures (16-19 words) with 3-4 clauses. Use 'however/therefore' transitions. Include 4-5 abstract nouns/verbs with precise adjectives. Introduce metaphorical language and nominalizations.",
        "examples": [
            "Biometric authentication systems, while enhancing security, raise ethical concerns regarding personal privacy and data protection.",  # 17 words
            "Cryptocurrency fluctuations demonstrate market volatility, yet blockchain technology continues evolving financial systems.",  # 14 words → expanded
            "Neuroscientific research reveals neural plasticity mechanisms that enable cognitive adaptation through deliberate practice."  # 16 words
        ],
        "questions": "Pose analytical questions using more precise or abstract vocabulary, and consider the broader impact. ",
        "keywords": ["transitions", "abstract concepts"]
    },
    ('9', '10'): {
        "description": "Sophisticated sentences (20-22 words) with embedded clauses. Use correlative conjunctions. Include 5-6 technical nouns/verbs with nuanced adjectives. Employ passive voice and conditional structures.",
        "examples":  [
            "Epistemological frameworks in quantum physics challenge classical notions of causality through probabilistic interpretations of subatomic interactions.",  # 18 words → expanded
            "Postcolonial literary critiques deconstruct Eurocentric narratives while amplifying marginalized voices through intersectional analysis of power dynamics.",  # 20 words
            "Sustainable architecture integrates biomimetic principles that emulate ecological systems to optimize energy efficiency in urban structures."  # 20 words
        ],
        "questions": "Construct inquiries that highlight conditional or nuanced language, focusing on how concepts interact. ",
        "keywords": ["embedded clauses", "conditional structures"]
    },
    ('11', '12'): {
        "description": "Dense academic prose (22+ words) with multiple embedded clauses. Use advanced conjunction combinations. Include 7-8 specialized nouns/verbs with domain-specific adjectives. High lexical density with nominalizations.",
        "examples": [
            "The ontological implications of quantum decoherence theory fundamentally destabilize classical Cartesian dualism through non-local entanglement phenomena observed in controlled laboratory conditions.",  # 24 words
            "Hermeneutic phenomenology in continental philosophy necessitates rigorous examination of lived experience structures while bracketing presuppositions through methodological epochē.",  # 22 words
            "Neuroplasticity research demonstrates cortical reorganization capabilities wherein repetitive cognitive stimuli induce dendritic arborization across associational neocortical regions."  # 20 words → expanded
        ],
        "questions": "Invite deep, multi-layered exploration using sophisticated vocabulary and embedded clauses. Require meta-analysis of specialized concepts ",
        "keywords": ["lexical density", "specialized language"]
    }
}
    for key_range in guidelines:
        if level in key_range:
            guideline = guidelines[key_range].copy()
            if not questions:
                if 'questions' in guideline:
                    guideline.pop("questions", None)
            return guideline


def reading_prompt_to_use(reading_level, kincaid_grade_level):
    """
    Determines whether to increase or reduce the text complexity of an assignment based on the student's reading level and the 
    Kincaid grade level. If the difference between the levels is significant, a prompt for adjusting the text complexity is returned.

    Parameters:
    - reading_level (int): The student's reading level.
    - kincaid_grade_level (int): The Kincaid grade level of the assignment text.

    Returns:
    - str: A prompt to either increase or reduce text complexity, or an empty string if no adjustment is needed.
    """
    
    increase_text_complexity = """
    To enhance the complexity of the text, consider the following adjustments:
    1. **Expand Vocabulary**: Incorporate more sophisticated and subject-specific vocabulary. Avoid simple words where a more complex synonym can add depth without confusing the context.
    2. **Lengthen Sentences**: Gradually increase the sentence length by using more compound and complex sentences. Incorporate conjunctions, relative clauses, and adverbial phrases to develop intricate sentence structures.
    """

    reduce_text_complexity = """
    To simplify the text, consider implementing these strategies:
    1. **Shorten Sentences**: Break long sentences into shorter, more digestible ones. Focus on clarity and brevity, avoiding unnecessary subordinate clauses and complex constructions.
    2. **Simplify Vocabulary**: Use simpler, more common words suitable for the student's age group. Replace complex words or jargon with straightforward alternatives.
    """
    level_difference = reading_level - kincaid_grade_level
    if abs(level_difference) <= 1:
        return ""
    elif level_difference > 1:
        return increase_text_complexity
    else:
        return reduce_text_complexity

def calculate_average_reading_level(levels):
    return sum(levels) / len(levels) if levels else 0

def extract_text_for_reading_level(json_data):
    relevant_texts = []

    if isinstance(json_data, str):
        logger.info("Content is a string, adding directly.")
        relevant_texts.append(json_data)
        return relevant_texts

    homework = json_data.get('data', {}).get('homework', json_data)

    for key in ['passage', 'conclusion', 'introduction', 'reflection', 'title']:
        if key in homework:
            if isinstance(homework[key], str):
                relevant_texts.append(homework[key])
            elif isinstance(homework[key], list):
                relevant_texts.extend([x for x in homework[key] if isinstance(x, str)])

    instructions = homework.get('instructions', [])
    relevant_texts.extend(instructions)

    challenges = homework.get('challenges', [])
    for challenge in challenges:
        if 'task' in challenge:
            relevant_texts.append(challenge['task'])
        if 'task_skills' in challenge:
            relevant_texts.append(challenge['task_skills'])
        if 'description' in challenge:
            relevant_texts.append(challenge['description'])
        if 'scenario' in challenge:
            relevant_texts.append(challenge['scenario'])
        if 'title' in challenge:
            relevant_texts.append(challenge['title'])

    return relevant_texts

def evaluate_reading_level(content, student_reading_level):

    texts_to_evaluate = extract_text_for_reading_level(content)
    logger.info(f"texts_to_evaluate: {texts_to_evaluate}")

    if not texts_to_evaluate:
        logger.warning("No texts to evaluate. Defaulting to 'Pass'.")
        return "Pass"
    
    reading_levels = [kincaid_grade_level_test_english(text) for text in texts_to_evaluate]

    avg_reading_level = calculate_average_reading_level(reading_levels)
    student_level_numeric = extract_and_convert_grade(student_reading_level)

    logger.info(f"Average Reading Level: {avg_reading_level}")
    logger.info(f"Student Reading Level: {student_level_numeric}")

    level_difference = student_level_numeric - avg_reading_level
    logger.info(f"Level Difference: {level_difference}")

    if abs(level_difference) <= 1:
        return "Pass"
    else:
        return "Fail"
    
def align_reading_level_agent(content, reading_level, nb_of_questions, extra_prompt, 
                             assignment_type, tools, passage_based, ai_passage=None):
    """
    Core agent functionality for aligning content to match a specified reading level.
    
    Adjusts text complexity, vocabulary, and sentence structure while maintaining
    the original subject matter and task difficulty.
    
    Args:
        content (dict or str): The passage or assignment to align
        reading_level (str): The target reading level (e.g., 'Grade 5')
        nb_of_questions (int): The number of required problems in the assignment
        extra_prompt (str): Additional instructions for reading level adjustments
        assignment_type (str): Type of assignment (e.g., 'vocab_fill', 'civics_fact')
        tools (dict): Tools required for processing the OpenAI request
        passage_based (str): Whether processing a passage or assignment
        ai_passage (dict, optional): The passage data if available
        
    Returns:
        dict or tuple: Aligned passage or assignment content
    """
    reading_level_example_text = reading_level_example(reading_level)
    guidelines_to_use = get_guideline_by_level(str(reading_level))
    logger.info(f"Extra prompt: {extra_prompt}")
    list_of_words=vocab_examples(reading_level)

    if passage_based == "processing_passage":
        logger.info("ALIGN READING LEVEL PASSAGE")
        messages = langfuse.get_prompt('reading_level_agent_for_passage', type='chat', label="latest")
        logger.info(f"questions of the assignment:{content}")

        complete_chat_prompt = messages.compile(
            json_response=ai_passage,
            reading_level=reading_level,
            guidelines_to_use=guidelines_to_use,
            extra_prompt=extra_prompt,
            list_of_words=list_of_words,
            reading_level_example_text=reading_level_example_text,
            questions=content
        )

        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        
        aligned_passage = make_openai_langfuse(complete_chat_prompt, tools=tools, temperature=temperature, model=model, max_tokens=max_tokens)
        return aligned_passage

    else:
        logger.info("ALIGN READING LEVEL ASSIGNMENT")
        if not isinstance(content, dict):
            try:
                content = json.loads(content)
            except:
                logger.warning("Failed to convert content to dictionary using json.loads, trying ast.literal_eval")
                try:
                    content=ast.literal_eval(content)
                except Exception as e:
                    logger.error(f"Failed to convert content to dictionary using ast.literal_eval: {e}")
                    #return "assignment", {"error": "Invalid JSON format provided and could not be converted to a dictionary."}

        # Extract homework block
        if "homework" in content:
            homework = content["homework"]
        else:
            homework = content  

        if "challenges" in homework and isinstance(homework["challenges"], list):
            temp_tool = filter_homework_challenges(tools)
            logger.info(f'temp_tools:{temp_tool}')
            guidelines_to_use = get_guideline_by_level(str(reading_level), questions=True)

            if type(homework["challenges"])==list:
                revised_challenges = [None] * len(homework["challenges"])
            else:
                revised_challenges = [None]

            # Define function to refine a single challenge
            def refine_single_challenge(index, challenge_data):
                additional_instructions = ""
                
                if assignment_type == "vocab_fill":
                    additional_instructions += (
                        " Ensure that none of the vocabulary words used in the questions "
                        "are altered or replaced under any circumstances."
                    )
                elif assignment_type in ["civics_critical", "civics_fact", "history_fact", "history_critical"]:
                    additional_instructions += (
                        " Mandatory: Under no circumstances should the adjusted passage for any question "
                        "be less than 80-100 words. This is non-negotiable."
                    )
                elif assignment_type in ["civics_vocab", "history_vocab"]:
                    additional_instructions += (
                        " Important: While adjusting the assignment, ensure that the adjusted background "
                        "is no less than 80-100 words long."
                    )

                additional_instructions += (
                    " Your task is to adjust the text to match the reading level of the student "
                    "but do not change the storyline or meaning of the text."
                )

                messages = langfuse.get_prompt('reading_level_agent_for_questions', type='chat', label="latest")
                complete_chat_prompt = messages.compile(
                    reading_level=reading_level,
                    guidelines_to_use=guidelines_to_use,
                    challenge_data=challenge_data,
                    extra_prompt=extra_prompt,
                    additional_instructions=additional_instructions,
                    list_of_words=list_of_words,
                    reading_level_example_text=reading_level_example_text
                )

                temperature = messages.config.get("openai", {}).get("temperature",0.5)
                model = messages.config.get("openai", {}).get("model","gpt-4.1")
                max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
                
                content = make_openai_langfuse(complete_chat_prompt, tools=temp_tool, temperature=temperature, max_tokens=max_tokens, model=model)
                
                return index, content

            with ThreadPoolExecutor() as executor:
                challenges=homework["challenges"]
                if type(challenges)==dict:
                    challenges=[challenges]
                futures = []
                for i, challenge_data in enumerate(challenges):
                    futures.append(executor.submit(refine_single_challenge, i, challenge_data))

                for future in as_completed(futures):
                    index, revised_challenge = future.result()
                    
                    
                    revised_challenges[index] = revised_challenge

            homework["challenges"] = revised_challenges
            return homework
        else:
            messages = langfuse.get_prompt('reading_level_agent_for_questions', type='chat', label="latest")
            complete_chat_prompt = messages.compile(
                reading_level=reading_level,
                guidelines_to_use=guidelines_to_use,
                challenge_data=content,
                extra_prompt=extra_prompt,
                additional_instructions="",
                list_of_words=list_of_words,
                reading_level_example_text=reading_level_example_text
            )

            temperature = messages.config.get("openai", {}).get("temperature",0.5)
            model = messages.config.get("openai", {}).get("model","gpt-4.1")
            max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
            
            aligned_assignment = make_openai_langfuse(complete_chat_prompt, tools=tools, temperature=temperature, model=model, max_tokens=max_tokens)
            return aligned_assignment

def filter_homework_challenges(tools):
    """
    Return a new list that contains one entry per “function” tool, where the
    `parameters.properties` subtree is replaced with the inner properties of
    `homework.challenges` (either the object itself or the array-item schema).

    The input list is left unchanged.  Tools that don’t match the expected
    structure are skipped.
    """
    result = []

    for tool in tools:
        if tool.get("type") != "function":
            continue

        fn = tool.get("function")
        if not fn:
            continue

        params     = fn.get("parameters", {})
        root_props = params.get("properties", {})
        homework   = root_props.get("homework", {})
        hw_props   = homework.get("properties", {})
        challenges = hw_props.get("challenges")

        if not isinstance(challenges, dict):
            continue

        if challenges.get("type") == "array" and "items" in challenges:
            challenge_props = challenges["items"].get("properties")
        else:
            challenge_props = challenges.get("properties")

        if not isinstance(challenge_props, dict):
            continue

        new_tool = {
            "type": "function",
            "function": {
                **{k: v for k, v in fn.items() if k != "parameters"},
                "parameters": {
                    "type": "object",
                    "properties": copy.deepcopy(challenge_props),
                },
            },
        }
        result.append(new_tool)

    return result
