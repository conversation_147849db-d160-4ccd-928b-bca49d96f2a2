from service.reading_level.reading_level_agent import align_reading_level_agent, reading_prompt_to_use, evaluate_reading_level
from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.reading_level.assess_reading_level_agent import extract_and_convert_grade,kincaid_grade_level_test_english

def check_reading_level_node(state: GeneratePipelineContext):
    """
    Node that checks if a passage or assignment meets the required reading level.
    
    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info(f"check_reading_level_node state ID: {id(state)}")
    logger.info(state.attempts)
    
    if state.attempts >= 3: 
        logger.info(f"Max reading level attempts ({3}) reached. Proceeding anyway.")
        return "Pass"
        
    if state.assignment_state == "processing_passage":
        content = state.ai_passage
    else:
        content = state.assignment
        
    reading_level = state.student.reading_level
    
    try:
        logger.info("Checking reading level in check_reading_level_node...")
        result = evaluate_reading_level(content, reading_level)

        logger.info(f"check_reading_level_node completed with result: {result}")
    except Exception as e:
        logger.error(f"Error in check_reading_level_node: {e}")
        result = "Pass"  
    
    return result

def align_reading_level_node(state: GeneratePipelineContext):
    """
    Node that handles aligning content to match a specified reading level.
    
    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info(f"align_reading_level_node state ID: {id(state)}")
    logger.info("inside reading level")
    
    # Extract required inputs from state
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type
    tools = state.tools
    passage_based = state.assignment_state
    reading_level = state.student.reading_level
    
    content = state.enriched_tasks if passage_based == "processing_passage" else state.assignment
    
    grade = int(kincaid_grade_level_test_english(str(content)))
    reading_level_int = extract_and_convert_grade(reading_level)
    extra_prompt = reading_prompt_to_use(reading_level_int, grade)
    
    state.attempts += 1
    
    try:
        result = align_reading_level_agent(
            content=content,
            reading_level=reading_level,
            nb_of_questions=nb,
            extra_prompt=extra_prompt,
            assignment_type=assignment_type,
            tools=tools,
            passage_based=passage_based,
            ai_passage=state.ai_passage
        )
        logger.info("align_reading_level_node completed successfully")
        
        if passage_based == "processing_passage":
            state.ai_passage = result
            logger.info("Updated state.ai_passage successfully.")
        else:
            state.assignment = result
            logger.info("Updated state.assignment successfully.")

    except Exception as e:
        logger.error(f"Error in align_reading_level_node: {e}")
    
    logger.info(f"align_reading_level_node result: {result}")
    return state  