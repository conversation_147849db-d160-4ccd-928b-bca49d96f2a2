from langgraph.graph import StateGraph
from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.nodes.initial_assignments.initial_history_node import generate_initial_history_assignment_node
from service.nodes.common_agents.align_additional_info_node import check_additional_info_node
from service.nodes.common_agents.character_strength_node import assess_strengths_node
from service.nodes.common_agents.working_style_node import working_style_inclusion_node
from service.nodes.common_agents.identify_difficulty_node import adjust_assignment_difficulty_node
from service.nodes.common_agents.interest_alignment_node import interest_inclusion_node,interest_search_node
from service.nodes.common_agents.standard_node import assess_standard_node
from service.nodes.common_agents.aggregator_node import aggregator_node
from service.nodes.common_agents.generate_rubrics_node import rubric_questions_node
from service.nodes.common_agents.generate_rubrics_hints_node import rubric_hints_node
from service.nodes.common_agents.kindergarten_adjustment_node import kindergarten_adjustment_node
from service.nodes.common_agents.translate_assignment_node import translate_assignment_node
from service.nodes.common_agents.dyslexia_node import dyslexia_agent_node
from service.supporting_graph_functions import route_final_steps, start, check_if_kindergarten,updated_passages_manually_node,check_if_translation_needed
from service.nodes.reading_level.reading_level_node import check_reading_level_node, align_reading_level_node
from service.nodes.common_agents.develop_tasks_node import develop_tasks_node
from service.nodes.common_agents.enrich_with_interest_node import enrich_with_interest_node
from service.nodes.common_agents.extract_skills_node import extract_skills_node
from service.nodes.common_agents.understand_standard_node import understand_standard_node
from service.nodes.common_agents.create_passage_from_tasks_node import create_passage_from_tasks_node
from service.nodes.common_agents.answer_key_verification_node import answer_key_verification_node
def history_general_langgraph(ptx:GeneratePipelineContext,langfuse_handler):
    logger.info("Creating History Assignment")
    try:
        workflow = StateGraph(GeneratePipelineContext)

        workflow.add_node("generate_initial_history_assignment", generate_initial_history_assignment_node)
        workflow.add_node("check_additional_info", check_additional_info_node)
        workflow.add_node("assess_strengths", assess_strengths_node)
        workflow.add_node("working_style_inclusion", working_style_inclusion_node)
        workflow.add_node("adjust_difficulty", adjust_assignment_difficulty_node)
        workflow.add_node("align_to_interest", interest_inclusion_node)
        workflow.add_node("assess_standard", assess_standard_node)
        workflow.add_node("aggregator", aggregator_node)
        workflow.add_node("generate_rubric_questions", rubric_questions_node)
        workflow.add_node("generate_rubrics_hints", rubric_hints_node)
        workflow.add_node("kindergarten_adjustment", kindergarten_adjustment_node)
        workflow.add_node("translate_assignment", translate_assignment_node)
        workflow.add_node("dyslexia_agent", dyslexia_agent_node)
        workflow.add_node("align_reading_level", align_reading_level_node)
        workflow.add_node("start", start)
        workflow.add_node("interest_search_node", interest_search_node)
        workflow.add_node("develop_tasks", develop_tasks_node)
        workflow.add_node("enrich_with_interest", enrich_with_interest_node)
        workflow.add_node("extract_skills", extract_skills_node)
        workflow.add_node("understand_standard", understand_standard_node)
        workflow.add_node("create_passage_from_tasks", create_passage_from_tasks_node)
        workflow.add_node("updated_passages_manually", updated_passages_manually_node)
        workflow.add_node("answer_key_verification", answer_key_verification_node)

        workflow.set_entry_point("start")
        #workflow.add_edge("start", "generate_initial_history_assignment")
        workflow.add_edge("start",'interest_search_node')
        workflow.add_edge("start", "develop_tasks")
        workflow.add_edge("develop_tasks", "enrich_with_interest")
        workflow.add_edge("enrich_with_interest", "create_passage_from_tasks")
        workflow.add_edge("create_passage_from_tasks", "generate_initial_history_assignment")
        #workflow.add_edge("generate_initial_history_assignment","updated_passages_manually")
        # workflow.add_edge("updated_passages_manually", "assess_strengths")
        # workflow.add_edge("updated_passages_manually", "working_style_inclusion")
        # workflow.add_edge("updated_passages_manually", "adjust_difficulty")
        # workflow.add_edge("updated_passages_manually", "assess_standard")
        # #workflow.add_edge("interest_search_node", "align_to_interest")
        
        # if ptx.request.add_info:
        #     workflow.add_edge("updated_passages_manually", "check_additional_info")
        #     workflow.add_edge("check_additional_info", "aggregator")
        
        # if ptx.student.is_dyslexic:
        #     workflow.add_edge("updated_passages_manually", "dyslexia_agent")
        #     workflow.add_edge("dyslexia_agent", "aggregator")

        # workflow.add_edge("assess_strengths", "aggregator")
        # workflow.add_edge("working_style_inclusion", "aggregator")
        # workflow.add_edge("adjust_difficulty", "aggregator")
        # workflow.add_edge("align_to_interest", "aggregator")
        # workflow.add_edge("assess_standard", "aggregator")
        
        workflow.add_conditional_edges("generate_initial_history_assignment", check_if_translation_needed, {"Yes": "translate_assignment", "No": "align_reading_level"})        
        workflow.add_conditional_edges(
            "align_reading_level",
            check_reading_level_node,
            {"Pass": "answer_key_verification", "Fail": "align_reading_level"},
        )
        workflow.add_edge("translate_assignment","answer_key_verification")
        workflow.add_conditional_edges("answer_key_verification", check_if_kindergarten, {"Yes": "kindergarten_adjustment", "No": "generate_rubric_questions"}, )
        workflow.add_edge("kindergarten_adjustment", "generate_rubric_questions")
        #workflow.add_edge("answer_key_verification", "generate_rubric_questions")
        workflow.add_conditional_edges("generate_rubric_questions", route_final_steps)
        #workflow.add_conditional_edges("generate_rubrics_hints", route_final_steps)

        chain = workflow.compile()
        logger.info("Graph compiled successfully (Example Pattern Version)")

    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise # Re-raise the exception to prevent proceeding

    # Invoke the graph
    try:
        logger.info("Invoking graph...")
        output = chain.invoke(input=ptx, config={
                "callbacks": [langfuse_handler]
            })
        logger.info("Graph invocation complete.")
        # Check if 'assignment' key exists before returning
        if output and 'assignment' in output:
            if 'essay' in ptx.request.assignment_type:
                return output['assignment']
            else:
                return {"homework": output['assignment'], "substandards_data": output['substandards_data']}
        else:
            logger.error(f"Graph finished but 'assignment' key missing in final output: {output}")
            return {"homework": None, "error": "Assignment data missing in final state"}

    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True) 
        raise
        return {"homework": None, "error": str(e)}


