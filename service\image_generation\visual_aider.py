from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger
import json

def generate_visual_aid_instructions(assignment_question: dict,
                                     standard_used: str,
                                     grade_level: str,
                                     visual_decision_response: dict):
    """
    Produces a list of precise, implementation-ready instructions for the
    graphic that will accompany the assignment.  The calling pipeline has
    already selected the graphic category inside visual_decision_response.

    Returns (OpenAI function-call format):
    {
      "visual_instructions": ["Step 1 …", "Step 2 …", …]
    }
    """

    logger.info("generate_visual_aid_instructions")


    graphic_cat = visual_decision_response.get("recommended_category", "NONE")
    decision_reasoning = visual_decision_response.get("reasoning", "")


    system_prompt = f"""
ROLE
• You are a senior instructional designer and technical visualisation expert.

CONTEXT
• The downstream agent will read ONLY your list of instructions to write code
  that generates the required graphic.
• Graphic category to implement: {graphic_cat}

INSTRUCTION GUIDELINES
1. Produce a clear, ordered list (each list item = one concise instruction).
2. Include all technical details needed for coding:
   – axes limits, titles, point coordinates, bar heights, colours, labels, etc.
   – reference exact numbers, variables or context values from the assignment.
3. Keep vocabulary and visual complexity appropriate for grade {grade_level}.
4. DO NOT reveal or hint at the solution to the problem.
5. Focus solely on what and how to draw; do not mention libraries or code.
6. Your output must match the JSON schema shown below—no extra keys, no prose
   outside the JSON.
7. The Visuals can not use answer key and therefore should not include the answer key or the correct solution at all.

OUTPUT SCHEMA
{{
  "visual_instructions": ["<instruction 1>", "<instruction 2>", "..."]
}}
    """.strip()


    user_prompt = f"""
Assignment question (JSON):
{json.dumps(assignment_question, indent=2)}

Original reasoning for choosing the graphic (for context):
{decision_reasoning}

TASK
Write the ordered list of detailed visual instructions per the system prompt.
Return ONLY the JSON object defined in the schema.
""".strip()

    tools = [{
        "type": "function",
        "function": {
            "name": "visual_instructions_output",
            "description": "Return a list of detailed, step-by-step drawing instructions.",
            "parameters": {
                "type": "object",
                "properties": {
                    "visual_instructions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Ordered list of precise drawing instructions."
                    }
                },
                "required": ["visual_instructions"]
            }
        }
    }]


    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user",   "content": user_prompt}
    ]

    response = make_openai_langfuse(
        messages=messages,
        tools=tools,
        model="gpt-4.1-mini",
        temperature=0.35
    )

    logger.info(f"visual_aid_instructions_response: {response}")
    return response['visual_instructions']