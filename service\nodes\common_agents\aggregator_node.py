from service.gen_data_models import GeneratePipelineContext
from service.common_agents.aggregator_agent import aggregator_agent, aggregator_agent_passage
from service.context_pipeline import logger

def aggregator_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting aggregator_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    tools=state.tools
    nb_of_questions = state.request.nb_of_questions
    assignment_type=state.request.assignment_type
    assignment_state=state.assignment_state
    if assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    if not adjustment_instructions:
        logger.info("No adjustments provided. Aggregator node skipped.")
        return {}
    if 'develop_tasks' in adjustment_instructions:
        adjustment_instructions.pop('develop_tasks')
    try:
        response = aggregator_agent(
            original_assignment=initial_content,
            adjustment_instructions=adjustment_instructions,
            tools=tools,
            assignment_type=assignment_type,
            nb_of_questions=nb_of_questions,
            assignment_state=assignment_state
        )

        logger.info(f"Aggregator response: {response}")
        state.adjustment_instructions.clear()
        if state.assignment_state == 'processing_passage':
            if 'passage' in response:
                response=response['passage']
            state.ai_passage = response
            logger.info("Passage successfully revised by aggregator node.")
        else:
            state.assignment = response
            logger.info("Assignment successfully revised by aggregator node.")
    except Exception as e:
        logger.error(f"Error in aggregator_node: {e}")

    return state

def aggregator_passage_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent_passage, and updates the assignment accordingly.
    """
    logger.info(f"Starting aggregator_passage_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    tools=state.tools
    grade=state.student.grade
    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    if not adjustment_instructions:
        logger.info("No adjustments provided. Aggregator node skipped.")
        return {}

    try:
        response = aggregator_agent_passage(
            original_passage=initial_content,
            adjustment_instructions=adjustment_instructions,
            grade=grade,
            tools=tools        
            )

        logger.info(f"Aggregator response: {response}")
        state.adjustment_instructions.clear()
        if state.assignment_state == 'processing_passage':
            if 'passage' in response:
                response=response['passage']
            state.ai_passage = response
            logger.info("Passage successfully revised by aggregator node.")
            logger.info(f"New passage: {state.ai_passage}")

    except Exception as e:
        logger.error(f"Error in aggregator_node: {e}")

    return state
