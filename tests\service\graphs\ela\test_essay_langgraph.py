from unittest.mock import patch, MagicMock
import pytest

# Mock classes to simulate context
class MockStudent:
    def __init__(self, grade="3", is_dyslexic=False):
        self.grade = grade
        self.is_dyslexic = is_dyslexic

class MockRequest:
    def __init__(self, add_info=False):
        self.add_info = add_info

class MockGeneratePipelineContext:
    def __init__(self, grade="3", is_dyslexic=False, add_info=False):
        self.student = MockStudent(grade, is_dyslexic)
        self.request = MockRequest(add_info)
        self.assignment_state = None

# Successful test case
@patch("service.graphs.ela.essay_langgraph.StateGraph.compile")
@patch("service.graphs.ela.essay_langgraph.StateGraph.add_node")
@patch("service.graphs.ela.essay_langgraph.StateGraph.add_edge")
@patch("service.graphs.ela.essay_langgraph.StateGraph.set_entry_point")
@patch("service.graphs.ela.essay_langgraph.StateGraph.add_conditional_edges")
def test_ela_essay_langgraph_success(
    mock_conditional_edges,
    mock_entry,
    mock_edge,
    mock_node,
    mock_compile
):
    from service.graphs.ela.essay_langgraph import ela_essay_langgraph

    mock_chain = MagicMock()
    mock_chain.invoke.return_value = {"assignment": {"title": "Test Essay", "questions": ["Q1", "Q2"]}}
    mock_compile.return_value = mock_chain

    ctx = MockGeneratePipelineContext()
    result = ela_essay_langgraph(ctx, langfuse_handler=MagicMock())

    assert isinstance(result, dict)
    assert "title" in result
    assert result["title"] == "Test Essay"

# Failing test case (simulate compile error)
@patch("service.graphs.ela.essay_langgraph.StateGraph.compile", side_effect=Exception("Compilation failed"))
def test_ela_essay_langgraph_compile_failure(mock_compile):
    from service.graphs.ela.essay_langgraph import ela_essay_langgraph

    ctx = MockGeneratePipelineContext()
    with pytest.raises(Exception) as e:
        ela_essay_langgraph(ctx, langfuse_handler=MagicMock())
    assert "Compilation failed" in str(e.value)
