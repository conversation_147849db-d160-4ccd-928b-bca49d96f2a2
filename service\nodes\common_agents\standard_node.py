from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.context_pipeline import logger
from service.common_agents.standard_agent import assess_standard_agent

def assess_standard_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles assessment of assignment alignment with educational standards.

    Extracts necessary inputs from the state and calls the core agent functionality.
    Checks for maximum attempts before proceeding.
    """
    logger.info("Starting assess_standard_node")

    standard = state.request.standard
    grade = state.student.grade
    initial_assignment = state.assignment
    state_standard = state.request.state_standard
    main_topic = state.request.main_topic
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type

    try:
        response = assess_standard_agent(
            main_topic=main_topic,
            initial_assignment=initial_assignment,
            standard=standard,
            grade=grade,
            state_standard=state_standard,
            assignment_type=assignment_type,
            nb=nb
        )
        logger.debug(f"Standard assessment response: {response}")

        next_steps = response.get('next_steps', '').strip()
        if next_steps:
            state.adjustment_instructions['standard_alignment'] = next_steps
            logger.info("Standard alignment adjustment instructions updated.")
        else:
            logger.warning("Adjustment indicated but no 'next_steps' provided.")

        return {"adjustment_instructions": state.adjustment_instructions}

    except Exception as e:
        logger.error(f"Error in assess_standard_node: {e}")
        return {}