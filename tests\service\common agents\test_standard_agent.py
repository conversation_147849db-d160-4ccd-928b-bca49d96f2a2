# import pytest
# from unittest.mock import patch, MagicMock

# # ✅ Success Case
# @patch("service.common_agents.standard_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
# @patch("service.common_agents.standard_agent.langfuse.get_prompt")
# @patch("service.common_agents.standard_agent.make_openai_langfuse")
# # def test_assess_standard_agent_success(mock_make_openai_langfuse, mock_get_prompt):
# #     from service.common_agents.standard_agent import assess_standard_agent

# #     # Mock input
# #     initial_assignment = {"content": "Sample assignment"}
# #     standard = {"common_core_standard": "CCSS.MATH.CONTENT.5.NBT.A.1"}
# #     grade = "5"
# #     state_standard = "CA"
# #     main_topic = "Math"
# #     nb = 2

# #     # Mock <PERSON> prompt
# #     mock_prompt = MagicMock()
# #     mock_prompt.compile.return_value = "compiled_prompt"
# #     mock_prompt.config = {
# #         "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
# #     }
# #     mock_get_prompt.return_value = mock_prompt

# #     # Mock OpenAI response
# #     mock_make_openai_langfuse.return_value = {
# #         "aligns_with_standard": True,
# #         "reasoning": "This assignment meets the standard.",
# #         "next_steps": "No changes required."
# #     }

# #     # Execute
# #     result = assess_standard_agent(
# #         initial_assignment=initial_assignment,
# #         standard=standard,
# #         grade=grade,
# #         state_standard=state_standard,
# #         main_topic=main_topic,
# #         assignment_type="math",
# #         nb=nb
# #     )

# #     assert result["aligns_with_standard"] is True
# #     assert isinstance(result["reasoning"], str)
# #     assert isinstance(result["next_steps"], str)


# # ❌ Failure Case – simulate Langfuse crash
# @patch("service.common_agents.standard_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
# @patch("service.common_agents.standard_agent.langfuse.get_prompt")
# @patch("service.common_agents.standard_agent.make_openai_langfuse")
# def test_assess_standard_agent_failure(mock_make_openai_langfuse, mock_get_prompt):
#     from service.common_agents.standard_agent import assess_standard_agent

#     initial_assignment = {"content": "Broken content"}
#     standard = {"cluster_statement": "Understand data structures"}
#     grade = "7"
#     state_standard = "NY"
#     main_topic = "CS"
#     nb = 1

#     # Simulate Langfuse failure
#     mock_prompt = MagicMock()
#     mock_prompt.compile.side_effect = Exception("Langfuse prompt compile error")
#     mock_prompt.config = {
#         "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
#     }
#     mock_get_prompt.return_value = mock_prompt

#     with pytest.raises(Exception, match="Langfuse prompt compile error"):
#         assess_standard_agent(
#             initial_assignment=initial_assignment,
#             standard=standard,
#             grade=grade,
#             state_standard=state_standard,
#             main_topic=main_topic,
#             assignment_type="cs",
#             nb=nb
#         )
