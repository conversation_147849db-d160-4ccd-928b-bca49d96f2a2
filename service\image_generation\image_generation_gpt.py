from openai import OpenAI
import os
from service.context_pipeline import logger
import base64
import io
def create_image_using_gpt(prompt, model="gpt-image-1"):
    """
    Generates an image using DALL·E based on the input prompt and parameters.

    Parameters:
        prompt (str): The text prompt for image generation.
        model (str): The model to use (default: "dall-e-3").
        quality (str): The quality of the image. Can be "standard" or "hd".
        n (int): Number of images to generate.

    Returns:
        list: A list of URLs to the generated images.
    """
    if "OPENAI_API_KEY" in os.environ:
        client=OpenAI()
    else:
        logger.info("Using default OpenAI API key")
        client=OpenAI(api_key="***************************************************")
    try:
        logger.info(f"Generating image with prompt: {prompt}")
        response = client.responses.create(
            model=model,
            input=prompt,
            tools=[
            {
                "type": "image_generation",
                "quality": "high",
                'size': "1024x1024"
            },
                ],

        )
        image_data = [ output.result for output in response.output if output.type == "image_generation_call" ]
        logger.info(f"Image generated")
        image_base64 = image_data[0]
        return image_base64
    except Exception as e:
        print(f"Error generating image: {e}")
        return ""

def edit_image_using_gpt(prompt,image, model="gpt-image-1"):
    """
    Generates an image using DALL·E based on the input prompt and parameters.

    Parameters:
        prompt (str): The text prompt for image generation.
        model (str): The model to use (default: "dall-e-3").
        quality (str): The quality of the image. Can be "standard" or "hd".
        n (int): Number of images to generate.

    Returns:
        list: A list of URLs to the generated images.
    """
    if "OPENAI_API_KEY" in os.environ:
        client=OpenAI()
    else:
        logger.info("Using default OpenAI API key")
        client=OpenAI(api_key="***************************************************")

    try:
        img_bytes = base64.b64decode(image)
        img_file = io.BytesIO(img_bytes)
        img_file.name = "image.png"

    except Exception as e:
        logger.error(f"Failed to decode base64 image: {e}")
        raise ValueError(f"Invalid base64 image data: {e}")
    try:
        response = client.images.edit(
            model=model,
            prompt=prompt,
            image=img_file,           
        )
        image_base64 = response.data[0].b64_json
        logger.info(f"Image generated")
        #image_base64 = image_data[0]
        return image_base64
    except Exception as e:
        print(f"Error generating image: {e}")
        return ""
