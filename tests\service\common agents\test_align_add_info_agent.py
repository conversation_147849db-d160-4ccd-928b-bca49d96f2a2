import unittest
from unittest.mock import patch, MagicMock
from service.common_agents.align_add_info_agent import check_additional_info_assignment_agent

class TestCheckAdditionalInfoAssignmentAgent(unittest.TestCase):

    @patch('service.common_agents.align_add_info_agent.make_openai_langfuse')
    @patch('service.common_agents.align_add_info_agent.langfuse.get_prompt')
    def test_check_additional_info_assignment_agent(self, mock_get_prompt, mock_make_openai_langfuse):
        # Setup mock for langfuse.get_prompt
        mock_messages = MagicMock()
        mock_messages.compile.return_value = "compiled_prompt"
        mock_messages.config = {
            "openai": {
                "temperature": 0.3,
                "model": "gpt-4",
                "max_tokens": 3000
            }
        }
        mock_get_prompt.return_value = mock_messages

        # Setup mock for make_openai_langfuse
        expected_response = {
            "additional_info_inclusion": True,
            "reasoning": "The extra context was directly referenced in the introduction.",
            "next_steps": "Maintain clarity and provide examples."
        }
        mock_make_openai_langfuse.return_value = expected_response

        # Inputs
        grade = "8"
        add_info = "Include real-world applications of Pythagorean theorem"
        initial_assignment = "Explain the Pythagorean theorem in a real-world context."
        nb_of_questions = 2
        assignment_type = "math_problem"

        # Act
        result = check_additional_info_assignment_agent(grade, add_info, initial_assignment, nb_of_questions, assignment_type)

        # Assert
        self.assertEqual(result, expected_response)
        mock_get_prompt.assert_called_once_with('check_additionalinfo_inclusion', type='chat', label="latest")
        mock_make_openai_langfuse.assert_called_once()


if __name__ == '__main__':
    unittest.main()
