import json
import ast
from service.context_pipeline import logger,aisdk_object
from langchain_openai import ChatOpenAI
import re
from langchain_core.messages import SystemMessage, HumanMessage, BaseMessage
from tenacity import retry, stop_after_attempt, wait_fixed
from service.openai_utils.validate_response import validate_output_structure
from openai import OpenAI
client=OpenAI(api_key="********************************************************************************************************************************************************************")
def convert_to_langchain_messages(messages: list[dict]) -> list[BaseMessage]:
    role_map = {
        'system': SystemMessage,
        'user': HumanMessage,
    }
    return [role_map[msg['role']](content=msg['content']) for msg in messages]

#@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
#@aisdk_object.cache_result(ttl_in_days=30)
def make_openai_langfuse(messages, tools=[], model="gpt-4o", temperature=0.5, max_tokens=4096, nb_of_questions=None,frequency_penalty=0.5):
    #temperature=1
    #model="gpt-5"
    llm = ChatOpenAI(model=model, temperature=temperature, max_tokens= max_tokens,frequency_penalty=frequency_penalty,openai_api_key="********************************************************************************************************************************************************************")
    
    if isinstance(messages, list):
        messages=convert_to_langchain_messages(messages)
    kwargs = {"temperature": temperature}
    
    if max_tokens > 0:
        kwargs["max_tokens"] = max_tokens
    logger.info(f"messages/prompt:{messages}")

    if tools:
        kwargs.update({
            "tools": tools,
            "tool_choice": "required"
        })

        msg = llm.invoke(messages, **kwargs)
        logger.info(f"Content: {msg}")
        content = msg.additional_kwargs.get("tool_calls")[0]["function"]["arguments"]

        logger.info(f"Raw tool call content: {content}")
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except Exception as e:
                logger.error("Failed to convert content to dictionary using json.loads: %s", e)
                try:
                    content = ast.literal_eval(content)
                except Exception as e:
                    logger.error("Failed to convert content using ast.literal_eval: %s", e)
                    return content
        try:
            validate_output_structure(content, tools, nb_of_questions)
        except Exception as e:
            logger.error("Validation failed: %s", e)
            raise
        return content
    
    else:
        msg = llm.invoke(messages, **kwargs)
        logger.info(f"Content: {msg.content}")
        return msg.content
    
#@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
#@aisdk_object.cache_result(ttl_in_days=30)
def make_openai_langfuse_reasoning(messages, tools=[], model="o3"):
    model="o3"
    reasoning = {
    "effort": "low",  # 'low', 'medium', or 'high'
    "summary": "auto",  # 'detailed', 'auto', or None
}
    logger.info(f"setting up openai with model: {model}")
    if tools:
        llm = ChatOpenAI(model=model,use_responses_api=True, model_kwargs={"reasoning": reasoning,"tools":tools,"tool_choice":"required"})
    else:    
        llm = ChatOpenAI(model=model,use_responses_api=True, model_kwargs={"reasoning": reasoning})    
    if isinstance(messages, list):
        messages=convert_to_langchain_messages(messages)

    logger.info(f"messages/prompt:{messages}")

    if tools:

        msg = llm.invoke(messages)
        logger.info(f"Content: {msg}")
        content=msg.tool_calls[0]['args']
        logger.info(f"Raw tool call content: {content}")
        if isinstance(content, str):
            try:
                content = json.loads(content)
            except Exception as e:
                logger.error("Failed to convert content to dictionary using json.loads: %s", e)
                try:
                    content = ast.literal_eval(content)
                except Exception as e:
                    logger.error("Failed to convert content using ast.literal_eval: %s", e)
                    return content
        try:
            validate_output_structure(content, tools)
        except Exception as e:
            logger.error("Validation failed: %s", e)
            raise
        return content
    
    else:
        msg = llm.invoke(messages)
        logger.info(f"Content: {msg.content}")
        return msg.content
#@aisdk_object.cache_result(ttl_in_days=30)
#@retry(stop=stop_after_attempt(5), wait=wait_fixed(2))
def make_openai_langfuse_websearch(prompt, model, temperature=0.5, max_tokens=4096, location=None):
    prompt = convert_to_langchain_messages(prompt)
    logger.info(f"messages/prompt: {prompt}")
    
    llm = ChatOpenAI(
        model=model,
        temperature=temperature,
        openai_api_key="********************************************************************************************************************************************************************"
                )
    
    tool = {"type": "web_search_preview"}
    if location:
        tool["location"] = {
            "country": location.get('country'),
            "type": "approximate",
            "city": location.get('city'),
            "region": location.get('region')
        }
    
    llm_with_tools = llm.bind_tools([tool])
    response = llm_with_tools.invoke(prompt)
    
    logger.info(f"Original Content: {response.content}")
    
    text_list = [item['text'] for item in response.content]
    cleaned_list = [re.sub(r'\[.*?\]\(.*?\)', '', item).replace(' ()', '') for item in text_list]
    
    logger.info(f"Edited Content: {cleaned_list}")
    return cleaned_list

def make_openai_json_response(message,model="gpt-4.1",temperature=0.5,max_tokens=4096):
    agent_response = client.chat.completions.create(
        model=model,
        messages=message,
        temperature=temperature,
        max_tokens=max_tokens,
        response_format={"type": "json_object"},
    )
    output_raw = agent_response.choices[0].message.content.strip()
    output = json.loads(output_raw)
    return output