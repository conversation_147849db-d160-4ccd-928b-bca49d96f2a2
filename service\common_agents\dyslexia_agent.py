from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object

def dyslexia_agent(initial_assignment: dict, nb_of_questions: int, assignment_type: str) -> dict:
    """
    Core agent functionality for enhancing assignment readability for students with dyslexia.

    Simplifies language, improves structure, and provides support while maintaining
    contextual accuracy and the same number of problems.

    Args:
        json_response: The JSON object containing the assignment data
        nb: The number of items or problems in the assignment
        tools: Tools required for processing the OpenAI request

    Returns:
        dict: Enhanced assignment with improved accessibility for dyslexic students
    """
    try:
        messages = langfuse.get_prompt('dyslexia_agent', type='chat', label="latest")
        logger.debug("Successfully retrieved dyslexia_agent prompt from langfuse")

        complete_chat_prompt = messages.compile(initial_assignment=initial_assignment)
        logger.debug("Compiled chat prompt for dyslexia adaptation")

        tools = [
            {
                "type": "function",
                "function": {
                    "name": "check_dyslexia_accessibility",
                    "description": "Check whether the generated assignment is accessible to dyslexic students by evaluating key accessibility factors.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "supports_accessibility": {
                                "type": "boolean",
                                "description": "Indicates whether the assignment is accessible to dyslexic students."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": "Clear and concise explanation of your determination on whether the assignment is accessible to dyslexic students."
                            },
                            "next_steps": {
                                "type": "string",
                                "description": "Detailed explanation of why the assignment is accessible or what actions should be taken to make the assignment more accessible."
                            }
                        },
                        "required": ["supports_accessibility", "reasoning", "next_steps"]
                    }
                }
            }
        ]

        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)

        content = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens)
        logger.info("Successfully adapted content for dyslexic students")
        return content
    
    except Exception as e:
        logger.error(f"Error in dyslexia_agent: {e}")
        return initial_assignment