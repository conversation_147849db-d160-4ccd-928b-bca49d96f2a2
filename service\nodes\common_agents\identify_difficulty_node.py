from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.identify_difficulty import adjust_assignment_difficulty
from service.context_pipeline import logger

def adjust_assignment_difficulty_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles identifying the difficulty level of assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting identify_difficulty_node")

    grade = state.student.grade
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    difficulty=state.request.difficulty
    nb = state.request.nb_of_questions
    
    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif state.assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    try:
        difficulty_result = adjust_assignment_difficulty(
            intial_assignment=initial_content,
            grade=grade,
            standard=standard,
            original_difficulty=difficulty,
            assignment_type=assignment_type,
            nb=nb
        )

        logger.debug(f"Difficulty adjustment result: {difficulty_result}")

        if not difficulty_result:
            logger.info("Assignment difficulty matches intended level; no adjustments required.")
            #return {}
        else:
            state.adjustment_instructions['difficulty'] = difficulty_result
            logger.info("Difficulty adjustment instructions updated.")

            return {"adjustment_instructions": state.adjustment_instructions}

    except Exception as e:
        logger.error(f"Error in adjust_assignment_difficulty_node: {e}")
        return {}