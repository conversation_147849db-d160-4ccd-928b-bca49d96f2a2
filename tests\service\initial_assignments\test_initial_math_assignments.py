# redis.exceptions.TimeoutError: 
import pytest
from unittest.mock import patch
from service.initial_assignments import initial_math_assignments

@pytest.fixture
def mock_math_inputs():
    return {
        "first_name": "Sam",
        "grade": "5",
        "reading_level": "grade_5",
        "interests": ["space", "engineering"],
        "working_style": "visual",
        "strengths": ["persistence"],
        "standard": {"common_core_standard": "5.NBT.B.7"},
        "state_standard": "CA-MATH-5.1",
        "difficulty": "medium",
        "assignment_type": "math_fact",
        "nb_of_questions": 3
    }

@patch("service.initial_assignments.initial_math_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_math_assignments.make_openai_langfuse")
def test_generate_math_fact_assignment(mock_openai, mock_prompt, mock_math_inputs):
    mock_prompt.return_value.compile.return_value = "compiled math_fact prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Math Facts Assignment"}}

    output, tools = initial_math_assignments.generate_initial_math_assignment(**mock_math_inputs)
    assert isinstance(output, dict)
    assert "homework" in output
    assert output["homework"]["title"] == "Math Facts Assignment"

@patch("service.initial_assignments.initial_math_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_math_assignments.make_openai_langfuse")
def test_generate_math_story_assignment(mock_openai, mock_prompt, mock_math_inputs):
    mock_math_inputs["assignment_type"] = "math_story"
    mock_prompt.return_value.compile.return_value = "compiled math_story prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Math Story Assignment"}}

    output, tools = initial_math_assignments.generate_initial_math_assignment(**mock_math_inputs)
    assert output["homework"]["title"] == "Math Story Assignment"

@patch("service.initial_assignments.initial_math_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_math_assignments.make_openai_langfuse")
def test_generate_math_partner_assignment(mock_openai, mock_prompt, mock_math_inputs):
    mock_math_inputs["assignment_type"] = "math_partner"
    mock_prompt.return_value.compile.return_value = "compiled math_partner prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Partner Math Assignment"}}

    output, tools = initial_math_assignments.generate_initial_math_assignment(**mock_math_inputs)
    assert output["homework"]["title"] == "Partner Math Assignment"

@patch("service.initial_assignments.initial_math_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_math_assignments.make_openai_langfuse")
def test_generate_math_worked_example_assignment(mock_openai, mock_prompt, mock_math_inputs):
    mock_math_inputs["assignment_type"] = "math_worked_example"
    mock_prompt.return_value.compile.return_value = "compiled worked_example prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "Worked Example Assignment"}}

    output, tools = initial_math_assignments.generate_initial_math_assignment(**mock_math_inputs)
    assert output["homework"]["title"] == "Worked Example Assignment"
