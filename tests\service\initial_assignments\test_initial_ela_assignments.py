import pytest
from unittest.mock import patch, MagicMock
from service.initial_assignments import initial_ela_assignments


@pytest.fixture
def mock_inputs():
    return {
        "grade": "5",
        "first_name": "<PERSON>",
        "working_style": "collaborative",
        "reading_level": "grade 5",
        "life_skills": ["critical thinking"],
        "passage_style": "narrative",
        "interest": ["space exploration"],
        "state_standard": "CCSS.ELA-LITERACY.RL.5.1",
        "standard": {"common_core_standard": "RL.5.1"},
        "assignment_type": "reading_comp_gen",
        "add_info": "<PERSON> enjoys stories with astronauts",
        "difficulty": "medium",
        "strengths": ["curiosity"],
        "nb": 3,
        "nb_of_questions": 2,
        "passage": "Sample passage for essay",
    }


@patch("service.initial_assignments.initial_ela_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_ela_assignments.make_openai_langfuse")
def test_generate_initial_ela_passage_success(mock_openai, mock_prompt, mock_inputs):
    mock_prompt.return_value.compile.return_value = "compiled prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"passage": ["This is a test passage."]}

    passage, tools = initial_ela_assignments.generate_initial_ela_passage(**mock_inputs)
    assert isinstance(passage, list)
    assert "This is a test passage." in passage


@patch("service.initial_assignments.initial_ela_assignments.langfuse.get_prompt")
@patch("service.initial_assignments.initial_ela_assignments.make_openai_langfuse")
def test_generate_initial_ela_assignment_success(mock_openai, mock_prompt, mock_inputs):
    mock_prompt.return_value.compile.return_value = "compiled assignment prompt"
    mock_prompt.return_value.config = {"openai": {"model": "gpt-4.1", "temperature": 0.5, "max_tokens": 4096}}
    mock_openai.return_value = {"homework": {"title": "ELA Assignment"}}

    mock_inputs["ai_passage"] = ["This is a test passage."]
    assignment, tools = initial_ela_assignments.generate_initial_ela_assignment(**mock_inputs)
    assert isinstance(assignment, dict)
    assert "homework" in assignment
    assert assignment["homework"]["title"] == "ELA Assignment"


def test_generate_initial_ela_passage_invalid_assignment_type(mock_inputs):
    mock_inputs["assignment_type"] = "invalid_type"
    with pytest.raises(Exception):
        initial_ela_assignments.generate_initial_ela_passage(**mock_inputs)
