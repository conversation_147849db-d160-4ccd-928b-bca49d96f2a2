from service.openai_utils.gpt_call import make_openai_langfuse
from service.reading_level.reading_level_agent import get_guideline_by_level
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object

def kindergarten_adjustment_agent(json_response: dict, student: dict, nb: int, tools: dict, assignment_type: str) -> dict:
    """
    Core agent functionality for adjusting assignments for kindergarten students.

    Simplifies language, improves readability, and ensures assignments are accessible to young learners
    while maintaining the original context and number of problems.

    Args:
        json_response: The JSON object containing the assignment data
        student: The student's information
        nb: The number of problems in the assignment
        tools: Tools required for processing the OpenAI request

    Returns:
        dict: Assignment adjusted for kindergarten level
    """
    logger.info("Starting kindergarten_adjustment_agent")
    try:
        logger.debug("Getting reading level guideline for kindergarten")
        reading_level_guideline = get_guideline_by_level("kindergarten")

        logger.debug("Retrieving kindergarten_adjustment prompt from langfuse")

        messages = langfuse.get_prompt('kindergarten_adjustment', type='chat', label="latest")
        if 'last_name' in student:
            student.pop('last_name')
        complete_chat_prompt = messages.compile(
            student=student,
            reading_level_guideline=reading_level_guideline,
            nb=nb,
            json_response=json_response
        )
        
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        
        if nb ==1:
            assignment = make_openai_langfuse(complete_chat_prompt, tools, model=model, temperature=temperature, max_tokens=max_tokens)
            if isinstance(assignment["homework"]["challenges"], dict):
                assignment["homework"]["challenges"] = [assignment["homework"]["challenges"]]
        else:
            assignment = make_openai_langfuse(complete_chat_prompt, tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb)
            
        logger.info("Successfully adjusted assignment for kindergarten level")
        return assignment
    
    except Exception as e:
        logger.error(f"Error in kindergarten_adjustment_agent: {e}")
        return json_response