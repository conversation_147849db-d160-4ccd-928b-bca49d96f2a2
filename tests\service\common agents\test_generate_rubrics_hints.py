# from unittest.mock import patch, MagicMock

# # Patch BEFORE import
# patch('service.common_agents.generate_rubrics_hints.aisdk_object.cache_result', lambda *a, **kw: lambda f: f).start()

# from service.common_agents.generate_rubrics_hints import generate_rubrics_hints_agent


# @patch('service.common_agents.generate_rubrics_hints.make_openai_langfuse')
# @patch('service.common_agents.generate_rubrics_hints.langfuse.get_prompt')
# def test_generate_rubrics_hints_agent_essay(mock_get_prompt, mock_make_openai_langfuse):
#     mock_prompt = MagicMock()
#     mock_prompt.compile.return_value = "compiled_essay_prompt"
#     mock_prompt.config = {
#         "openai": {"temperature": 0.4, "model": "gpt-4.1", "max_tokens": 1024}
#     }
#     mock_get_prompt.return_value = mock_prompt
#     mock_make_openai_langfuse.return_value = "Generated hint for essay"

#     assignment = {
#         "essay": {
#             "rubrics": ["clarity", "evidence", "organization"],
#             "description": ""
#         }
#     }

#     result = generate_rubrics_hints_agent(assignment, grade=7, assignment_type="essay", nb=1)

#     assert result["essay"]["description"] == "Generated hint for essay"
