# import sys
# import pytest
# from unittest.mock import patch, MagicMock

# # Patch BEFORE the import to override the Redis cache decorator
# patch('service.common_agents.generate_rubrics.aisdk_object.cache_result', lambda *a, **kw: lambda f: f).start()

# # Now import the module (after patching)
# from service.common_agents.generate_rubrics import generate_rubric_questions_agent

# @patch('service.common_agents.generate_rubrics.langfuse.get_prompt')
# @patch('service.common_agents.generate_rubrics.make_openai_langfuse')
# def test_generate_rubric_questions_agent(mock_make_openai_langfuse, mock_get_prompt):
#     # Mock prompt object
#     mock_prompt = MagicMock()
#     mock_prompt.compile.return_value = "compiled_prompt"
#     mock_prompt.config = {
#         "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
#     }
#     mock_get_prompt.return_value = mock_prompt

#     # Mock OpenAI return
#     mock_make_openai_langfuse.return_value = {
#         "rubrics": [
#             {
#                 "rubric_question_number": "Q1",
#                 "rubric_question_text": "Is the answer relevant to the task?"
#             }
#         ],
#         "learning_outcomes": ["Understand the concept"]
#     }

#     # Input for the test
#     test_assignment = {
#         "homework": {
#             "challenges": [
#                 {"task": "Explain gravity.", "answerKey": "Gravity is a force..."}
#             ]
#         }
#     }

#     result = generate_rubric_questions_agent(
#         assignment=test_assignment,
#         standard="NGSS.5.PS2.1",
#         assignment_type="science",
#         passage="Gravity affects all objects.",
#         grammar=True,
#         grade="5",
#         difficulty="medium",
#         nb=1
#     )

#     assert "challenges" in result
#     assert "rubrics" in result["challenges"][0]
#     assert len(result["challenges"][0]["rubrics"]) > 0
