from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger, aisdk_object
from service.context_pipeline import langfuse

def assess_standard_agent(initial_assignment: dict, standard: dict, grade: str, 
                          state_standard: str, main_topic:str, assignment_type: str, nb: int) -> str:
    """
    Core agent functionality for evaluating an assignment's alignment with educational standards.
    
    Assesses whether an assignment aligns with specific common core standards, cluster statements,
    and grade-level benchmarks.
    
    Args:
        json_response: The JSON object containing the assignment data
        standard: The common core standard details
        grade: The grade level for which the assignment is intended
        state_standard: The state-specific educational standard
        tools: Tools required for processing the OpenAI request
        
    Returns:
        str: "Pass" if the assignment aligns with standards, "Fail" otherwise
    """
    standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')

    assessment_tools = [
        {
            "type": "function",
            "function": {
                "name": "assess_assignment_standard_alignment",
                "description": "Assess whether a homework assignment aligns with the specified common core standard, cluster statement, and grade-level benchmarks.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "aligns_with_standard": {
                            "type": "boolean",
                            "description": "Indicates whether the assignment appropriately aligns with the given educational standard."
                        },
                        "reasoning": {
                            "type": "string",
                            "description": "Clear and concise explanation of the determination regarding standard alignment."
                        },
                        "next_steps": {
                            "type": "string",
                            "description": "Detailed explanation of why it aligns or what actions should be taken to maintain or improve alignment."
                        }
                    },
                    "required": ["aligns_with_standard", "reasoning", "next_steps"]
                }
            }
        }
    ]

    messages = langfuse.get_prompt(name='assess_standard', type='chat', label="latest")
    complete_chat_prompt = messages.compile(
        standard=standard_data,
        grade=grade,
        state_standard=state_standard,
        initial_assignment=initial_assignment,
        main_topic =main_topic
    )
    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)

    content = make_openai_langfuse(complete_chat_prompt, tools=assessment_tools, temperature=temperature, model=model, max_tokens=max_tokens)
    return content