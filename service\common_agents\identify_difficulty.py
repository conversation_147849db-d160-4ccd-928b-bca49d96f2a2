from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger

def identify_assignment_difficulty(assignment_type, initial_assignment, grade, standard, nb):
    """
    Identify the difficulty level of an assignment based on its type, content, grade, and standard.

    Args:
        assignment_type (str): Type of the assignment (math, history, essay, etc.)
        initial_assignment (str): Content of the assignment
        grade (str): Grade level
        standard (str): Educational standard

    Returns:
        str: Identified difficulty level
    """
    logger.info(f"Starting difficulty identification for {assignment_type} assignment")
    try:
        difficulty_map = {
            "ela": "difficulty_levels_criteria_ELA_general",
            "math": "difficulty_levels_criteria_math_general",
            "history": "difficulty_levels_criteria_history_general",
            "ela_essay": "difficulty_levels_criteria_ELA_essay",
            "history_essay": "difficulty_levels_criteria_history_essay"
        }

        # Determine the appropriate prompt and subject based on assignment type
        logger.debug(f"Determining prompt and subject for assignment type: {assignment_type}")
        if 'math' in assignment_type:
            langfuse_difficulty_prompt = difficulty_map["math"]
            subject = 'maths'
        elif 'history' in assignment_type and 'essay' in assignment_type:
            langfuse_difficulty_prompt = difficulty_map["history_essay"]
            subject = 'history'
        elif 'history' in assignment_type:
            langfuse_difficulty_prompt = difficulty_map["history"]
            subject = 'history'
        elif 'essay' in assignment_type:
            langfuse_difficulty_prompt = difficulty_map["ela_essay"]
            subject = 'english languge'
        else:
            langfuse_difficulty_prompt = difficulty_map["ela"]
            subject = 'english languge'

        logger.info(f"Selected prompt: {langfuse_difficulty_prompt}, subject: {subject}")

        try:
            logger.debug("Fetching difficulty criteria from Langfuse")
            messages = langfuse.get_prompt(langfuse_difficulty_prompt, type='text', label="latest")
            difficulty_criteria = messages.compile()

            logger.debug("Fetching difficulty agent prompt from Langfuse")
            messages = langfuse.get_prompt('identify_difficulty_agent', type='chat', label="latest")
            complete_chat_prompt = messages.compile(
                criteria=difficulty_criteria,
                grade=grade,
                standard=standard,
                initial_assignment=initial_assignment,
                subject=subject,
            )
        except Exception as e:
            logger.error(f"Error fetching prompts from Langfuse: {str(e)}")
            raise

        tools = [
            {
                "type": "function",
                "function": {
                    "name": "identify_difficulty",
                    "description": "Determine the overall difficulty level of an assignment based on subject-specific criteria and educational complexity.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "difficulty_level": {
                                "type": "string",
                                "enum": ["easy", "medium", "hard"],
                                "description": "Overall difficulty level of the assignment, categorized as 'easy', 'medium', or 'hard'."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": "Detailed explanation justifying the assigned difficulty level based on conceptual depth, skill integration, problem complexity, and other educational criteria."
                            }
                        },
                        "required": ["difficulty_level", "reasoning"]
                    }
                }
            }
        ]

        temperature = messages.config.get("openai", {}).get("temperature", 0.5)
        model = messages.config.get("openai", {}).get("model", "gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

        logger.debug(f"Making OpenAI call with model: {model}, temperature: {temperature}")

        try:
            response = make_openai_langfuse(
                messages=complete_chat_prompt,
                tools=tools,
                temperature=temperature,
                model=model,
                max_tokens=max_tokens,
            )
            logger.info(f"Successfully identified difficulty level")
            logger.debug(f"OpenAI response received: {response}")
            return response['difficulty_level']
        except Exception as e:
            logger.error(f"Error in OpenAI API call: {str(e)}")
            raise

    except Exception as e:
        logger.error(f"Error in identify_assignment_difficulty: {str(e)}")
        raise

def adjust_assignment_difficulty(intial_assignment, grade, standard, original_difficulty, assignment_type, nb):
    """
    Adjust the difficulty of an assignment if the identified difficulty doesn't match the requested difficulty.

    Args:
        intiial_assignment (str): Content of the initial assignment
        grade (str): Grade level
        standard (str): Educational standard
        original_difficulty (str): The requested difficulty level
        assignment_type (str): Type of the assignment (math, history, essay, etc.)

    Returns:
        dict: Empty dict if no adjustment needed, otherwise a dict with adjustment direction and prompt
    """
    logger.info(f"Starting difficulty adjustment for {assignment_type} assignment")
    logger.debug(f"Original requested difficulty: {original_difficulty}")

    try:
        logger.debug("Calling identify_assignment_difficulty to determine current difficulty")
        assignment_difficulty_identified = identify_assignment_difficulty(assignment_type, intial_assignment, grade, standard, nb)
        if not assignment_difficulty_identified:
            logger.warning("Failed to identify difficulty level")
            assignment_difficulty_identified=original_difficulty
        logger.info(f"Identified difficulty: {assignment_difficulty_identified}, Original difficulty: {original_difficulty}")

        if assignment_difficulty_identified.lower() == original_difficulty.lower():
            logger.info("No difficulty adjustment needed - identified difficulty matches requested difficulty")
            return {}
        else:
            logger.info("Difficulty adjustment needed - identified difficulty differs from requested difficulty")

            try:
                difficulty_dict = {
                    "easy": 0,
                    "medium": 1,
                    "hard": 2
                }

                original_difficulty_value = difficulty_dict[original_difficulty.lower()]
                identified_difficulty_value = difficulty_dict[assignment_difficulty_identified.lower()]

                if original_difficulty_value < identified_difficulty_value:
                    adjustment_direction = "decrease"
                else:
                    adjustment_direction = "increase"

                logger.info(f"Adjustment direction: {adjustment_direction}")
                logger.debug(f"Original difficulty value: {original_difficulty_value}, Identified difficulty value: {identified_difficulty_value}")

                difficulty_map = {
                    "ela": "difficulty_ELA_general",
                    "math": "difficulty_math_general",
                    "history": "difficulty_history_general",
                    "ela_essay": "difficulty_ELA_essay",
                    "history_essay": "difficulty_history_essay"
                }

                logger.debug(f"Selecting difficulty prompt for assignment type: {assignment_type}")
                if 'math' in assignment_type:
                    langfuse_adjust_difficulty_prompt = difficulty_map["math"]
                elif 'history' in assignment_type and 'essay' in assignment_type:
                    langfuse_adjust_difficulty_prompt = difficulty_map["history_essay"]
                elif 'history' in assignment_type:
                    langfuse_adjust_difficulty_prompt = difficulty_map["history"]
                elif 'essay' in assignment_type:
                    langfuse_adjust_difficulty_prompt = difficulty_map["ela_essay"]
                else:
                    langfuse_adjust_difficulty_prompt = difficulty_map["ela"]

                logger.info(f"Selected adjustment prompt: {langfuse_adjust_difficulty_prompt}")

                try:
                    logger.debug(f"Fetching {adjustment_direction}_{langfuse_adjust_difficulty_prompt} prompt from Langfuse")
                    messages = langfuse.get_prompt(f'{adjustment_direction}_{langfuse_adjust_difficulty_prompt}', type='text', label="latest")
                    adjustment_direction_prompt = messages.compile()
                    logger.info(f"Successfully retrieved adjustment prompt")

                    return {f"{adjustment_direction}_difficulty": adjustment_direction_prompt}
                except Exception as e:
                    logger.error(f"Error fetching adjustment prompt from Langfuse: {str(e)}")
                    raise

            except KeyError as e:
                logger.error(f"Invalid difficulty level encountered: {str(e)}")
                raise
            except Exception as e:
                logger.error(f"Error in difficulty adjustment process: {str(e)}")
                raise

    except Exception as e:
        logger.error(f"Error in adjust_assignment_difficulty: {str(e)}")
        raise



