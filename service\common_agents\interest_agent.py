from service.openai_utils.gpt_call import make_openai_langfuse,make_openai_langfuse_websearch
from service.context_pipeline import logger, aisdk_object
from service.context_pipeline import langfuse
import random



def interest_search_agent(interest,grade,search_queries):    
    messages = langfuse.get_prompt('interest_search_agent', type='chat', label="latest")
    prompt = messages.compile(interest=interest, grade=grade,search_queries=search_queries)
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    response=make_openai_langfuse_websearch(prompt, model, temperature,max_tokens)
    system_prompt="""You are an information organizer. Your task is to take input text (often a single long string inside a one-element list) and return a clean list of section strings. Each section string must contain a section heading followed by its bullet points, preserving the original wording.

        Follow these rules:

        - Input normalization: If the input is a list of strings, join them in order into one text block without adding characters.
        - Remove scaffolding: Delete any introductory line before the first section heading and any closing/summary line after the last section.
        - Section detection: A section heading is a standalone line (no leading bullet) that typically ends with a colon and may be wrapped in Markdown emphasis (e.g., Heading:). Everything until the next heading (or end) belongs to that section.
        - Preserve content: Do not paraphrase, reorder, or add content. Keep original wording, punctuation, capitalization, diacritics, and the dash bullets.
        - Strip Markdown emphasis: Remove Markdown bold/italic markers (** __ * _) from headings and within bullet lines, but keep the text exactly otherwise.
        - Bullets and spacing: Ensure bullet lines begin with "- " (dash + space). Remove extra blank lines; use single newlines between lines inside a section. Trim leading/trailing whitespace inside each section.
        
        Output format: Return only a JSON array of strings. Each array element is one section, formed as: "Heading:\n- Bullet 1...\n- Bullet 2...\n..."
        No extra commentary, keys, labels, or trailing commas. If sections cannot be identified, return a single-element array with the cleaned text.
"""
    user_prompt=f"""Organize the given information into a list of strings by section. Remove the opening intro and closing summary lines, keep the exact wording of headings and bullets, strip Markdown bold/italic markers, and return only a JSON array of section strings in their original order.
    Information:{response}"""
    messages=[{"role": "system", "content": system_prompt},
              {"role": "user", "content": user_prompt}]
    tools = [{
        "type": "function",
        "function": {
            "name": "refine_sections",
            "description": "Convert input text into a cleaned list of section strings; returns { refined: string[] }.",
            "parameters": {
                "type": "object",
                "properties": {
                    "refined_version": {
                        "type": "array",
                        "description": "Rewrite the information into a list of strings by section.",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name":        {"type": "string", "description": "Name/heading of the section"},
                                "description": {"type": "string", "description": "Detailed description of the section"}
                            },
                            "required": ["name", "description"]
                        }
                    }
                },
                "required": ["refined_version"]
            }
        }
    }]
    cleanup_repsone=make_openai_langfuse(messages,model="gpt-4.1",temperature=0.2,max_tokens=max_tokens,tools=tools)
    
    return cleanup_repsone['refined_version']

def supporting_interest_agent(initial_assignment: dict, interest_information: str, nb: int, assignment_type: str, assignment_state: str) -> dict:
    """
    Aligns assignments to student interests by generating fun facts and current information
    using a single OpenAI API call with web search capability.

    Returns:
        dict: {
            "adjustment_instructions": str (empty if already included)
        }
    """
    tools = [
        {
            "type": "function",
            "function": {
                "name": "check_feedback_for_interest_reference",
                "description": "Evaluate whether the feedback incorporates the student's personal interests to make it more engaging and relevant.",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "interest_referenced": {
                            "type": "boolean",
                            "description": "Indicates whether the feedback includes reference to the student’s personal interests or real-world connections that matter to them."
                        },
                        "reasoning": {
                            "type": "string",
                            "description": "Explanation of why the feedback does or does not reference the student’s interests."
                        },
                        "interest_guidance": {
                            "type": "string",
                            "description": "If interest is referenced, explain how it supports engagement and how to maintain or expand it. If not, suggest specific ways to tailor the feedback using student interests."
                        }
                    },
                    "required": ["interest_referenced", "reasoning", "interest_guidance"]
                }
            }
        }
    ]

    logger.info("Checking if the assignment already includes the interest.")
    messages = langfuse.get_prompt('Interest_agent_for_updaton', type='chat', label="latest")
    complete_chat_prompt = messages.compile(
        initial_assignment=initial_assignment,
        interesting_information=interest_information,
    )
    
    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
    result = make_openai_langfuse(complete_chat_prompt, temperature=temperature, model=model, max_tokens=max_tokens,tools=tools)
    return result

def _build_general_information(interest, grade):
    sub_category = random.choice([
        'interest_fun_facts_agent',
        'interest_historical_facts',
        'interest_famous_personalities',
        'interest_related_topics'
    ])
    logger.info(f"Generating information for {sub_category}")

    messages      = langfuse.get_prompt(sub_category, type='chat', label="latest")
    prompt        = messages.compile(interest=interest, grade=grade)
    temperature   = messages.config.get("openai", {}).get("temperature", 0.5)
    model         = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens    = messages.config.get("openai", {}).get("max_tokens", 4096)
    tools = [{
        "type": "function",
        "function": {
            "name": "generate_agent_information",
            "description": "Generates a structured list of information based on the agent's predefined category of information focus.",
            "parameters": {
                "type": "object",
                "properties": {
                    "information": {
                        "type": "array",
                        "description": "Generated list of information entries tailored specifically to agent's category",
                        "items": {
                            "type": "object",
                            "properties": {
                                "name":        {"type": "string", "description": "Name/title of the generated information"},
                                "description": {"type": "string", "description": "Detailed description of the generated information"}
                            },
                            "required": ["name", "description"]
                        }
                    }
                },
                "required": ["information"]
            }
        }
    }]

    result = make_openai_langfuse(prompt,
                                  temperature=temperature,
                                  model=model,
                                  max_tokens=max_tokens,
                                  tools=tools)
    logger.info(f"General Interest Information: {result}")
    return str(result['information'])


def choose_random_interest_information(interest, grade):
    """
    Tries to return 'real-time' information first (50 % chance).
    If the real-time path fails, automatically falls back to 'general'.
    """
    main_category = random.choice(['real_time', 'general'])
    logger.info(f"Main category chosen: {main_category}")

    if main_category == 'real_time':
        try:
            sub_category = random.choice([
                'interest_search_news',
                'interest_search_trends_and_events',
                'interest_search_popular_discussions'
            ])
            logger.info(f"Generating information for {sub_category}")

            messages      = langfuse.get_prompt(sub_category, type='chat', label="latest")
            prompt        = messages.compile(interest=interest, grade=grade)
            temperature   = messages.config.get("openai", {}).get("temperature", 0.5)
            model         = messages.config.get("openai", {}).get("model", "gpt-4.1")
            max_tokens    = messages.config.get("openai", {}).get("max_tokens", 4096)
            tools = [{
                "type": "function",
                "function": {
                    "name": "generate_search_queries",
                    "description": "Generate a list of search queries related to a specific topic or interest.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "search_queries": {
                                "type": "array",
                                "items": {
                                    "type": "string",
                                    "description": "A relevant search query related to the topic or interest provided."
                                },
                                "description": "List of search query strings."
                            }
                        },
                        "required": ["search_queries"]
                    }
                }
            }]

            result = make_openai_langfuse(prompt,
                                          temperature=temperature,
                                          model=model,
                                          max_tokens=max_tokens,
                                          tools=tools)
            logger.info(f"Search Queries: {result}")

            interest_information = interest_search_agent(
                interest, grade, result['search_queries'])
            logger.info(f"Interest Information: {interest_information}")

            return interest_information

        except Exception as e:
            logger.error(f"Real-time search failed ({e}). Falling back to general.")

    # Either the random choice was 'general' or the real-time path failed:
    return _build_general_information(interest, grade)