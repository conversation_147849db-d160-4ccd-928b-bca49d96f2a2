from service.openai_utils.gpt_call import make_openai_langfuse
from service.image_generation.image_generation_gpt import create_image_using_gpt,edit_image_using_gpt
from service.context_pipeline import logger

def inspect_generated_image(image, assignment):
    task = assignment.get("task", "Task not provided.")
    description = assignment.get("description", "No additional details provided.")
    scenario = assignment.get("scenario", "No specific scenario given.")
    answer_key= assignment.get("answerKey", "No answer key provided.")

    system_prompt = """
You are an expert "Educational Image Accuracy Inspector" tasked with critically evaluating educational images created to visually complement assignments.

Your Main Role:
Carefully and strictly inspect each educational image for visual accuracy, conceptual correctness, numeric consistency, completeness, clarity, and appropriateness based entirely and explicitly upon provided assignment details.

Must-Follow Inspection Criteria (Clearly Defined Steps):

Step 1. Strict Visual Accuracy:
- STRICTLY ASSERT the image's visual representation matches precisely and explicitly the provided assignment scenario and mathematical task.
- ENSURE explicitly stated numerical values, quantities, dimensions, proportions or relationships explicitly mentioned in the task or scenario are visually correct, accurate, depicted clearly, and consistently within the image.
- VERIFY explicitly that if numeric requirements explicitly exist in the task or scenario, the image visually reflects these exact numeric values or proportions explicitly. Images should NEVER randomly depict numeric values differing from the explicit numerical context provided in the assignment.

Step 2. Numeric Consistency and Label Validations:
- Confirm explicitly the correctness and explicit presence of numeric related visuals, including labeled lines, arrows, distances, units (yards, meters, centimeters, percentages), and graphical indicators as required.
- Identify explicitly minor textual, numeric, or labeling inaccuracies or omissions explicitly fixable through minor edits (e.g., correcting or adding labels clearly matching numbers or units explicitly provided, adding numeric proportions explicitly indicated clearly).

Step 3. Explicit Answer Visibility Check (Critical):
- Explicitly CONFIRM that the image NEVER explicitly nor implicitly reveals numeric solutions from the provided "Answer Key".
- Ensure visual indicators explicitly avoid the numeric calculation or numeric solution provided within the Answer Key.

Structured Response Format (Explicitly Required):
Always explicitly respond with the following structured format explicitly:

{
  "image_passed": <True ONLY IF fully correct & strictly aligned to provided numeric values, context, and constraints; otherwise False>,
  "editable": <True ONLY IF minor numeric/textual/label corrections explicitly can fix inaccuracies clearly; False if significant regeneration needed>,
  "reasoning": "<Clear, explicit, concise reasoning explicitly referring ONLY to provided Task, Scenario, Answer Key details>",
  "adjustment_instructions": "<Clear, precise, actionable instructions for minor numeric/textual/label edits explicitly needed, CLEARLY indicating exactly what to edit and ALSO clearly stating what NOT to edit (negative prompt). If no edits are needed or complete regeneration is required instead, return empty string ("")>"
}

Explicit Instructions for Adjustments (Repeatedly Emphasized New Instruction):
- Adjustment instructions MUST ALWAYS be PRECISE, CLEAR, SPECIFIC, and ACTIONABLE, explicitly stating EXACTLY what should be changed.
- Adjustment instructions MUST explicitly ALSO include NEGATIVE prompts clearly stating EXACTLY what elements NOT to change or alter in the image.

Explicit Constraints & Reminders (Repeatedly emphasized):
- Inspections MUST be explicitly grounded solely on explicitly provided assignment details (Task, Scenario, Answer Key).
- Numeric values explicitly given in the assignment MUST explicitly match visual representations.
- Explicitly suggest ONLY minor, clearly defined numeric or textual edits; NEVER extensive redesign.
- NEVER assume or infer numeric/contextual details explicitly NOT provided.
- ALWAYS clearly and explicitly provide NEGATIVE prompts alongside adjustment instructions, clearly stating details or aspects NOT to change in the image.
"""

    user_prompt = f"""
    Explicitly inspect and evaluate the provided image strictly based on the given assignment information:

Scenario:
{scenario}

Task:
{task}

Description (for additional guidance only):
{description}

Answer Key (for STRICT negative checking explicitly):
{answer_key}

Explicit Inspection Task:
- Perform a strict inspection explicitly ensuring the provided image is accurate, numerically consistent, clearly labeled, and conceptually aligned strictly with the provided scenario and explicitly stated task.
- Clearly confirm numeric visuals explicitly match the numeric details explicitly provided.
- Explicitly ensure NO numeric answers from the "Answer Key" are visually revealed explicitly or implicitly.

If the image needs minor editing:
- Adjustment instructions MUST BE PRECISE, explicitly stating exactly WHAT needs to be EDITED and also explicitly providing a NEGATIVE prompt clearly stating explicitly WHAT SHOULD NOT BE EDITED OR CHANGED in the image.

Explicitly respond clearly and explicitly ONLY with the structured JSON response format explicitly defined in your instructions, strictly following ALL explicit inspection criteria, constraints, and requirements."""
    
    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": [
            {"type": "text", "text": user_prompt},
            {
                "type": "image_url",
                "image_url": {
                    "url": f"data:image/jpeg;base64,{image}",
                    "detail": "high"
                }
            }
        ]}
    ]

    tools = [
        {
            "type": "function",
            "function": {
                "name": "inspect_generated_image",
                "description": (
                    "Inspect the generated image and decide if it accurately represents the assignment. "
                    "If it fails, specify if it can be fixed by simple editing (e.g., removing text or a number), or if it must be regenerated from scratch. "
                    "If editing is possible, provide clear editing instructions."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "image_passed": {
                            "type": "boolean",
                            "description": "True if the image passes all requirements; false if it does not.",
                        },
                        "reasoning": {
                            "type": "string",
                            "description": "Clear, concise explanation justifying the chosen option, explicitly referencing the assignment scenario, standard, and grade level."
                        },
                        "editable": {
                            "type": "boolean",
                            "description": "True if the image can be fixed with a simple edit; false if it must be regenerated.",
                        },
                        "adjustment_instructions": {
                            "type": "string",
                            "description": "If editable, instructions for how to fix the image. Leave blank if not editable or if the image passed.",
                        }
                    },
                    "required": ["image_passed","reasoning", "editable", "adjustment_instructions"]
                }
            }
        }
    ]

    response = make_openai_langfuse(messages=messages, tools=tools, model="gpt-4.1-nano", temperature=0.7)
    logger.info("response: %s", response)
    return response

def edit_image(image_base64, inspection ,assignment):
    """
    Edits the image according to the instruction.
    Returns the edited image as base64.
    """
    logger.info("editing image")
    task = assignment.get("task", "")
    scenario = assignment.get("scenario", "")
    answer_key= assignment.get("answerKey", "")
    edit_prompt = f"""
    You are an expert educational image editor.

    **ROLE:**  
    You specialize in precisely editing visual educational content for children, especially math-related imagery used in learning platforms.

    **INPUT:**  
    The image provided represents a math learning scene.  and it does not meet the requirements because of {inspection['reasoning']}. You must apply the following edit:
    - Edit Instruction: {inspection['adjustment_instructions']}
    Assignment Context:
    Scenario:
    {scenario}

    Task:
    {task}

    Answer Key (for STRICT negative checking explicitly):
    {answer_key}

    **STEPS (Editing Constraints):**  
    - Perform ONLY the specified edit. Do not modify anything else in the image.
    - Retain the original style, color scheme, proportions, layout, character positioning, and visual theme.
    - Ensure the image remains age-appropriate, visually clear, and free of any unintended alterations.
    - Do NOT add any numbers, text, equations, or answers unless explicitly asked.
    - If the instruction requires **removing** an element (e.g., number or label), erase it cleanly so the area looks natural and untouched.
    - If the instruction requires **adding** or **modifying** visual elements (e.g., shapes, objects), match the existing art style exactly.
    - Maintain symmetry and object alignment if applicable to the original image structure.

    **EXPECTATION:**  
    The final output should look exactly like the original except for the requested change. A viewer should not be able to tell the image was edited aside from the intended modification.

    Generate only the edited image. Do not add annotations, borders, or any overlays.
    """

    # messages = [
    #     {"role": "user", "content": [
    #         {"type": "text", "text": edit_prompt},
    #         {
    #             "type": "image_url",
    #             "image_url": {
    #                 "url": f"data:image/jpeg;base64,{image_base64}",
    #                 "detail": "high"
    #             }
    #         }
    #     ]}
    # ]
    image = edit_image_using_gpt(edit_prompt,image_base64, model="gpt-image-1")
    #logger.info(f"image:{image}")
    return image

def generate_direct_image(assignment, decision, grade_level):
    prompt = f"""
    You are an expert creator of educational images used to visually enhance math assignments.

    Scenario:
    {assignment.get('scenario', '')}

    Task:
    {assignment.get('task', '')}
    
    Description for the image:
    {decision.get('description', '')}

    Important (What your image SHOULD clearly include):
    - Clearly visualize the MAIN MATHEMATICAL FOCUS described in the "Task".
    - Visual depiction should clearly represent the provided real-world "Scenario".
    - Keep visuals engaging, realistic, student-friendly, and suitable for {grade_level}.
    - Use clear graphical elements (such as arrows, dotted lines, motion paths, measurement indicators, stages) to visually illustrate and emphasize math-related concepts explicitly described in the "Task".

    Negative constraints (DO NOT include):
    - NO numeric answers, numeric labels, or solution hints directly or indirectly related to the provided AnswerKey.
    - NEVER visually depict explicit or implicit numeric calculations provided below.
    
    AnswerKey:
    {assignment.get('answerKey', '')}
    """
    image = create_image_using_gpt(prompt, model="gpt-image-1")
    return image