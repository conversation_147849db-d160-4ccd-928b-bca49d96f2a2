import pytest
from unittest.mock import patch, MagicMock

# ✅ SUCCESS TEST
@patch("service.common_agents.working_style_agent.make_openai_langfuse")
@patch("service.common_agents.working_style_agent.langfuse.get_prompt")
@patch("service.common_agents.working_style_agent.contingent_working_style")
def test_identify_working_style_inclusion_success(mock_contingent_style, mock_get_prompt, mock_make_openai):
    from service.common_agents.working_style_agent import identify_working_style_inclusion

    # Test input
    assignment = {"title": "Group Project", "description": "Work collaboratively on a science project."}
    working_style = "collaborative"
    assignment_type = "homework"

    # Mock contingent style transformation
    mock_contingent_style.return_value = "collaborative"

    # Mock Langfuse prompt
    mock_prompt = MagicMock()
    mock_prompt.compile.return_value = "compiled_prompt"
    mock_prompt.config = {
        "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
    }
    mock_get_prompt.return_value = mock_prompt

    # Mock OpenAI response
    mock_make_openai.return_value = {
        "supports_working_style": True,
        "reasoning": "The assignment encourages collaboration.",
        "next_steps": "Ensure group roles are clear."
    }

    result = identify_working_style_inclusion(assignment, working_style, assignment_type, nb=1)
    assert result["supports_working_style"] is True
    assert "reasoning" in result


# ❌ FAILURE TEST – Simulate prompt compilation failure
@patch("service.common_agents.working_style_agent.make_openai_langfuse")
@patch("service.common_agents.working_style_agent.langfuse.get_prompt")
@patch("service.common_agents.working_style_agent.contingent_working_style")
def test_identify_working_style_inclusion_failure(mock_contingent_style, mock_get_prompt, mock_make_openai):
    from service.common_agents.working_style_agent import identify_working_style_inclusion

    assignment = {"title": "Reading Task", "description": "Read and summarize a passage."}
    working_style = "independent"
    assignment_type = "homework"

    # Simulate prompt error
    mock_contingent_style.return_value = "independent"
    mock_prompt = MagicMock()
    mock_prompt.compile.side_effect = Exception("Prompt failed")
    mock_get_prompt.return_value = mock_prompt

    result = identify_working_style_inclusion(assignment, working_style, assignment_type, nb=1)
    assert result == {"adjust_working_style": False}
