from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.kindergarten_adjustments_agent import kindergarten_adjustment_agent
from service.context_pipeline import logger

def kindergarten_adjustment_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles adjusting assignments for kindergarten students.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting kindergarten_adjustment_node")

    # Extract required inputs from state
    json_response = state.assignment
    tools = state.tools
    student = state.student.dict()
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type

    # Call the core agent functionality
    try:
        content = kindergarten_adjustment_agent(
            json_response=json_response,
            student=student,
            nb=nb,
            tools=tools,
            assignment_type=assignment_type
        )
        state.assignment = content
        state.adjustments_done["kindergarten"] = True
        logger.info("Completed kindergarten_adjustment_node successfully")
    except Exception as e:
        logger.error(f"Error in kindergarten_adjustment_node: {e}")
        state.assignment = json_response  
        state.adjustments_done["kindergarten"] = False  

    return state
