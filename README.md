# UniqLearnAI - Generate Assignment

## Overview

The project is set up to handle HTTP requests to generate unique assignments for students. It utilizes Google Cloud Functions to process incoming requests and produce a markdown template for the assignment.

### Usage

This function can be deployed as an endpoint in as a google cloud function, allowing educators to generate tailored assignments for their students by providing the necessary parameters.

## Quick Start Guide!

1. **API Endpoint**:

   - Use the provided API endpoint for making the POST request.
   - https://us-central1-bubbly-granite-412822.cloudfunctions.net/generate_assignment

2. **POST Request Using curl**:

   - Execute the following `curl` command in your terminal to make a POST request:
     ```bash
     curl -X POST 'https://us-central1-bubbly-granite-412822.cloudfunctions.net/generate_assignment' \
     -H 'Content-Type: application/json' \
     -d '{
         "first_name": "<PERSON>",
         "last_name": "Doe",
         "working_style": "Analytical",
         "interests": "math",
         "reading_level": "5th",
         "difficulty": "Medium",
         "strengths": "math",
         "state_standard": "NewYorkNextGenerationMathStandards",
         "standard":{ "common_core_standard": "Solve problems involving addition and subtraction within 100.", "domain": "Operations and Algebraic Thinking", "standard_code": "3.OA.A.1", "cluster_statement": "Solve problems involving addition and subtraction within 100." },
         "grade": "fourth",
         "nb_of_questions": 5,
         "assignment_type": "math_story",
         "add_info": "optional_additional_info"
         "session_id": "your_session_id",
         "student_id": "optional_student_id",
         "teacher_id": "optional_teacher_id"
     }'
     ```
   - Replace `'YOUR_API_ENDPOINT'`, `'your_session_id'`, `'optional_student_id'`, and `'optional_teacher_id'` with the actual values.

3. **Parameters**:
   - Adjust the JSON data payload as needed for your specific use case.

This `curl` command will send a POST request to the specified API endpoint with the provided JSON data. Make sure to replace the placeholder values with actual data relevant to your application. 😊

### Constraints

Certain inputs should follow a predefined set of values:

#### Assignment types
- math_story
- math_word_problem
- math_partner
- math_home_dynamic
- math_fact
- math_worked_example
- reading_comp_gen
- reading_comp_prov
- inf_essay
- arg_essay
- vocab_fill
- civics_analogy
- civics_critical
- civics_vocab
- civics_fact

#### Difficulty

- Easy
- Medium
- Hard

#### Working Style

- Analytical
- Amiable
- Driver
- Expressive

#### Assignment types
- math_story
- math_word_problem
- math_partner
- math_home_dynamic
- math_fact
- math_worked_example
- reading_comp_gen
- reading_comp_prov
- inf_essay
- arg_essay
- vocab_fill

#### Reading Level

- 1st
- 2nd
- 3rd
- 4th
- 5th
- 6th
- 7th
- 8th
- 9th
- 10th
- 11th
- 12th

#### Additional Info
Additional info is currently required for the following assignment_types:
- reading_comp_prov : Expected input string
- inf_essay : Expected input string
- arg_essay : Expected input string

## Cloudbuild

This `cloudbuild.yaml` file is used to define the steps required to deploy a Google Cloud Function using Google Cloud Build.

### Steps Breakdown

- **Step 1: Deploy Function**
  - **Builder**: `gcr.io/cloud-builders/gcloud`
    - This builder image is used to execute `gcloud` commands.
  - **Arguments**:
    - `functions`: Specifies that the command will be related to Cloud Functions.
    - `deploy`: Command to deploy the function.
    - `generate_assignment`: The name of the Cloud Function to deploy.
    - `--source=.`: Sets the source code location to the current directory.
    - `--trigger-http`: Creates an HTTP trigger for the Cloud Function.
    - `--runtime=python39`: Sets the runtime to Python 3.9.

### SAMPLE INPUT:
```json
{
  "first_name": "John",
  "last_name": "Doe",
  "working_style": "Analytical",
  "interests": "math",
  "reading_level": "5th",
  "difficulty": "Medium",
  "strengths": "math",
  "state_standard": "New York Next Generation Math Standards",
  "standard": {
    "topic": "Geometry",
    "standard_code": "6.G.1",
    "common_core_standard": "Find the area of right triangles, other triangles, special quadrilaterals, and polygons by composing into rectangles or decomposing into triangles and other shapes; apply these techniques in the context of solving real-world and mathematical problems."
  },
  "grade": "fourth",
  "nb_of_questions": "3",
  "session_id": "testid",
  "assignment_type": "math_story"
}
```

### SAMPLE OUTPUT:
```json
{
    "data": {
        "homework": {
            "challenges": [
                {
                    "answerKeys": "The area of the right triangle is 24 square meters. (Area = 0.5 * base * height = 0.5 * 6 * 8 = 24)",
                    "description": "The right triangle has a base of 6 meters and a height of 8 meters.",
                    "index": "1",
                    "scenario": "John entered the Enchanted Forest and saw a magic wall. To pass, he needed to find the area of a right triangle on the wall.",
                    "task": "Find the area of the right triangle to pass through the wall. Show your work.",
                    "title": "The Enchanted Forest"
                },
                {
                    "answerKeys": "The area of the rectangle is 50 square meters. (Area = length * width = 10 * 5 = 50)",
                    "description": "The rectangle has a length of 10 meters and a width of 5 meters.",
                    "index": "2",
                    "scenario": "After the forest, John reached the Giant's Gate. It was locked. To open it, he needed to find the area of a rectangle.",
                    "task": "Find the area of the rectangle to open the gate. Show your work.",
                    "title": "The Giant's Gate"
                },
                {
                    "answerKeys": "The area of the triangle is 31.5 square meters. (Area = 0.5 * base * height = 0.5 * 7 * 9 = 31.5)",
                    "description": "The triangle has a base of 7 meters and a height of 9 meters.",
                    "index": "3",
                    "scenario": "Beyond the gate, John found a hidden cave. Inside, he saw a triangle on the wall. To go further, he needed to find its area.",
                    "task": "Find the area of the triangle to reveal the secret passage. Show your work.",
                    "title": "The Hidden Cave"
                }
            ],
            "conclusion": "After solving the last problem, the secret passage opened. John found the hidden treasure because of his math skills.",
            "estimatedCompletionTime": "30 minutes",
            "instructions": [
                "Read each scenario.",
                "Solve the math problem.",
                "Write down your answers and show your work.",
                "Check your answers."
            ],
            "introduction": "John was a brave boy who loved math. One day, he found an old map in his attic. The map showed a path to a hidden treasure. To find the treasure, John had to solve some math problems. Each problem would bring him closer to the treasure. Excited, John started his journey.",
            "reflection": "How did solving these math problems help you in the adventure? Why is it important to be accurate in math?",
            "title": "John's Geometry Adventure"
        }
    },
    "input_tokens": 8224,
    "model_name": "gpt-4o-2024-05-13",
    "output_tokens": 3227,
    "prompt": "Create a captivating, story-driven math adventure for a student named John, who is in fourth grade. This math adventure will align with the standard: 6.G.1 - Find the area of right triangles, other triangles, special quadrilaterals, and polygons by composing into rectangles or decomposing into triangles and other shapes; apply these techniques in the context of solving real-world and mathematical problems., from the New York Next Generation Math Standards.\n            (1) Difficulty: The story will unfold in a series of math challenges, each with a Medium difficulty level, appropriate for John's grade level, fourth grade.Difficulty level: Medium\nDescription: Questions that involve multiple steps or a deeper understanding of concepts.\nExamples: Solve for x in the quadratic equation x^2 - 7x + 10 = 0 using the quadratic formula., Given the function f(x) = 2x + 3, find f(5) and then use the result to solve 2y - 9 = f(5).. These examples illustrate the expected level of complexity in questions related to the mathematical concepts covered. Adjust the numbers, scenarios, and problem structure according to the specific topic and educational standards of the assignment. Keep in mind that the difficulty level should be adapted based on the topic and the student's grade level.The goal is to weave these types of math challenges seamlessly into the storyline. It is important that the use of the math in the story makes sense; for example, you can't have a fraction of a person or have negative points in a sports game.\n            Don't let the interests or story line change the focus of the math topic. Also, do not solve the problem for the student in the story, or provide them a step-by-step of how to do it; let\n            them solve the problem on their own. \nStart with simpler math problems and gradually introduce more complex scenarios as John progresses in the story. For instance, early challenges might involve basic {'topic': 'Geometry', 'standardCode': '6.G.1', 'commonCoreStandard': 'Find the area of right triangles, other triangles, special quadrilaterals, and polygons by composing into rectangles or decomposing into triangles and other shapes; apply these techniques in the context of solving real-world and mathematical problems.'}, with \n            later challenges requiring him to solve complex problems on {'topic': 'Geometry', 'standardCode': '6.G.1', 'commonCoreStandard': 'Find the area of right triangles, other triangles, special quadrilaterals, and polygons by composing into rectangles or decomposing into triangles and other shapes; apply these techniques in the context of solving real-world and mathematical problems.'} to advance the plot. This progression should mimic the rising tension in the narrative, making each problem crucial to the storyline's advancement. Math challenges should be embedded within \n            the story in a manner that their resolution feels essential to the storyline. \n            Ensure that solving each problem builds on the knowledge or outcome of the previous one, creating a connected and coherent story-driven math journey. This linkage emphasizes learning through accumulation of knowledge and skills. \n            \n (2) Engagement: The adventure should be immersive, encouraging John to interact with the story through drawing, dialogue choices, and manipulation of story-related objects. Each math problem should be multi-step\n            and integrated into the story's events or character decisions.\n (3) Remember, this activity is designed to be completed within 30 minutes by John. Balance the number and complexity of problems accordingly.\n            Furthermore, the story and challenges should reflect and engage with all aspects of John's learning profile, ensuring the activity is not only educational but also deeply engaging and personally relevant to them.\n            \n The final output should read like a short story, with problems embedded within. The story's conclusion should align with solving the final math challenge, providing a sense of accomplishment and narrative closure.Refer to the following **readability and complexity guidelines** when creating the text: {('1', '2'): {'description': 'Focus on simple sentences and basic vocabulary. The sentences should be short, typically 5-7 words, using common nouns and action verbs. Stick to concepts familiar to their everyday life.', 'examples': ['The cat is big.', 'I like red apples.'], 'keywords': ['basic', 'simple', 'everyday']}, ('3', '4'): {'description': 'You can introduce compound sentences and slightly more complex vocabulary. Sentences can extend to 7-10 words. Introduce new, straightforward concepts in a clear context to aid comprehension.', 'examples': ['She plays soccer and sings.', 'The dog ran quickly to the park.'], 'keywords': ['compound sentences', 'straightforward']}, ('5', '6'): {'description': 'Start using complex sentences with conjunctions and relative clauses. Introduce technical terms relevant to their studies but provide clear definitions and explanations. Sentence length can range from 10-15 words.', 'examples': ['The teacher, who is very kind, helps us.', 'If it rains, we will stay inside.'], 'keywords': ['complex sentences', 'conjunctions', 'relative clauses']}, ('7', '8'): {'description': 'Use more varied sentence structures and introduce abstract concepts that require inference. Vocabulary can be more sophisticated, covering a broader range of subjects including science and humanities. Sentences can be around 15-20 words.', 'examples': ['Despite the rain, the event continued as planned.', 'He considered the problem, but his solution was flawed.'], 'keywords': ['varied structures', 'abstract concepts', 'inference']}, ('9', '10'): {'description': 'Encourage critical thinking with texts that offer multiple viewpoints or arguments. Utilize advanced vocabulary and more complex syntactic structures, such as passive voice or subjunctive mood. Sentences can be more than 20 words.', 'examples': ['If I were the president, I would prioritize education.', 'The book was read by the entire class.'], 'keywords': ['critical thinking', 'subjunctive mood', 'passive voice']}, ('11', '12'): {'description': 'Prepare students for college-level reading with highly complex texts that use specialized academic or technical vocabulary. Texts should challenge their understanding and encourage analytical thinking. Sentences can be complex and lengthy, often exceeding 25 words.', 'examples': ['The juxtaposition of opposing ideals exacerbates societal tensions.', 'Understanding the nuances of quantum mechanics requires sophisticated analytical skills.'], 'keywords': ['specialized vocabulary', 'analytical thinking', 'complex']}}Again, this is just an example. The math concept in the examples should be strictly adhered to, but every question does not need to use these numbers, but rather should be representative of that level of complexity. While still remaining aligned with the standard,\n            tailor this activity to John’s learning profile. Remember, the goal is to create an engaging, interactive learning experience that goes beyond pencil-and-paper tasks.\n            \n \"To achieve this, you will use specific **readability and complexity guidelines** given above. These guidelines will help maintain the focus on suitable sentence structures and vocabulary, ensuring that the content is both accessible and challenging for John.\"\n            \n(3)Time Consideration: The worksheet should be completable within 30 minutes, considering the number and complexity of problems relative to John's capabilities.\n            \n(4)Interests Integration: Weave in John's interest in math into the problems, using specific names, places, records, or events familiar within that interest sphere. The more relevant information you can use about\n            the given interest, the better. Use the interests to make the math make more sense, don't just use it arbitrarily. Ensure the math remains the focal point, with the interest serving to enhance engagement and relevance.\n            \n(5)Working Style Adaptation: Tailor questions to an Analytical working style.Characteristics of the analytical working style include: Concerned with aggressive approaches, Think logically, Seek data, Need to know the process, Utilize caution, Prefer to do things themselves, Want others to notice their accuracy, Gravitate toward quality control, Avoid conflict, Need to be right, Like to contemplate.Considering their analytical working style, may be prompted to organize something or a situation, given their inclination for organization and structure. This could also mean that they are simply provided with problems or activities that are already well-organized and structured. They like details, so ensure that problems include the intricate details of contexts. Refrain from using general nouns and statements, and lean more towards using specifics. They are goal-oriented, so make sure that there is a CLEAR and CONCISE goal for each problem and the assignment/activity as a whole. Lastly, they enjoy accuracy and data collection, so try to incorporate finding data from the internet or using any sort of metrics to ensure accuracy. Remember, you are generating things for young students, so the extent to which they can do all of these things is limited. Do not attempt to use every one of these methods in every question. These are just examples of how you should tailor assignments based on their working style. While it is important to incorporate a student's working style, do not do it at the expense of the integrity of the mathematical concept. Use these examples where they make sense, do not force it. And, don't make the whole problem about the working style beause remember, there are other aspects of the student's profile that need to be embedded.\n(6)Character Strengths Utilization: Incorporate math character strengths subtly into the problems, prompting John to employ these strengths naturally within the context of solving the math problems.\n(7)Reading Level Adjustment: Craft the wording of each problem to align with 5th reading level, using vocabulary and sentence structures that are accessible yet challenging enough to stimulate learning and engagement.Use the following examples as references for\n            how complex the vocabulary and sentence structure should be: \n'Aidan looks at the clock and watches 5 seconds go by. How far can each player run?', 'In math, Aidan learns about multiplication. He uses it to work out how far the players run.', 'Player 1 is quick, moving 7 yards in a second. How far does he travel in 5 seconds?', 'Player 5 speeds along at 35 yards in one second. Aidan works out his distance after 5 seconds.', 'Aidan keeps a chart to track how fast and far each player goes.', 'Aidan knows that to win, it's important to understand how speed and distance work together.', 'Aidan gets better at this by doing his math homework, calculating distances for his team.'\nRemember, these are just examples of the vocabulary, sentence structure, and general complexity. Do not use the words or characters or anything like that from these examples; and don't try to match the style or tone from these examples. It is just to show you vocab and sentence structure. Thank you!\n\n            This approach ensures that each math problem on the worksheet not only serves an educational purpose but also resonates with John's personal interests, working style, and character strengths, making the learning experience both effective and enjoyable.\n            Can you label above each question which profile factors you used for that specific question.\n            \nLet's think step by step to output the optimal assignment.",
    "session_id": "testid",
    "student_id": "",
    "teacher_id": "",
    "template": "<img src='https://images.squarespace-cdn.com/content/v1/652f26bf3e374b08a0673416/d537ba63-e924-455a-b5d8-e6772dccecc0/UniqLearn_logo_transparent+black.png?format=1500w' alt='logo' width='200'/> \n\n# John's Geometry Adventure \n\n## Instructions \n- Read each scenario. \n- Solve the math problem. \n- Write down your answers and show your work. \n- Check your answers. \n\n## Introduction \n John was a brave boy who loved math. One day, he found an old map in his attic. The map showed a path to a hidden treasure. To find the treasure, John had to solve some math problems. Each problem would bring him closer to the treasure. Excited, John started his journey. \n\n---\n### The Enchanted Forest\nJohn entered the Enchanted Forest and saw a magic wall. To pass, he needed to find the area of a right triangle on the wall.\n\n#### Challenge 1\n\nThe right triangle has a base of 6 meters and a height of 8 meters.\n\n**Task:** Find the area of the right triangle to pass through the wall. Show your work.\n\n#### Answer Area for question 1: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n---\n### The Giant's Gate\nAfter the forest, John reached the Giant's Gate. It was locked. To open it, he needed to find the area of a rectangle.\n\n#### Challenge 2\n\nThe rectangle has a length of 10 meters and a width of 5 meters.\n\n**Task:** Find the area of the rectangle to open the gate. Show your work.\n\n#### Answer Area for question 2: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n---\n### The Hidden Cave\nBeyond the gate, John found a hidden cave. Inside, he saw a triangle on the wall. To go further, he needed to find its area.\n\n#### Challenge 3\n\nThe triangle has a base of 7 meters and a height of 9 meters.\n\n**Task:** Find the area of the triangle to reveal the secret passage. Show your work.\n\n#### Answer Area for question 3: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n## Conclusion \nAfter solving the last problem, the secret passage opened. John found the hidden treasure because of his math skills. \n\n## Reflection \nHow did solving these math problems help you in the adventure? Why is it important to be accurate in math? \n\n**Estimated Completion Time:** 30 minutes \n",
    "template_before_recurse": "<img src='https://images.squarespace-cdn.com/content/v1/652f26bf3e374b08a0673416/d537ba63-e924-455a-b5d8-e6772dccecc0/UniqLearn_logo_transparent+black.png?format=1500w' alt='logo' width='200'/> \n\n# John's Geometry Adventure \n\n## Instructions \n- Read each scenario carefully. \n- Solve the math challenge based on the given description. \n- Write down your answers and show your work. \n- Check your answers to ensure they are correct. \n\n## Introduction \n John was an adventurous boy who loved math and solving puzzles. One day, he found a mysterious map in his attic. The map led to an ancient treasure hidden in a secret location. To find the treasure, John had to solve a series of math challenges. Each challenge would bring him closer to the treasure. Excited by the adventure, John set off on his journey. \n\n---\n### The Enchanted Forest\nJohn entered the Enchanted Forest, where he encountered a magical barrier. To pass through, he needed to find the area of a right triangle that was etched into the barrier.\n\n#### Challenge 1\n\nThe right triangle has a base of 6 meters and a height of 8 meters.\n\n**Task:** Calculate the area of the right triangle to unlock the barrier.\n\n#### Answer Area for question 1: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n---\n### The Giant's Gate\nAfter passing through the Enchanted Forest, John arrived at the Giant's Gate. The gate was locked, and to open it, he needed to solve a puzzle involving a special quadrilateral.\n\n#### Challenge 2\n\nThe quadrilateral is a rectangle with a length of 10 meters and a width of 5 meters.\n\n**Task:** Find the area of the rectangle to open the Giant's Gate.\n\n#### Answer Area for question 2: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n---\n### The Hidden Cave\nBeyond the Giant's Gate, John discovered a hidden cave. Inside the cave, he found an ancient inscription that described a polygon. To proceed, John had to find the area of this polygon.\n\n#### Challenge 3\n\nThe polygon is a triangle with a base of 7 meters and a height of 9 meters.\n\n**Task:** Calculate the area of the triangle to reveal the secret passage in the cave.\n\n#### Answer Area for question 3: \n<table style='border: 2px solid black;'><thead><tr><th width='1000px' height='250px'></th></tr></thead></table> \n\n## Conclusion \nWith the final challenge solved, the secret passage opened, revealing the hidden treasure. John had successfully completed his adventure and discovered the ancient treasure, all thanks to his math skills. \n\n## Reflection \nHow did solving these math problems help you progress through the adventure? \n\n**Estimated Completion Time:** 30 minutes \n"
}
```
