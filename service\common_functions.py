from service.context_pipeline import logger
import re

guidelines = {
    ('','kindergarten') : {
        "description": "Introduce basic concepts with very simple sentences. Focus on building foundational skills like counting, identifying shapes, and recognizing letters.",
        "examples": [
            "<PERSON> is happy. How many apples does he have?",
            "<PERSON> sees a red ball. What shape is it?",
            "<PERSON> has 3 pencils. How many more does he need to make 5?",
            "<PERSON> likes the colour yellow"
        ],
        "keywords": ["basic", "simple", "everyday"]
    },
    ('1', '2'): {
        "description": "Focus on simple sentences and basic vocabulary. The sentences should be short, typically 5-7 words, using common nouns and action verbs. Stick to concepts familiar to their everyday life.",
        "examples": [
            "<PERSON>'s cat jumps far. How many times can it jump in 4 tries?",
            "<PERSON> sees rain in a cup. How much rain in 2 hours?",
            "<PERSON> claps with the music. How many claps in 10 seconds?",
            "<PERSON> sees apples on trees. How many apples are there?",
            "<PERSON> has books on a shelf. How many books on 3 shelves?"
        ],
        "keywords": ["basic", "simple", "everyday"]
    },
    ('3', '4'): {
        "description": "You can introduce compound sentences and slightly more complex vocabulary. Sentences can extend to 7-10 words. Introduce new, straightforward concepts in a clear context to aid comprehension.",
        "examples": [
            "<PERSON> counts 5 apples in her basket, then adds 3 more. Can you tell how many apples she has in total?",
            "<PERSON> owns 4 toy cars. If he gets 2 more as gifts, can you calculate his new total of toy cars?",
            "<PERSON> bakes 6 cupcakes. If she decides to eat 1, how many cupcakes does she have left?",
            "<PERSON> reads 2 books every week. How many books does he read after 4 weeks?",
            "<PERSON> has a collection of 10 pencils. If she gives 2 pencils to her friend, how many pencils does she have now?"
        ],
        "keywords": ["compound sentences", "straightforward"]
    },
    ('5', '6'): {
        "description": "Start using complex sentences with conjunctions and relative clauses. Introduce technical terms relevant to their studies but provide clear definitions and explanations. Sentence length can range from 10-15 words.",
        "examples": [
            "During his art project, Leo carefully measures to draw lines exactly 6 inches long. He wonders about the number of 2-inch sections he can divide these lines into.",
            "The library is 5 blocks from Tom's house. He's trying to figure out the total number of blocks he would walk if he goes there and back over the course of 4 days.",
            "Mia's recipe requires 3/4 cup of sugar. She's doubling the recipe and needs to know the total amount of sugar necessary.",
            "If you read 10 pages of a book each day, how many pages will you have read in 2 weeks?",
            "The school has 4 classes, each with 20 students. How many students are there in total?"
        ],
        "keywords": ["complex sentences", "conjunctions", "relative clauses"]
    },
    ('7', '8'): {
        "description": "Use more varied sentence structures and introduce abstract concepts that require inference. Vocabulary can be more sophisticated, covering a broader range of subjects including science and humanities. Sentences can be around 15-20 words.",
        "examples": [
            "Despite the rain, the event continued as planned. Calculate how many people attended if the venue can hold 200 people and there were 50 seats left.",
            "He considered the problem, but his solution was flawed. Explain why his approach didn't work based on the given data.",
            "The hypothesis that the scientist proposed was proven correct after several experiments. Describe how these experiments supported the hypothesis.",
            "The book explores themes of identity and belonging in a complex narrative. Discuss how these themes are presented through the characters' experiences.",
            "Understanding the chemical reactions in photosynthesis requires knowledge of biology and chemistry. Illustrate how these subjects intersect in the process of photosynthesis."
        ],
        "keywords": ["varied structures", "abstract concepts", "inference"]
    },
    ('9', '10'): {
        "description": "Encourage critical thinking with texts that offer multiple viewpoints or arguments. Utilize advanced vocabulary and more complex syntactic structures, such as passive voice or subjunctive mood. Sentences can be more than 20 words.",
        "examples": [
            "If I were the president, I would prioritize education. Debate whether this is a feasible policy and what impact it might have on the country.",
            "The book was read by the entire class, and it sparked a lively discussion. Analyze how the book's themes contributed to the class discussion.",
            "Considering both sides of the argument, it becomes clear that there is no easy solution. Evaluate the strengths and weaknesses of each argument presented.",
            "The experiment was conducted to determine the effects of temperature on enzyme activity. Summarize the results and their implications for biochemical processes.",
            "The novel's protagonist faces numerous challenges that force them to grow and change. Discuss how these challenges contribute to the protagonist's development."
        ],
        "keywords": ["critical thinking", "subjunctive mood", "passive voice"]
    },
    ('11', '12'): {
        "description": "Prepare students for college-level reading with highly complex texts that use specialized academic or technical vocabulary. Texts should challenge their understanding and encourage analytical thinking. Sentences can be complex and lengthy, often exceeding 25 words.",
        "examples": [
            "The juxtaposition of opposing ideals exacerbates societal tensions. Analyze how this concept is illustrated in the context of historical events.",
            "Understanding the nuances of quantum mechanics requires sophisticated analytical skills. Describe the key principles of quantum mechanics and their implications for modern physics.",
            "The research paper presents a detailed analysis of economic models. Summarize the main arguments and evaluate their relevance to current economic theories.",
            "In the philosophical debate about ethics, the concept of moral relativism is explored. Discuss how moral relativism challenges traditional ethical frameworks.",
            "The study on climate change outlines various mitigation strategies. Assess the effectiveness of these strategies in reducing global warming based on the provided data."
        ],
        "keywords": ["specialized vocabulary", "analytical thinking", "complex"]
    }
}

def validate_inputs(working_style, difficulty):
    """
    Validate the working style and difficulty level inputs to ensure they are within the specified range.

    Args:
    working_style (str): The working style of the student.
    difficulty (str): The difficulty level of the assignment.

    Returns: None
    In case of invalid inputs, a ValueError is raised.
    """
    # Define the valid working styles and difficulty levels
    valid_working_styles = ['analytical', 'amiable', 'driver', 'expressive']
    valid_difficulty_levels = ['easy', 'medium', 'hard']

    # Check if the working style is valid
    if working_style.lower() not in valid_working_styles:
        raise ValueError(f"Invalid working style: {working_style}. Choose from {', '.join(valid_working_styles)}.")

    # Check if the difficulty level is valid
    if difficulty.lower() not in valid_difficulty_levels:
        raise ValueError(f"Invalid difficulty level: {difficulty}. Choose from {', '.join(valid_difficulty_levels)}.")

def difficulty_examples(assignment_difficulty):
    # Lowercase the difficulty level for consistent comparison
    assignment_difficulty_lower = assignment_difficulty.lower()

    # Provide detailed explanations for each difficulty level
    difficulty_descriptions = {
        'easy': "Questions that require basic understanding and direct application of concepts.",
        'medium': "Questions that involve multiple steps or a deeper understanding of concepts.",
        'hard': "Complex questions that challenge analytical skills and comprehensive understanding."
    }

    # Examples dictionary mapping difficulty levels to example problems
    examples = {
        'easy': [
            "Solve for x in the equation x^2 + 5x + 6 = 0 by factoring.",
            "Calculate the area of a rectangle with length 5 units and width 3 units."
        ],
        'medium': [
            "Solve for x in the quadratic equation x^2 - 7x + 10 = 0 using the quadratic formula.",
            "Given the function f(x) = 2x + 3, find f(5) and then use the result to solve 2y - 9 = f(5)."
        ],
        'hard': [
            "Prove that the sum of the squares of any two sides of a right triangle is equal to the square of the hypotenuse.",
            "Given a polynomial p(x) = 4x^3 - 3x^2 + 7x - 5, find the roots using synthetic division and confirm them by substituting back into the polynomial."
        ]
    }

    # Check if the difficulty level exists in the dictionary
    if assignment_difficulty_lower not in examples:
        return "Error: The specified difficulty level is not recognized. Please choose from 'easy', 'medium', or 'hard'."

    # Construct the response message with guidelines for adaptation
    response = (
        f"Difficulty level: {assignment_difficulty_lower.title()}\n"
        f"Description: {difficulty_descriptions[assignment_difficulty_lower]}\n"
        f"Examples: {', '.join(examples[assignment_difficulty_lower])}. These examples illustrate the expected level of complexity in questions related to the mathematical concepts covered. Adjust the numbers, scenarios, and problem structure according to the specific topic and educational standards of the assignment. Keep in mind that the difficulty level should be adapted based on the topic and the student's grade level."
    )
    return response

def track_closest_assignment(tracked_data, reading_level_int):
    """
    Identifies the assignment from the tracked data that has the closest Flesch-Kincaid grade level to the student's reading level.

    Args:
    tracked_data (list of dict): A list where each entry contains 'assignment' and 'grade' keys.
                                 'assignment' is the assignment text, and 'grade' is the Flesch-Kincaid grade level of that assignment.
    reading_level_int (int): The student's current reading level as an integer.

    Returns:
    str: The assignment with the closest Flesch-Kincaid grade level to the student's reading level.
    """
    # Find the assignment with the closest Flesch-Kincaid grade level to the student's reading level
    closest_assignment = min(tracked_data, key=lambda x: abs(x['grade'] - reading_level_int))
    logger.info(f"Tracked data: {tracked_data}")
    logger.info(f"Closest value used: {closest_assignment['assignment']}")
    return closest_assignment['assignment']

def contingent_working_style(workstyle_sent):
    """
    Returns a tailored description of how to modify assignments based on the student's working style.

    Args:
    workstyle_sent (str): The working style of the student, which can be 'analytical', 'amiable', 'driver', or 'expressive'.

    Returns:
    str: A detailed explanation of how assignments should be adjusted to suit the specified working style. Each style
         includes characteristics of the working style and suggestions for incorporating them into assignments.
    """
    workstyle_lower = workstyle_sent.lower()
    if workstyle_lower == 'analytical':
      return "Characteristics of the analytical working style include: Concerned with aggressive approaches, Think logically, Seek data, Need to know the process, Utilize caution, Prefer to do things themselves, Want others to notice their accuracy, Gravitate toward quality control, Avoid conflict, Need to be right, Like to contemplate." \
             "Considering their analytical working style, may be prompted to organize something or a situation, given their inclination for organization and structure. " \
             "This could also mean that they are simply provided with problems or activities that are already well-organized and structured. " \
             "They like details, so ensure that problems include the intricate details of contexts. " \
             "Refrain from using general nouns and statements, and lean more towards using specifics. " \
             "They are goal-oriented, so make sure that there is a CLEAR and CONCISE goal for each problem and the assignment/activity as a whole. " \
             "Lastly, they enjoy accuracy and data collection, so try to incorporate finding data from the internet or using any sort of metrics to ensure accuracy. " \
             "Remember, you are generating things for young students, so the extent to which they can do all of these things is limited. " \
             "Do not attempt to use every one of these methods in every question. These are just examples of how you should tailor assignments based on their working style. " \
             "While it is important to incorporate a student's working style, do not do it at the expense of the integrity of the concept. Use these examples where they make sense, do not force it. " \
             "And, don't make the whole problem about the working style because remember, there are other aspects of the student's profile that need to be embedded."
    elif workstyle_lower == 'amiable':
        #added new characteristics from doc Aidan sent in Slack
      return "Characteristics of the amiable working style include: Concerned with stability, Think logically, Want documentation and facts, Like personal involvement, Need to know step-by-step sequence, " \
             "Want others to notice their patient perseverance, Avoid risks/changes, Dislike conflict, Accommodate others, Look for calmness and peace, Enjoy teamwork, Want sincere feedback that they are appreciated. " \
             "Considering their amiable working style, likes close, personal relationships, so orient assignments around their relationships with their loved ones and helping them in different ways. " \
             "They are inclined to listen and support others, so frame problems as problems that their loved ones are having, and they are trying to help with. " \
             "They aren't the strongest goal-setters, so try to set an overall goal for them, and support them step-by-step in achieving it. " \
             "Lastly, they enjoy working with others, so you could prompt them to ask someone else for an input into their problem. " \
             "For example, if the standard was mulitiplication of single digit numbers the question could be 'ask your friend how many siblings they have, then multiply that number by 5.' " \
             "Remember, you are generating things for young students, so the extent to which they can do all of these things is limited. Do not attempt to use every one of these methods in every question. " \
             "These are just examples of how you should tailor assignments based on their working style. While it is important to incorporate a student's working style, do not do it at the expense of the integrity of the concept. " \
             "Use these examples where they make sense, do not force it. And, don't make the whole problem about the working style because remember, there are other aspects of the student's profile that need to be embedded."
    elif workstyle_lower == 'driver':
      return "Characteristics of the driver working style include: Concerned with being #1, Think logically, Want facts and highlights, Strive for results, Like personal choices, Like changes, Prefer to delegate, Want others to notice accomplishments, Need to be in charge, Tendency towards conflict." \
             "Considering their driver working style, may be given an assignment that is oriented around making a final decision. " \
             "For example, you could frame an addition problem as 'Student will go to the store if they have more than 12 dollars. " \
             "They have 5 dollars in their left pocket and 9 in their right. Based on that, will student go to the store? Why?' They also enjoy competition, so frame things in a competitive way whether it's with their friends, themselves, or imaginary characters within in the assignment. " \
             "Lastly, they enjoy freedom and managing themselves and others. So, in a non-negative way, prompt them to think in leader-type ways where they make decisions for a group or delegate responsibilities based on the answer to the question. " \
             "Remember, you are generating things for young students, so the extent to which they can do all of these things is limited. Do not attempt to use every one of these methods in every question. " \
             "These are just examples of how you should tailor assignments based on their working style. While it is important to incorporate a student's working style, do not do it at the expense of the integrity of the concept. " \
             "Use these examples where they make sense, do not force it. And, don't make the whole problem about the working style because remember, there are other aspects of the student's profile that need to be embedded."
    elif workstyle_lower == 'expressive':
      return "Characteristics of the expressive working style include: Concerned with approval and appearances, Seek enthusiastic people and situations, Think emotionally, Want to know the general expectations, Need involvement and people contact, Like changes and innovations, Want others to notice THEM, Often need help getting organized, Dislike conflict, Look for action and stimulation, Surround themselves with optimism, Want feedback that they “look good” ." \
             "Considering their expressive working style, can sometimes be a risk taker, so provide contexts for questions that involve risks and spontaneous actions(that are obviously age appropriate). " \
             "They are also not limited by tradition, so feel free to incorporate creative and unrealistic examples. For example, for a student interested in baseball they wouldn't mind if a problem implied that someone could hit 300 home runs in a season, although not traditional. In addition to this, they are ultra-curious, so make sure you create assignments that foster and support their pursuit of curiosities. " \
             "This may look like posing questions beyond the actual homework question, prompting them to ask questions about the concpet or context, drawing them in with a hook that entices their curiosity, etc. " \
             "They also are usually less inclined to stick with one thing for an extended period of time, so having one question be an entire story or situation that takes 10 minutes is likely less effective with them than multiple questions that takes about the same amount of time. " \
             "Lastly, they also enjoy working with others, so try to incorporate interaction with others where possible, while still maintaining that they are to complete this assignment alone. " \
             "Remember, you are generating things for young students, so the extent to which they can do all of these things is limited. Do not attempt to use every one of these methods in every question. " \
             "These are just examples of how you should tailor assignments based on their working style. While it is important to incorporate a student's working style, do not do it at the expense of the integrity of the concept. " \
             "Use these examples where they make sense, do not force it. And, don't make the whole problem about the working style because remember, there are other aspects of the student's profile that need to be embedded."
    else:
        return workstyle_lower

def reading_level_example(readinglevel_sent):
    """
    Provides sample problems or vocabulary based on the reading level sent as input.

    Args:
    readinglevel_sent (str): The reading level for which to provide examples (e.g., '1st', '2nd', '3rd', etc.).

    Returns:
    str: A string containing example problems or vocabulary suited to the specified reading level.
    """
    if readinglevel_sent in ["1st", "1", "first","01"]:
        return "'Aidan's cat jumps far. How many times can it jump in 4 tries?', 'Aidan sees rain in a cup. How much rain in 2 hours?', 'Aidan claps with the music. How many claps in 10 seconds?', 'Aidan sees apples on trees. How many apples are there?', 'Aidan has books on a shelf. How many books on 3 shelves?'"
    elif readinglevel_sent in ["2nd", "2", "second","02"]:
        return "'Aidan has apples in 2 baskets. Count all the apples.', 'Sara has 5 red balloons and 3 blue ones. Count her balloons.', 'Tom reads 4 pages in a book each day. How many pages does he read in 7 days?', 'Jenny had 10 candies. She gives 2 away to her friends. How many does she have now?', '1 box has 6 crayons. How many in 4 boxes?'"
    elif readinglevel_sent in ["3rd", "3", "third","03"]:
        return "'Sara counts 5 apples in her basket, then adds 3 more. Can you tell how many apples she has in total?', 'Tom owns 4 toy cars. If he gets 2 more as gifts, can you calculate his new total of toy cars?', 'Jenny bakes 6 cupcakes. If she decides to eat 1, how many cupcakes does she have left?', 'Alex reads 2 books every week. How many books does he read after 4 weeks?', 'Mia has a collection of 10 pencils. If she gives 2 pencils to her friend, how many pencils does she have now?'"
    elif readinglevel_sent in ["4th", "4", "fourth",'04']:
        return "Here is a list exmemplifying vocab words for 4th grade: 'Word', 'range', 'essay', 'condition', 'scientific', 'reference', 'increase', 'growth', 'convert', 'customary', 'generalization', 'acute', 'inference', 'conclude', 'role', 'comparison', 'partial', 'select', 'specific', 'basic', 'progress', 'actual', 'appropriate', 'task', 'ability', 'conduct', 'conflict', 'influence', 'persuasive', 'primary', 'audience', 'historical', 'organization', 'positive', 'precise', 'transform', 'unfamiliar', 'variety', 'vary', 'boundary', 'career', 'exist', 'narrow', 'preserve', 'publish', 'reduce', 'simplify', 'suggest', 'advertisement', 'descriptive', 'eventually', 'involve', 'prevent', 'endanger', 'error', 'examine', 'generate', 'require', 'decrease', 'differ', 'draft', 'evenly', 'inspire', 'limit', 'rate', 'beyond', 'discussion', 'forecast', 'gain', 'individual', 'original', 'prove', 'accurate', 'composite', 'convince', 'establish', 'inform', 'instruction', 'particular', 'relative', 'safely', 'argument', 'complex', 'definite', 'department', 'outline', 'prompt', 'diverse', 'extreme', 'persuade', 'quantity', 'realistic', 'benefit', 'clarify', 'comprehend', 'historic', 'production', 'similarity', 'decimal', 'ounce', 'height', 'equivalent', 'per', 'quotient', 'cm', 'range'. Now for some sentence examples.'Sam found 8 apples in one basket and another 7 in a different one. How many apples are there in all?', 'Lily possesses 10 stickers but gives 3 to her friend. How many stickers remain with Lily?', 'In the lot, there are 5 cars, and each one is equipped with 4 wheels. Can you calculate the total number of wheels present?', 'Every night, Jake dedicates time to read 2 pages of his book. How many pages will he have read after 7 nights?', 'Emma commits to saving 2 dollars each week. What will be her total savings after a period of 4 weeks?'"
    elif readinglevel_sent in ["5th", "5", "fifth"]:
        return "'Aidan looks at the clock and watches 5 seconds go by. How far can each player run?', 'In math, Aidan learns about multiplication. He uses it to work out how far the players run.', 'Player 1 is quick, moving 7 yards in a second. How far does he travel in 5 seconds?', 'Player 5 speeds along at 35 yards in one second. Aidan works out his distance after 5 seconds.', 'Aidan keeps a chart to track how fast and far each player goes.', 'Aidan knows that to win, it\'s important to understand how speed and distance work together.', 'Aidan gets better at this by doing his math homework, calculating distances for his team.'"
    elif readinglevel_sent in ["6th", "6", "sixth",'06']:
        return "'During his art project, Leo carefully measures to draw lines exactly 6 inches long. He wonders about the number of 2-inch sections he can divide these lines into.', 'The library is 5 blocks from Tom\'s house. He\'s trying to figure out the total number of blocks he would walk if he goes there and back over the course of 4 days.', 'Mia\'s recipe requires 3/4 cup of sugar. She\'s doubling the recipe and needs to know the total amount of sugar necessary.'"
    elif readinglevel_sent in ["7th", "7", "seventh", '07']:
        return "'In science, Sarah tracks the growth of her plant, measuring a 2-inch increase in the first week.', 'In art, Lucas uses a grid to scale his drawing, with each square is 5 square inches of the final piece.', 'For geography, Emma figures out the average rainfall in her city over four months. She notices that there were 10 inches in the first month.', 'At the fundraiser, Jake sells candy bars, wondering how many he could sell in three hours after selling 15 in the first hour.', 'In music, Mia times her piano practice, starting with 20 minutes on Monday and planning to double it by Friday.'"
    elif readinglevel_sent in [ "8th", "8", "eighth",'08']:
        return "'Sarah has to figure out what x equals in the math problem 3x + 5 = 20 to know x\'s value.', 'In science, Mike finds out how dense something is by using its weight and how much space it takes up.', 'Julia is making a chart to show how a plant gets taller over time, using centimeters to measure.', 'Emma is working out how much money is needed for a school event by adding the costs of all the things needed.', 'For his project on places around the world, Leo is changing kilometers into miles to see how far apart two cities are.'"
    elif readinglevel_sent in ["9th", "9", "nineth", '09']:
        return "'Aidan looked at the science experiment\'s numbers to see how fast reactions happened, timing them in seconds.', 'During math, he figured out the amount of water moving through a pipe in five minutes by solving problems.', 'He observed how fast plants grew, tracking their height increase in inches every week.', 'For his history assignment, Aidan mapped out how territories got bigger over years, turning years into seconds to see it differently.', 'In art class, he worked out how long it takes for paint layers to dry, using what he knows about speeds.'"
    elif readinglevel_sent in ["10th", "10", "tenth"]:
        return "'The physics teacher gave out a project on how things move when thrown, asking for calculations on how fast they start moving and how far they go.', 'In the chemistry lab, students have to figure out how quickly reactions happen, measured in moles per second, for their experiments.', 'For the math contest, Julia has to work through tough algebra problems that include quadratic functions.', 'In the calculus class, the homework is about finding derivatives of functions to get how things speed up or slow down.', 'The engineering club is working on a bridge design, and they need to figure out how much force the cables can hold with different weights.'"
    elif readinglevel_sent in ["11th", "11", "eleventh"]:
        return "'The coach presented a detailed diagram to illustrate each runner\'s pace in meters per minute.', 'Students were tasked to compute the total distance each runner covered in a 10-minute sprint.', 'In the geometry class, the assignment involved calculating the angle of elevation for a soccer ball kicked at an initial speed of 20 meters per second.', 'For physics homework, students calculated the acceleration of a car that accelerates from 0 to 60 miles per hour in 7 seconds.', 'Maya planned to explore the connection between the angle of a ramp and the speed of a ball rolling down for her science project.'"
    elif readinglevel_sent in ["12th", "12", "twelfth", "HS"]:
        return "'The physics teacher presented a complicated formula to figure out how fast something speeds up, which required advanced problem-solving abilities.', 'In higher-level calculus, learners delve into the idea of derivatives to grasp how quickly things change under different conditions.', 'The stats course involves dissecting data set variations, an essential ability for decoding research findings.', 'Grasping trigonometry\'s core ideas is key to tackling issues related to angles and distances in more complex math.', 'Examining geometric proofs lays the groundwork for logical thought and critical analysis in more advanced mathematical studies.', 'Using integrals in real-life scenarios pushes students to adapt theoretical ideas to practical issues.'"
    elif readinglevel_sent == 'kindergarten':
        return "'Aidan is happy. How many apples does he have?', 'Sara sees a red ball. What shape is it?', 'Tom has 3 pencils. How many more does he need to make 5?', 'Jenny likes the colour yellow'"
    else:
        logger.info(f"No reading level example found for grade: {readinglevel_sent}")

def vocab_examples(grade):
    """
    Provides grade-appropriate vocabulary examples based on the input grade level.

    Args:
    grade (str): The grade level for which vocabulary examples are required.
    Returns:
    list or tuple: A list or tuple of vocabulary sets appropriate for the given grade level.
    """
    grade = str(grade).lower()
    if grade in ["1st", "1", "first","01", "kindergarten"]:
        return lexile_vocab_g1
    elif grade in ["2nd", "2", "second","02"]:
        return lexile_vocab_g2
    elif grade in ["3rd", "3", "third","03"]:
        return texas_vocab_g3, lexile_vocab_g3
    elif grade in ["4th", "4", "fourth",'04']:
        return texas_vocab_g4, lexile_vocab_g4
    elif grade in ["5th", "5", "fifth"]:
        return lexile_vocab_g5
    elif grade in ["6th", "6", "sixth", "7th", "7", "seventh", "8th", "8", "eighth",'06','07','08']:
        return lexile_vocab_ms
    elif grade in ["9th", "9", "nineth", "10th", "10", "tenth", "11th", "11", "eleventh", "12th", "12", "twelfth", "HS",'09']:
        return lexile_vocab_hs



def character_strength_explanations(strengths):
    logger.info("this is character_strength_explanations")
    """
    Generates explanations for a list of character strengths provided in the input. Each character strength corresponds
    to a specific explanation that describes how the strength can be incorporated into educational activities or assignments.

    Args:
    strengths (str): A comma-separated string of character strengths.

    Returns:
    str: A concatenated string of explanations for each strength. If the input strength is recognized, its corresponding
    explanation is included in the result.
    """
    strength_explanations = {
    'Creativity': 'A student with the strenght of creativity could be asked to solve something in an unusual way, in addition to the traditional way of solving it. The student may be asked an open-ended question, prompting them to think of something new about the concept. The student could be asked to make a connection with how the concept connects to something in their personal life. ',
    'Curiosity': ' A student with the strength of curiosity might be given a new idea, like a random connection to a math problem, and asked to explore this new idea. For example, a student could be given a fun fact about something related to their interest and relevant to the math problem.',
    'Judgement': ' A student with the strength of judgement may be asked if the final answer is what they thought it would be, or close, to adhere to the idea that they are open-minded and able to change their mind in the light of evidence. Students can be given ideas or a opinion and asked to use the answer to the question to decide what the logical or rational choice is.',
    'Love of Learning': ' A student with the strength of love of learning could be asked if they feel like they learned something. They could also be given a fact or piece of information for them to add it to their fund of knowledge. This student could also be asked reflective questions to ensure that they understand the concept, and the relevance of said concept, all for the purpose of making sure that they feel like they learned something and expanded their knowledge.',
    'Perspective': ' A student with the strength of perspective could be tasked to think of a problem in a friend’s life that could be solved using the concept. A student with this strength could also be prompted to help a classmate who is struggling with this concept. A student could be given a problem with two or three different options/sides and have to decide which is the better option.',
    'Bravery': ' A student with the strength of bravery might be assigned the most challenging problem in the set to tackle first, pushing them to confront difficulties head-on. A student may be asked to set a goal for the question or the whole assignment. In a more dynamic question, the assignment might involve a story or context that has to do with confronting a fear.',
    'Perseverance': ' A student with the strength of perseverance could be given a goal or asked to set one for the assignment or for each question. This student could benefit from motivating comments throughout the assignment. This assignment could include questions that have to do with struggling to get through barriers or obstacles, but using the mathmateial concept, the student can get past it.',
    'Honesty': ' A student with the strength of honesty  may be asked how they feel about the problem, the concept. They could be asked if they are committed to learning this concept, why or why not? They may be given a problem that has a story or context having to do with goals aligning implicit interests, all while using the mathematical concept to progress through the story.',
    'Zest': ' A student with the strength of zest could be given an assignment that matches their energy level and is presented in a very enthusiastic and excited way. This student may be asked reflective questions asking about what made them excited about this assignment or concept, why it is exciting, and more.',
    'Love': ' A student with the strength of love could be tasked with assignments where the context of the questions are there close relationships. Maybe they are solving problems for their loved ones, empahsize teamwork, charitable acts, etc.',
    'Kindness': ' A student with the strength of kindness can be given a problem that has to do with helping others. This can mean they are explaining a math concept to a classmate, this can mean solving problems that are in a real-world context where they are helping a loved one, being asked a reflective question on how their ability to solve this problem can help them support and be generous to others, etc.',
    'Social Intelligence': ' A student with the strength of social intelligence may be tasked with organizing a group or event, considering scheduling, budgeting, or other things considering group dynamics. Another example would be to solve a problem requiring the student to use their understanding of human behavior and interactions, like splitting a bill among friends while considering each person’s financial situation.',
    'Teamwork': ' A student with the strength of teamwork could be given group problems where appropriate, but could also be given fictional situations within their assignments where they are on a team and need to solve problems for the good of the team. Reflective questions about why it is important to be dedicated and reliable could be helpful.',
    'Fairness': ' A student with the strength of fairness might be asked to solve a problem and use the answer to make a fair decision. It is important to emphasize that the feelings in the given situation are not as important as the facts that should justify the decision. A student may be asked reflective questions about whether the choice made gives equal opportunity to all, and if they feel as though personal feelings biased the decision.',
    'Leadership': ' A student with the strength of leadership could be tasked with setting a goal for each assignment, or in a problem with a given situation, and outlining what kind of help may be needed and what team would be a good fit for solving the problem. An emphasis should be placed on the student’s role as a leader and on the importance of putting together a good team.',
    'Forgiveness': ' A student with the strength of forgiveness might have students analyze math problems where mistakes were made and discuss how to correct them, emphasizing the importance of learning from errors and forgiving oneself for making them. Students may also be asked reflective questions relevant to their assignments about how forgiveness is important in the context of academics and real life.',
    'Humility': ' A student with the strength of humility might be asked questions that require the student to reflect on their learning process. For example, after solving a problem, you could ask, "What was the most challenging part of this problem for you, and how did you overcome it?" This encourages self-awareness and acknowledgment of difficulties. Present math problems that are grounded in real-world scenarios, emphasizing the practical application of math rather than abstract achievements. This approach highlights the usefulness of the skill rather than the students prowess. Students may also be asked to write down before a problem what they think they don’t know and afterwards, highlight their mistakes and learn from them.',
    'Prudence': ' A student with the strength of prudence could be asked to check each step before moving on to the next. Students with this strength should show all work, even moreso than other students. Also, students with this strength could be given a decision to make based on the answer to the question, and then asked to explain the long-term consequences of the decision and why that one was less risky.',
    'Self-Regulation': ' A student with the strength of self-regulation might give the student the opportunity to empower themselves and leverage their confidence as a means to solve problems. Give students reflective questions, allowing them to acknowledge the achievement of certain goals; these can be goals they set for the assignment, as a student, or life in general.',
    'Appreciation of Beauty and Excellence': ' A student with the strenght of appreciation of beauty and excellence may be given problems whose context is a beautiful scene or situation, enough to the point where the student can notice it and appreciate its beauty. A student may also be given reflective questions on the beauty and excellence of their ability to do math and why it is helpful.',
    'Gratitude': ' A student with the strength of gratitude could be tasked with problems that have real-world situations where gratitude is a prime emotion. Students can be prompted to be grateful for someone who may have helped them learn this math concept or solve the problem. The problem itself could be about finding the answer and explaining why gratitude may be important to solving future problems.',
    'Hope': ' A student with the strength of hope may be asked to set a high expectation for themselves in the context of completing this assignment and physically writing down that they believe in themselves. A student may also be asked after solving a problem if there was another pathway to doing so. ',
    'Humor': ' A student with the strength of humor may be given an assignment that is just generally a happy and cheerful vibe. This could also mean that students have problems where the answers make their peers happy. Lastly, student may be prompted to analyze their mistakes in an assignment and, in a cheerful manner, explain why it is helpful to make mistakes or something to that effect.',
    'Spirituality': 'A student with the strength of spirituality could be asked to reflect on how solving this assignment made them feel, understanding the emotions within and how they can effect future problem solving.'
    }

    cs_explanations = ""
    # Append explanations for the inputted strengths
    if isinstance(strengths, str):
        strengths = strengths.split(',')
    for strength in strengths:
        if strength in strength_explanations:
            cs_explanations += f"{strength}: {strength_explanations[strength]}\n"

    return cs_explanations
def life_skills_explanations(life_skills,standard):
    """
    Generates explanations for a list of life_skills provided in the input. Each character strength corresponds
    to a specific explanation that describes how the life_skills can be incorporated into educational activities or assignments.

    Args:
    life_skills (str): A comma-separated string of character strengths.

    Returns:
    str: A concatenated string of explanations for each strength. If the input strength is recognized, its corresponding
    explanation is included in the result.
    """


    #Dictionary with all possible strenghts and corresponding explanations
    life_skills_explanations = {
    'Critical Thinking': f'Create a set of engaging and thoughtful questions that prompt learners to practice critical thinking, aligned with the standard: {standard}. Each question should challenge learners to:\r\n\r\nObjectively analyze and evaluate information:\r\n\r\nAsk learners to objectively examine the details provided.\r\n\r\nPrompt evaluation of the validity, relevance, or reliability of the information or scenario presented.\r\n\r\nIdentify underlying assumptions, biases, and logical connections:\r\n\r\nEncourage identification of hidden assumptions or implicit biases in the given context.\r\n\r\nPrompt learners to clarify logical relationships, causes, and effects within the provided information.\r\n\r\nClearly reason and make evidence-based decisions:\r\n\r\nRequire learners to articulate their reasoning clearly and explicitly.\r\n\r\nAsk learners to support their responses by directly referencing factual evidence or data.\r\n\r\nIncorporate creative thinking aligned with the standard:\r\n\r\nPresent hypothetical scenarios, thought experiments, or slightly open-ended situations that inspire creative yet rigorous analysis.\r\n\r\nCreativity must always align with and enhance analysis according to the provided standard, never diminishing the requirement for evidence-based reasoning or objective evaluation.\r\n\r\nEnsure the generated questions strike a careful balance between creativity and rigorous alignment with the {standard}.',
    'Creativity': f'Create engaging and stimulating questions that encourage learners to practice creative thinking, aligned with the standard: {standard}. Each question should challenge learners to:\r\n\r\nGenerate Original Ideas and Think Outside the Box:\r\n\r\nPrompt learners to produce innovative, novel solutions or concepts.\r\n\r\nEncourage thinking beyond traditional or obvious answers, inspiring imaginative exploration.\r\n\r\nThink Beyond the Immediate Context:\r\n\r\nPose questions that compel learners to extend their thinking beyond what is explicitly stated or presented.\r\n\r\nInvite speculative thinking, hypothetical scenarios, or visionary projections.\r\n\r\nExplore Multiple Perspectives and Experiment:\r\n\r\nGuide learners to explore diverse viewpoints or angles when addressing problems.\r\n\r\nAsk learners to experiment conceptually, imagining new methods or approaches that diverge from conventional norms.\r\n\r\nExpress Ideas in Unique, Creative Ways:\r\n\r\nEncourage learners to articulate their responses through original metaphors, analogies, or imaginative narratives.\r\n\r\nInvite learners to use inventive forms of expression to communicate their thinking clearly yet uniquely.\r\n\r\nEnsure each question fosters an open-ended and inventive mindset, carefully aligning with and enhancing the creativity-focused aspects of the provided standard ({standard}).',
    'Problem Solving': f'Generate thoughtful and practical questions that encourage learners to practice problem-solving skills, aligned with the standard: {standard}. Each question should guide learners to:\r\n\r\nIdentify, Analyze, and Resolve Challenges:\r\n\r\nClearly define and identify the core issues or challenges presented.\r\n\r\nPrompt learners to deeply analyze these challenges to understand their root causes or contributing factors.\r\n\r\nEncourage practical resolutions based on thorough analysis.\r\n\r\nBreak Down Problems and Explore Potential Solutions:\r\n\r\nLead learners to decompose complex problems into smaller, manageable components.\r\n\r\nAsk learners to systematically explore multiple viable solutions or alternative pathways.\r\n\r\nApply Logical Strategies to Reach an Outcome:\r\n\r\nInvite learners to clearly articulate the logical processes or strategies employed to resolve the problem.\r\n\r\nEncourage step-by-step reasoning that leads methodically toward a clear outcome or solution.\r\n\r\nApproach Situations with a Clear Goal in Mind:\r\n\r\nEnsure each question explicitly or implicitly emphasizes a defined goal or objective.\r\n\r\nPrompt learners to keep the goal central throughout the process of reasoning, analysis, and solution formulation.\r\n\r\nEach question should foster structured, strategic problem-solving aligned closely with the provided standard ({standard}).',
    'Communication': f'Prompt Instructions for LLM:\r\nGenerate insightful and engaging questions that prompt learners to develop strong communication skills, aligned with the standard: {standard}. Each question should encourage learners to:\r\n\r\nEffectively Share and Exchange Information, Ideas, and Emotions:\r\n\r\nPrompt learners to clearly communicate their ideas, viewpoints, or emotions.\r\n\r\nEncourage thoughtful questions around expressing oneself effectively and actively listening in interactions.\r\n\r\nDemonstrate Clarity in Expressing Opinions, Arguments, and Emotions:\r\n\r\nAsk learners to articulate opinions or arguments clearly and persuasively.\r\n\r\nChallenge learners to consider how clarity impacts understanding and emotional connection in communication.\r\n\r\nUnderstand Contexts and Evaluate Communication Methods:\r\n\r\nPresent diverse scenarios or contexts and prompt learners to analyze how communication approaches should adapt accordingly.\r\n\r\nEncourage learners to evaluate the strengths and limitations (pros and cons) of different communication methods within given contexts.\r\n\r\nEach question should help learners develop adaptability, sensitivity, and effectiveness in communication, carefully aligned with the provided standard ({standard}).',
    'Emotional Skills': f'Create reflective and engaging questions that encourage learners to develop strong emotional skills, aligned with the standard: {standard}. Each question should prompt learners to:\r\n\r\nRecognize, Understand, and Manage Emotions:\r\n\r\nEncourage learners to identify and describe their own emotions, as well as the emotions of others.\r\n\r\nAsk learners to explore how understanding these emotions can lead to healthier management and decision-making.\r\n\r\nDemonstrate and Recognize Empathy:\r\n\r\nGuide learners to imagine and articulate the feelings or perspectives of others in different situations.\r\n\r\nPrompt thoughtful reflection on how empathy influences interactions and relationships.\r\n\r\nHandle Stress and Navigate Social Interactions and Relationships:\r\n\r\nPose scenarios where learners must consider effective ways to handle stress or emotionally challenging situations.\r\n\r\nEncourage strategic thinking about managing interpersonal interactions and maintaining healthy relationships.\r\n\r\nDevelop Self-awareness and Sensitivity:\r\n\r\nPrompt learners to reflect critically on their own emotional reactions and behaviors.\r\n\r\nFoster sensitivity by asking learners to consider how their words and actions impact the emotions of others.\r\n\r\nEach question should help learners enhance emotional intelligence, empathy, and interpersonal skills in alignment with the provided standard ({standard}).'
    }

    life_skills_selected = f"\r\n Please make sure try to asses the students skills in terms {life_skills}.\r\n This is the difinition of the skills:\r\n"
    # Append explanations for the inputted life_skill
    for life_skill in life_skills:
        life_skills_selected += f"{life_skill}: {life_skills_explanations[life_skill]}\n"

    return life_skills_selected

def convert_to_bullet_points(input_string):
    """
    Converts the input string into bullet points separated by newline characters.
    Splits primarily on '.', and further splits on '?' and '!'.
    Keeps the punctuation in the final bullet point.

    Parameters:
    input_string (str): The text to be converted into bullet points.

    Returns:
    str: A string with bullet points separated by newline characters.
    """
    # Check if the string already contains bullet points (simple check)
    if input_string.strip().startswith('-'):
        return input_string
    sentences = re.findall(r'[^.?!]+[.?!]?', input_string)

    # Create bullet points for each sentence (ignoring empty/whitespace-only items).
    bullet_points = []
    for sentence in sentences:
        cleaned = sentence.strip()
        if cleaned:
            bullet_points.append(f"- {cleaned}")

    # Join all bullet points with '\n'
    return "\n".join(bullet_points)

def inject_passage_field(tools):
    for tool in tools:
        if tool.get("type") == "function":
            parameters = tool.get("function", {}).get("parameters", {})
            homework = parameters.get("properties", {}).get("homework", {})

            if "properties" in homework and "required" in homework:
                # Add the 'passage' field
                homework["properties"]["passage"] = {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the generated reading passage"
                        }
                }

                # Ensure it's included in the required list
                if "passage" not in homework["required"]:
                    homework["required"].append("passage")
    return tools

def create_challenges_list(enriched_tasks):
    index=1
    for object in enriched_tasks:
        object['index']=index
        index += 1
    return enriched_tasks

texas_vocab_g3 = ['advance', 'adventures', 'Aladdin', 'arranged', 'astounding', 'atmosphere', 'backwater', 'blues', 'bolted', 'call on', 'capital', 'caterpillar', 'celebrate', 'celebration', 'chaos', 'civil', 'common', 'conceited', 'concluded', 'conducted', 'confusing', 'contemplated', 'courtier', 'croquet', 'cross', 'cupboard', 'curious', 'currant', 'custard', 'deafening', 'deed', 'dejected', 'deprive', 'disguise', 'dismally', 'distress', 'domestic', 'dormouse', 'duchess', 'dungeon', 'ecstasies', 'enthusiastically', 'escorted', 'evasively', 'expedition', 'fate', 'feeble', 'forage', 'fortune', 'furrow', 'gaining on', 'genie', 'glimpse', 'glittering', 'gross', 'heartily', 'hedgehog', 'hesitating', 'hour', 'immense', 'impertinence', 'imprisoned', 'improvised', 'indignant', 'indignantly', 'interferes', 'irritated', 'jury', 'justly', 'keenly', 'lurk', "ma'am", 'mad', 'magician', 'mallet', 'meandered', 'mended', 'merchant', 'modest', 'monstrous', 'muttering', 'nephew', 'obey', 'occupant', 'odd', 'palace', 'particularly', 'patter', 'peculiar', 'peddler', 'perceive', 'pleasure', 'pop', 'possessed', 'postpone', 'precious', 'proposal', 'pursued', 'radiant', 'recklessly', 'recollection', 'remained', 'remarkable', 'reproached', 'retired', 'revenge', 'scurry', 'seized', 'sensible', 'sentinels', 'sentries', 'sentence', 'servant', 'slumber', 'solitary', 'spirit', 'splendidly', 'spotted', 'stability', 'startled', 'state', 'sternly', 'storm', 'subtle', 'sultan', 'summoned', 'surveyed', 'tailor', 'temper', 'thoroughly', 'thunder', 'toffee', 'treasure', 'unerring', 'unrestrainedly', 'vanished', 'verdict', 'warily', 'whisk', 'witness', 'activist', 'adapt', 'amphibian', 'aquatic', 'attract', 'behavior', 'calcified', 'carnivore', 'cavity', 'characteristic', 'classify', 'climate', 'cold-blooded', 'column', 'communicate', 'constant', 'delta', 'diaphragm', 'exoskeleton', 'feather', 'fin', 'flock', 'gill', 'glide', 'habitat', 'herbivore', 'hibernate', 'huddle', 'inject', 'insulation', 'intelligent', 'internal', 'invertebrates', 'kingdom', 'language', 'life cycle', 'mammal', 'mammary glands', 'marine', 'migrate', 'molt', 'nectar', 'nerves', 'nest', 'nocturnal', 'observe', 'omnivore', 'orchestra', 'oxygen', 'plumage', 'poisonous', 'predator', 'primate', 'primatologist', 'reproduction', 'reptile', 'scale', 'school', 'secrete', 'sensitive', 'shed', 'sonar', 'spine', 'stately', 'suction cup', 'survive', 'tadpole', 'temperature', 'transformation', 'venom', 'venomous', 'vertebrates', 'warm-blooded', 'zoologist', 'Achilles', 'Achilles tendon', 'active', 'accurate', 'American Sign Language', 'anvil', 'appendages', 'appendicular bones', 'auditory nerve', 'automatically', 'axial bones', 'blind', 'braille', 'brain stem', 'breakthrough', 'calcium', 'cardiac muscle', 'cartilage', 'cast', 'cell', 'cell body', 'cerebellum', 'cerebral cortex', 'cerebrum', 'challenge', 'circulate', 'cochlea', 'coil', 'college degree', 'command', 'concussion', 'connective', 'consciously', 'contact lens', 'contract', 'coordinates', 'cope', 'cornea', 'courage', 'cranium', 'cushion', 'dairy', 'deaf', 'decade', 'delicate', 'dendrite', 'determined', 'digest', 'disability', 'ear canal', 'eardrum', 'eventually', 'exist', 'expand', 'farsighted', 'femur', 'fiber', 'fibula', 'flexible', 'flinch', 'fluid', 'frame', 'functions', 'gesture', 'gland', 'Grammy Awards', 'guide dog', 'hammer', 'hemisphere', 'hollow', 'imitate', 'inner ear', 'insert', 'involuntary', 'invulnerable', 'iris', 'joint', 'laser beam', 'LASIK surgery', 'lens', 'ligament', 'marrow', 'medulla', 'membrane', 'middle ear', 'model', 'muscle', 'muscular system', 'nearsighted', 'nervous system', 'optician', 'optic nerve', 'optometrist', 'organ', 'outer ear', 'overcoming', 'palm', 'paralyzed', 'pelvis', 'PET scan', 'politics', 'posture', 'prescription', 'pupil', 'realistic', 'receptors', 'reflex', 'retina', 'rods and cones', 'scapula', 'search', 'seemed to click', 'shoulder blade', 'skeletal system', 'skull', 'spinal column', 'spout', 'sternum', 'stirrup', 'stomach', 'structure', 'temper tantrum', 'tendon', 'tibia', 'tissue', 'tribute', 'Trojan', 'vertebra', 'vibrate', 'vision', 'voluntary', 'vulnerable', 'warrior', 'well', 'wiring', 'wondrous', 'x-ray']

texas_vocab_g4 = ['adept', 'antibody', 'anticipation', 'beneficiary', 'buckled', 'bulbar polio', 'castor oil', 'character trait', 'chronological', 'chuckled', 'coaxed', 'confiscated', 'contagious', 'contaminate', 'crevices', 'crippled', 'diagnosed', 'diagnosis', 'edible', 'embedded', 'enticing', 'epidemic', 'excruciating', 'feminist', 'fiction', 'first person', 'firsthand account', 'flawlessly', 'float', 'forget-me-nots', 'fragrant', 'gazed', 'glisten', 'grimace', 'guava', 'gunnysack', 'heralded', 'Homecoming', 'illegal', 'implications', 'infantile', 'iron lung', 'isolation ward', 'jubilantly', 'laden', 'limp', 'lugged', 'melody', 'metaphor', 'mucus', 'nonfiction', 'nourishment', 'O.T.', 'paralysis', 'paralyzed', 'personal narrative', 'phlegm', 'plow', 'plunge', 'pores', 'prickly', 'pulpit', 'respiratory', 'scored', 'secondhand account', 'sermon', 'simile', 'sin', 'spasm', 'spinal tap', 'striding', 'structure', 'tempting', 'tinge', 'unique', 'vaccine', 'virus', 'woozy', 'abscesses', 'agricultural', 'airborne', 'antibiotic', 'antiseptics', 'aquatic', 'arguably', 'avid', 'baboon', 'bacteriologist', 'bigwigs', 'boils', 'botanist', 'botany', 'charred', 'contaminated', 'crop rotation', 'cultures', 'cylinder', 'deliberately', 'deter', 'diaphragm', 'digress', 'dim', 'drawbacks', 'ecosystems', 'English Channel', 'evaporate', 'fertile', 'financiers', 'frequency', 'greasy spoons', 'heed', 'humble', 'illumination', 'indifference', 'inspire', 'intricate', 'inventory', 'irk', 'irritation', 'isolated', 'malfunctioning', 'manure', 'marketing', 'optimistic', 'organisms', 'parchment', 'patent', 'pendulum', 'petri dishes', 'phonograph', 'player piano', 'potential', 'practical', 'prestigious', 'primates', 'producers', 'profit', 'quelle guigne', 'quelle merveille', 'rehabilitation', 'revolutionary', 'self-sufficient', 'sharecroppers', 'simians', 'spearhead', 'Stone Age', 'stylus', 'sundial', 'synchronized', 'take root', 'telegraph', 'tinker', 'transmitter', 'très magnifique', 'upshot', 'vaccines', 'abound', 'abstain', 'accurate', 'ambassador', 'ammunition', 'assembly', 'bayonet', 'belfry', 'bewitching', 'bleak', 'boycott', 'breeches', 'burden', 'burly', 'casualty', 'charge', 'conflict', 'confront', 'console', 'convoy', 'decisive', 'declaration', 'defiant', 'dread', 'eliminate', 'engraving', 'enlist', 'export', 'fleet', 'foil', 'foraging', 'formidable', 'fortify', 'front', 'grievance', 'henpecked', 'hero', 'heroine', 'implication', 'import', 'impose', 'impress', 'indirectly', 'intolerable', 'jeer', 'levy', 'liberty', 'master', 'mastermind', 'melancholy', 'militia', 'misleading', 'model company', 'morale', 'musket', 'musket ball', 'muzzle', 'neutral', 'ninepins', 'opposition', 'oppressive', 'otherwise', 'patriot', 'peal', 'petition', 'proclamation', 'provoke', 'range', 'rebel', 'recruit', 'regiment', 'reinforce', 'repeal', 'retreat', 'revere', 'score', 'skeptical', 'skirmish', 'so-called', 'splendor', 'stockpile', 'strategic', 'suitor', 'surrender', 'switch', 'tactics', 'tax', 'traitor', 'turning point', 'tyrannical', 'villain', 'volley', 'woo']

lexile_vocab_g1 = [
    "lesson",
    "each",
    "solve",
    "shape",
    "sentence",
    "grow",
    "vocabulary",
    "compare",
    "chart",
    "science",
    "inquiry",
    "addition",
    "essential",
    "pattern",
    "explain",
    "below",
    "observe",
    "activity",
    "strategy",
    "alike",
    "resource",
    "describe",
    "detail",
    "envision",
    "large",
    "complete",
    "model",
    "topic",
    "natural",
    "independent",
    "predict",
    "symbol",
    "technology",
    "estimate"
]

lexile_vocab_g2 = [
    "form",
    "event",
    "clue",
    "effect",
    "amount",
    "connect",
    "example",
    "reason",
    "volume",
    "comprehension",
    "direction",
    "purpose",
    "illustrate",
    "expository",
    "opinion",
    "value",
    "contrast",
    "during",
    "retell",
    "solution",
    "contain",
    "context",
    "difference",
    "design",
    "summarize",
    "paragraph",
    "selection",
    "sequence",
    "fiction",
    "web",
    "mental",
    "method",
    "result",
    "analyze",
    "communicate",
    "location",
    "apply",
    "classify",
    "expand",
    "recycle",
    "carefully",
    "interest",
    "concept",
    "infer",
    "source",
    "informational",
    "process",
    "revise",
    "experience",
    "correctly",
    "preview",
    "relate",
    "system",
    "investigate",
    "toward",
    "theme"
]

lexile_vocab_g3 = [
    "affect",
    "organize",
    "represent",
    "critical",
    "factor",
    "separate",
    "locate",
    "prediction",
    "operation",
    "active",
    "depend",
    "section",
    "term",
    "hypothesis",
    "link",
    "focus",
    "similar",
    "standard",
    "express",
    "allow",
    "statement",
    "observation",
    "suppose",
    "improve",
    "adaptation",
    "invent",
    "balance",
    "develop",
    "illustration",
    "definition",
    "display",
    "opposite",
    "phrase",
    "visual",
    "passage",
    "description",
    "interpret",
    "nutrient",
    "characteristic",
    "challenge",
    "determine",
    "multiple",
    "online",
    "prefix",
    "professor",
    "revolve",
    "situation",
    "surround",
    "visualize",
    "combination",
    "conserve",
    "generalize",
    "interact",
    "knowledge",
    "potential",
    "relevant",
    "behavior",
    "evaluate",
    "excite",
    "explanation",
    "interactive",
    "declaration",
    "phase",
    "procedure",
    "response",
    "unique",
    "average",
    "occur",
    "caption",
    "representative",
    "suffix",
    "construct",
    "content",
    "farther",
    "fluency",
    "inherit",
    "relationship",
    "clearly",
    "pronunciation",
    "reasonableness"
]

lexile_vocab_g4 = [
    "range",
    "essay",
    "condition",
    "scientific",
    "reference",
    "increase",
    "growth",
    "convert",
    "customary",
    "generalization",
    "acute",
    "inference",
    "conclude",
    "role",
    "comparison",
    "partial",
    "select",
    "specific",
    "basic",
    "progress",
    "actual",
    "appropriate",
    "task",
    "ability",
    "conduct",
    "conflict",
    "influence",
    "persuasive",
    "primary",
    "audience",
    "historical",
    "organization",
    "positive",
    "precise",
    "transform",
    "unfamiliar",
    "variety",
    "vary",
    "boundary",
    "career",
    "exist",
    "narrow",
    "preserve",
    "publish",
    "reduce",
    "simplify",
    "suggest",
    "advertisement",
    "descriptive",
    "eventually",
    "involve",
    "prevent",
    "endanger",
    "error",
    "examine",
    "generate",
    "require",
    "decrease",
    "differ",
    "draft",
    "evenly",
    "inspire",
    "limit",
    "rate",
    "beyond",
    "discussion",
    "forecast",
    "gain",
    "individual",
    "original",
    "prove",
    "accurate",
    "composite",
    "convince",
    "establish",
    "inform",
    "instruction",
    "particular",
    "relative",
    "safely",
    "argument",
    "complex",
    "definite",
    "department",
    "outline",
    "prompt",
    "diverse",
    "extreme",
    "persuade",
    "quantity",
    "realistic",
    "benefit",
    "clarify",
    "comprehend",
    "historic",
    "production",
    "similarity"
]

lexile_vocab_g5 = [
    "compromise",
    "percent",
    "advantage",
    "coordinate",
    "approve",
    "additional",
    "assembly",
    "exploration",
    "oppose",
    "arctic",
    "indicate",
    "resistance",
    "approach",
    "origin",
    "reveal",
    "quiz",
    "quotation",
    "quality",
    "adjust",
    "classification",
    "introduction",
    "representation",
    "previous",
    "constant",
    "contribute",
    "development",
    "everyday",
    "feedback",
    "impact",
    "logical",
    "secondary",
    "succession",
    "achieve",
    "define",
    "maintain",
    "severe",
    "consist",
    "lack",
    "viewpoint",
    "category",
    "compact",
    "composition",
    "concern",
    "dependent",
    "depression",
    "despite",
    "detect",
    "efficient",
    "medium",
    "obtain",
    "relation",
    "slightly",
    "transition",
    "version",
    "attribute",
    "engage",
    "importance",
    "mainly",
    "preparation",
    "recall",
    "therefore",
    "various",
    "vast",
    "accuracy",
    "alternative",
    "circulation",
    "concentrate",
    "critique",
    "disadvantage",
    "further",
    "generally",
    "highlight",
    "navigation",
    "neutral",
    "presentation",
    "propose",
    "reliable",
    "spiral",
    "artificial",
    "assessment",
    "behave",
    "confuse",
    "constantly",
    "demonstrate",
    "desire",
    "dominant",
    "exert",
    "gradually",
    "informal",
    "irregular",
    "newly",
    "promote",
    "proportion",
    "resolve",
    "specialize",
    "typical",
    "conjunction",
    "contribution",
    "convey",
    "driven",
    "encounter",
    "expectation",
    "farthest",
    "homonym",
    "incorrect",
    "interaction",
    "perspective",
    "scarce",
    "separation",
    "suggestion",
    "technique"
]

lexile_vocab_ms = [
    "analysis",
    "absolute",
    "variation",
    "academic",
    "conjecture",
    "association",
    "interval",
    "standardize",
    "initial",
    "percentage",
    "approximate",
    "translate",
    "modify",
    "randomly",
    "conditional",
    "periodic",
    "approximately",
    "objective",
    "appeal",
    "cite",
    "principle",
    "statistics",
    "assess",
    "verify",
    "experimental",
    "universal",
    "alternate",
    "annual",
    "effective",
    "bias",
    "precision",
    "eliminate",
    "integration",
    "satisfy",
    "arrangement",
    "calculation",
    "extension",
    "trend",
    "assign",
    "noble",
    "thus",
    "indirect",
    "enable",
    "emphasize",
    "paraphrase",
    "participate",
    "aspect",
    "continuous",
    "compose",
    "insight",
    "enlarge",
    "consult",
    "interpretation",
    "complement",
    "distinct",
    "instance",
    "distinguish",
    "format",
    "distribute",
    "synthesize",
    "verbal",
    "downward",
    "margin",
    "slant",
    "dynamic",
    "significant",
    "confirm",
    "valid",
    "brainstorm",
    "external",
    "prior",
    "restate",
    "effectively",
    "operate",
    "fundamental",
    "peer",
    "isolate",
    "greatly",
    "accurately",
    "commonly",
    "dramatic",
    "frequently",
    "logically",
    "singular",
    "advertise",
    "apparent",
    "span",
    "similarly",
    "insert",
    "climax",
    "educate",
    "imply",
    "achievement",
    "creative",
    "boldface",
    "informative",
    "properly",
    "ensure",
    "fully",
    "analogy",
    "assumption",
    "restrict",
    "occupy",
    "regard",
    "complicate",
    "enclose",
    "intend",
    "optical",
    "intensity",
    "tendency",
    "virtual",
    "admire",
    "conversion",
    "inductively",
    "rapid",
    "respectively",
    "yield",
    "breed",
    "cumulative",
    "subordinate",
    "suspect",
    "chronological",
    "continuously",
    "efficiency",
    "undergo",
    "categorize",
    "rearrange",
    "typically",
    "update",
    "revision",
    "usage",
    "derive",
    "overlap",
    "consistent",
    "criteria",
    "integrate",
    "multimedia",
    "atmospheric",
    "explanatory",
    "literacy",
    "practical",
    "resemble",
    "widely",
    "descend",
    "improvement",
    "obstacle",
    "publication",
    "simulate",
    "briefly",
    "highly",
    "characterization",
    "fascinate",
    "hypothesize",
    "precede",
    "employ",
    "restriction",
    "anecdote",
    "rubric",
    "speculate",
    "currently",
    "limitation",
    "meaningful",
    "regularly",
    "relatively",
    "tightly",
    "dominate",
    "gender",
    "organizational",
    "participant",
    "requirement",
    "sufficient",
    "systematic",
    "circumstance",
    "fictional",
    "passive",
    "specify",
    "technical",
    "acquire",
    "logic",
    "mythology",
    "recreation",
    "unfold",
    "exposition",
    "inductive",
    "scheme",
    "timeline",
    "consequence",
    "factual",
    "astronomy",
    "characterize",
    "evolve",
    "focal",
    "quantitative",
    "alter",
    "denote",
    "functional",
    "observational",
    "collaboration",
    "comparative",
    "fame",
    "finite",
    "orient",
    "spun",
    "caution",
    "donate",
    "exhaust",
    "qualitative",
    "symbolic",
    "align",
    "convenient",
    "manipulate",
    "propel",
    "shield",
    "significance",
    "somewhat",
    "exceed",
    "exception",
    "incomplete",
    "keyword",
    "originate",
    "outward",
    "reduction",
    "unexpected",
    "capitalize",
    "destructive",
    "occurrence",
    "quest",
    "rarely",
    "steadily",
    "symbolize",
    "adequate",
    "afterward",
    "analytical",
    "anticipate",
    "classical",
    "complexity",
    "freely",
    "impress",
    "interfere",
    "memorable",
    "narration",
    "numerous",
    "partly",
    "slight",
    "suspend",
    "thrive",
    "deeply",
    "enhance",
    "favorable",
    "motivation",
    "regularity",
    "thoroughly",
    "uncover",
    "vague",
    "watt",
    "contradiction",
    "install",
    "regulation",
    "smoothly",
    "technological",
    "visually",
    "brightly",
    "annually",
    "displace",
    "educational",
    "excess",
    "introductory",
    "regardless",
    "reinforce",
    "repel",
    "shortage",
    "spite",
    "suitable",
    "textual",
    "worldwide",
    "fitness",
    "selective",
    "separately",
    "vital",
    "exposure",
    "likewise",
    "minimize",
    "partially",
    "perceive",
    "portable",
    "abbreviation",
    "converge",
    "determination",
    "flaw",
    "mislead",
    "persuasion",
    "roughly",
    "submersible",
    "unbiased",
    "unchanged",
    "coherent",
    "effectiveness",
    "efficiently",
    "incorporate",
    "yearly"
]

lexile_vocab_hs = [
    "criticism",
    "critically",
    "depict",
    "contemporary",
    "increasingly",
    "conference",
    "induce",
    "primarily",
    "existence",
    "devote",
    "twentieth",
    "retain",
    "innovation",
    "compel",
    "dramatically",
    "influential",
    "sustain",
    "elsewhere",
    "nevertheless",
    "capable",
    "absence",
    "moderate",
    "marginal",
    "perception",
    "isolation",
    "extensive",
    "lifestyle",
    "productive",
    "distinctive",
    "consumption",
    "frequent",
    "refine",
    "distinction",
    "merge",
    "uncertainty",
    "formulate",
    "discipline",
    "motivate",
    "philosophical",
    "consideration",
    "readily",
    "sharply",
    "initially",
    "descent",
    "disrupt",
    "disperse",
    "evident",
    "syntax",
    "abundance",
    "regain",
    "tremendous",
    "induction",
    "devise",
    "paradox",
    "phenomenon",
    "acknowledge",
    "discourage",
    "expel",
    "archetype",
    "implement",
    "administer",
    "confine",
    "necessity",
    "ongoing",
    "physician",
    "costly",
    "transitional",
    "supernatural",
    "underlie",
    "independently",
    "renew",
    "indirectly",
    "differentiate",
    "inferior",
    "significantly",
    "transcription",
    "beneficial",
    "intermediate",
    "notion",
    "tolerance",
    "defect",
    "incidence",
    "patent",
    "contradict",
    "eternal",
    "manual",
    "counterclaim",
    "cooperative",
    "psychological",
    "repeatedly",
    "unequal",
    "comprise",
    "enrich",
    "instruct",
    "attain",
    "diverge",
    "advancement",
    "designate",
    "compile",
    "cooperate",
    "distort",
    "diminish",
    "profession",
    "completion",
    "permanently",
    "plentiful",
    "consequently",
    "constitute",
    "counterargument",
    "dependence",
    "resistant",
    "omit",
    "dominance",
    "acceptance",
    "adjustment",
    "framework",
    "undermine",
    "validity",
    "voluntary",
    "deliberate",
    "successive",
    "merit",
    "dilemma",
    "precaution",
    "subsequent",
    "therapy",
    "latter",
    "nutrition",
    "embody",
    "abruptly",
    "collaborative",
    "consistently",
    "lowering",
    "overview",
    "argumentative",
    "uneven",
    "unlikely",
    "colleague",
    "credible",
    "notable",
    "outlook",
    "inward",
    "nominative",
    "plagiarism",
    "intrigue",
    "preservation",
    "explicitly",
    "fallacy",
    "recur",
    "deficiency",
    "drawback",
    "thorough",
    "contradictory",
    "duration",
    "refute",
    "copyright",
    "discard",
    "faulty",
    "hereditary",
    "inexpensive",
    "loosely",
    "respective",
    "wane",
    "concise",
    "predictable",
    "prominence",
    "simplicity",
    "sympathetic",
    "withstand",
    "appropriately",
    "exclusion",
    "remnant",
    "abbreviate",
    "aftermath",
    "enjoyment",
    "prescribe",
    "comprehensive",
    "deem",
    "diagnose",
    "periodically",
    "affix",
    "afflict",
    "ascend",
    "innovative",
    "minimal",
    "neatly",
    "accessible",
    "creativity",
    "experimentation",
    "intricate",
    "premise",
    "prevention",
    "readiness",
    "vigorously",
    "affordable",
    "authoritative",
    "discern",
    "dissimilar",
    "implication",
    "revolutionize",
    "unclear",
    "additionally",
    "innate",
    "likelihood",
    "peculiar",
    "speculation",
    "chiefly",
    "conform",
    "indefinitely",
    "orally",
    "reliability",
    "usable",
    "accommodate",
    "adequately",
    "elapse",
    "mainstream",
    "noticeable",
    "nuance",
    "outweigh",
    "progression",
    "conversely",
    "disprove",
    "recreational",
    "setback",
    "counterpart",
    "discourse",
    "inaccurate",
    "misuse",
    "nonverbal",
    "nurture",
    "usefulness",
    "archaeology",
    "commonplace",
    "dissatisfy",
    "dual",
    "excel",
    "heed",
    "implicit",
    "jargon",
    "milestone",
    "reliance",
    "sufficiently",
    "cautious",
    "certify",
    "connotative",
    "encouragement",
    "exploitation",
    "indication",
    "monotonous",
    "recognizable",
    "scholarly",
    "captivity",
    "comparable",
    "endurance",
    "foremost",
    "recede",
    "aesthetic",
    "annotation",
    "assertion",
    "assortment",
    "concisely",
    "durable",
    "dwindle",
    "esteem",
    "inefficient",
    "lifelong",
    "nutritional",
    "optimal",
    "assimilate",
    "continual",
    "decorative",
    "forceful",
    "inconsistent",
    "optimistic",
    "prevalent",
    "uncontrolled",
    "unfavorable",
    "unresolved",
    "acknowledgement",
    "discrepancy",
    "drastically",
    "fathom",
    "fluctuate",
    "fluctuation",
    "ghostly",
    "panelist",
    "reappear",
    "reconstruct",
    "straightforward",
    "vividly",
    "alternatively",
    "inclination",
    "locally",
    "majestic",
    "mythical",
    "mythological",
    "routinely",
    "unexpectedly",
    "deformation",
    "exemplify",
    "flexibility",
    "observable",
    "annotate",
    "collaborate",
    "counteract",
    "encompass",
    "extensively",
    "innermost",
    "measurable",
    "selectively",
    "tolerant",
    "wasteful",
    "adorn",
    "ensue",
    "incorrectly",
    "incur",
    "indent",
    "insightful",
    "namely",
    "nonessential",
    "outright",
    "advantageous",
    "deprivation",
    "equate",
    "persistence",
    "powerfully",
    "promptly",
    "thoughtfully",
    "unintended",
    "unreliable",
    "versatile",
    "alphabetical",
    "astound",
    "computerized",
    "continuity",
    "diagnosis",
    "impair",
    "initiation",
    "knowledgeable",
    "meager",
    "medicinal",
    "moderation",
    "restrictive",
    "unchanging",
    "closeness",
    "cohesive",
    "consistency",
    "cosmology",
    "doubtful",
    "favorably",
    "finely",
    "imagism",
    "ingenuity",
    "internationally",
    "overtake",
    "skeptical",
    "stately",
    "systematically",
    "unconvincing",
    "unravel",
    "unused",
    "allege",
    "beforehand",
    "elaboration",
    "freshness",
    "inclusion",
    "inherently",
    "jumble",
    "perseverance",
    "questionable",
    "seminal",
    "thinly",
    "urgency",
    "alternately",
    "bolster",
    "broadly",
    "coherence",
    "intentionally",
    "invariably",
    "jointly",
    "unbroken",
    "adjoin",
    "anew",
    "comfortably",
    "meteorology",
    "noteworthy",
    "propagation",
    "proportionally",
    "societal",
    "aeronautics",
    "discontinuity",
    "gritty",
    "seaward",
    "shortcoming",
    "acronym",
    "browse",
    "bulky",
    "clarification",
    "elicit",
    "facet",
    "familiarity",
    "furiously",
    "harmonious",
    "helplessly",
    "manageable",
    "quantify",
    "readable",
    "rediscover",
    "terminology",
    "unaffected",
    "unwise",
    "annoyance",
    "confidently",
    "foresee",
    "identifiable",
    "masterful",
    "misinterpret",
    "moderately",
    "negation",
    "nutritious",
    "outset",
    "successively",
    "succumb",
    "unrealistic",
    "unsupported",
    "validate",
    "artistry",
    "avid",
    "botany",
    "convincingly",
    "fearsome",
    "foundational",
    "hesitant",
    "miscellaneous",
    "persuasively",
    "reputable",
    "rigidly",
    "securely",
    "tragically",
    "expressively",
    "formalize",
    "leisurely",
    "omission",
    "originality",
    "specification",
    "ably",
    "causal",
    "delineate",
    "entice",
    "inaccessible",
    "induct",
    "noticeably",
    "rigidity",
    "unusable",
    "warmly",
    "allowable",
    "assort",
    "economical",
    "excitedly",
    "mesmerize",
    "intonation",
    "misconception",
    "appropriateness",
    "adage",
    "phonics",
    "unscramble"
]
ELA = {
    "reading_comp_gen": [
        "3.L.la Explain the function of nouns, pronouns, verbs, adjectives, and adverbs in general and their functions in particular sentences",
        "3.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "3.L.3b Recognize and observe differences between the conventions of spoken and written standard English.",
        "3.L.4a Use sentence-level context as a clue to the meaning of a word or phrase.",
        "3.L.5b Identify real-life connections between words and their use (e.g., describe people who are friendly or helpful).",
        "3.RF.3d Read grade-appropriate irregularly spelled words.",
        "3.RF.4 Read with sufficient accuracy and fluency to support comprehension.",
        "3.RF.4a Read grade-level text with purpose and understanding.",
        "3.RF.4b Read grade-level prose and poetry orally with accuracy, appropriate rate, and expression on successive readings.",
        "3.RF.4c Use context to confirm or self-correct word recognition and understanding, rereading as necessary.",
        "3.RL.1 Ask and answer questions to demonstrate understanding of a text, referring explicitly to the text as the basis for the answers.",
        "3.RL.2 Recount stories, including fables, folktales, and myths from diverse cultures; determine the central message, lesson, or moral and explain how it is conveyed through key details in the text.",
        "3.RL.3 Describe characters in a story (e.g., their traits, motivations, or feelings) and explain how their actions contribute to the sequence of events.",
        "3.RL.7 Explain how specific aspects of a text's illustrations contribute to what is conveyed by the words in a story (e.g., create mood, emphasize aspects of a character or setting).",
        "3.RL.9 Compare and contrast the themes, settings, and plots of stories written by the same author about the same or similar characters (e.g., in books from a series).",
        "3.RI.1 Ask and answer questions to demonstrate understanding of a text, referring explicitly to the text as the basis for the answers.",
        "3.RI.2 Determine the main idea of a text; recount the key details and explain how they support the main idea.",
        "3.RI.3 Describe the relationship between a series of historical events, scientific ideas or concepts, or steps in technical procedures in a text, using language that pertains to time, sequence, and cause/effect.",
        "3.RI.7 Use information gained from illustrations (e.g., maps, photographs) and the words in a text to demonstrate understanding of the text (e.g., where, when, why, and how key events occur).",
        "3.RI.8 Describe the logical connection between particular sentences and paragraphs in a text (e.g., comparison, cause/effect, first/second/third in a sequence).",
        "3.RI.9 Compare and contrast the most important points and key details presented in two texts on the same topic.",
        "3.RI.10 By the end of the year, read and comprehend informational texts, including history/social studies, science, and technical texts, at the high end of the grades 2-3 text complexity band independently and proficiently.",
        "3.SL.2 Determine the main ideas and supporting details of a text read aloud or information presented in diverse media and formats, including visually, quantitatively, and orally."
        "4.SL.2 Paraphrase portions of a text read aloud or information presented in diverse media and formats, including visually, quantitatively, and orally.",
        "4.SL.3 Identify the reasons and evidence a speaker provides to support particular points.",
        "4.L 4c Consult reference materials (e.g., dictionaries, glossaries, thesauruses). both print and digital, to find the pronunciation and determine or clarify the precise meaning of key words and phrases.",
        "4.L.5 Demonstrate understanding of figurative language, word relationships, and nuances in word meanings.",
        "4.L.5a Explain the meaning of simple similes and metaphors (e.g., as pretty as a picture) in context.",
        "4.RF.3 Know and apply grade-level phonics and word analysis skills in decoding words",
        "4.RF.4 Read with sufficient accuracy and fluency to support comprehension.",
        "4.RF.4a Read grade-level text with purpose and understanding.",
        "4.RF.4b Read grade-level prose and poetry orally with accuracy, appropriate rate, and expression on successive readings.",
        "4.RF.4c Use context to confirm or self-correct word recognition and understanding. rereading as necessary.",
        "4.RL.1 Refer to details and examples in a text when explaining what the text says explicitly and when drawing inferences from the text.",
        "4.RL.2 Determine a theme of a story. drama, or poem from details in the text: summarize the text.",
        "4.RL.3 Describe in depth a character. setting. or event in a story or drama. drawing on specific details in the text (e.a.. a character's thoughts. words. or actions).",
        "4.RL.5 Explain major differences between poems, drama, and prose, and refer to the structural elements of poems (e.g., verse, rhythm, meter) and drama (e.g., casts of characters, settings, descriptions, dialogue, stage directions) when writing or speaking about a text.",
        "4.RL.6 Compare and contrast the point of view from which different stories are narrated, including the difference between first- and third-person narrations.",
        "4.RL.7 Make connections between the text of a story or drama and a visual or oral presentation of the text, identifying where each version reflects specific descriptions and directions in the text.",
        "4.RL.9 Compare and contrast the treatment of similar themes and topics (e.g., opposition of good and evil) and patterns of events (e.g., the quest) in stories, myths, and traditional literature from different cultures.",
        "4.RL.1 By the end of the year, read and comprehend literature, including stories, dramas, and poetry, in the grades 4-5 text complexity band proficiently, with scaffolding as needed at the high end of the range.",
        "4.RI.1 Refer to details and examples in a text when explaining what the text says explicitly and when drawing inferences from the text.",
        "4.RI.2 Determine the main idea of a text and explain how it is supported by key details; summarize the text.",
        "4.RI.3 Explain events, procedures, ideas, or concepts in a historical, scientific, or technical text, including what happened and why, based on specific information in the text.",
        "4.RI.5 Describe the overall structure (e.g., chronology, comparison, cause/effect, problem/solution) of events, ideas, concepts, or information in a text or part of a text.",
        "4.RI.6 Compare and contrast a firsthand and secondhand account of the same event or topic; describe the differences in focus and the information provided.",
        "4.RI.7 Interpret information presented visually, orally, or quantitatively (e.g., in charts, graphs, diagrams, time lines, animations, or interactive elements on Web pages) and explain how the information contributes to an understanding of the text in which it appears.",
        "4.RI.8 Explain how an author uses reasons and evidence to support particular points in a text.",
        "4.RI.10 By the end of year, read and comprehend informational texts, including history/social studies, science, and technical texts, in the grades 4-5 text complexity band proficiently, with scaffolding as needed at the high end of the range."
    ],
    "reading_comp_prov": [
        "3.L.la Explain the function of nouns, pronouns, verbs, adjectives, and adverbs in general and their functions in particular sentences",
        "3.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "3.L.3b Recognize and observe differences between the conventions of spoken and written standard English.",
        "3.L.4a Use sentence-level context as a clue to the meaning of a word or phrase.",
        "3.L.5b Identify real-life connections between words and their use (e.g., describe people who are friendly or helpful).",
        "3.RF.3d Read grade-appropriate irregularly spelled words.",
        "3.RF.4 Read with sufficient accuracy and fluency to support comprehension.",
        "3.RF.4a Read grade-level text with purpose and understanding.",
        "3.RF.4b Read grade-level prose and poetry orally with accuracy, appropriate rate, and expression on successive readings.",
        "3.RF.4c Use context to confirm or self-correct word recognition and understanding, rereading as necessary.",
        "3.RL.1 Ask and answer questions to demonstrate understanding of a text, referring explicitly to the text as the basis for the answers.",
        "3.RL.2 Recount stories, including fables, folktales, and myths from diverse cultures; determine the central message, lesson, or moral and explain how it is conveyed through key details in the text.",
        "3.RL.3 Describe characters in a story (e.g., their traits, motivations, or feelings) and explain how their actions contribute to the sequence of events.",
        "3.RL.7 Explain how specific aspects of a text's illustrations contribute to what is conveyed by the words in a story (e.g., create mood, emphasize aspects of a character or setting).",
        "3.RL.9 Compare and contrast the themes, settings, and plots of stories written by the same author about the same or similar characters (e.g., in books from a series).",
        "3.RI.1 Ask and answer questions to demonstrate understanding of a text, referring explicitly to the text as the basis for the answers.",
        "3.RI.2 Determine the main idea of a text; recount the key details and explain how they support the main idea.",
        "3.RI.3 Describe the relationship between a series of historical events, scientific ideas or concepts, or steps in technical procedures in a text, using language that pertains to time, sequence, and cause/effect.",
        "3.RI.7 Use information gained from illustrations (e.g., maps, photographs) and the words in a text to demonstrate understanding of the text (e.g., where, when, why, and how key events occur).",
        "3.RI.8 Describe the logical connection between particular sentences and paragraphs in a text (e.g., comparison, cause/effect, first/second/third in a sequence).",
        "3.RI.9 Compare and contrast the most important points and key details presented in two texts on the same topic.",
        "3.RI.10 By the end of the year, read and comprehend informational texts, including history/social studies, science, and technical texts, at the high end of the grades 2-3 text complexity band independently and proficiently.",
        "3.SL.2 Determine the main ideas and supporting details of a text read aloud or information presented in diverse media and formats, including visually, quantitatively, and orally."
        "4.SL.2 Paraphrase portions of a text read aloud or information presented in diverse media and formats, including visually, quantitatively, and orally.",
        "4.SL.3 Identify the reasons and evidence a speaker provides to support particular points.",
        "4.L 4c Consult reference materials (e.g., dictionaries, glossaries, thesauruses). both print and digital, to find the pronunciation and determine or clarify the precise meaning of key words and phrases.",
        "4.L.5 Demonstrate understanding of figurative language, word relationships, and nuances in word meanings.",
        "4.L.5a Explain the meaning of simple similes and metaphors (e.g., as pretty as a picture) in context.",
        "4.RF.3 Know and apply grade-level phonics and word analysis skills in decoding words",
        "4.RF.4 Read with sufficient accuracy and fluency to support comprehension.",
        "4.RF.4a Read grade-level text with purpose and understanding.",
        "4.RF.4b Read grade-level prose and poetry orally with accuracy, appropriate rate, and expression on successive readings.",
        "4.RF.4c Use context to confirm or self-correct word recognition and understanding. rereading as necessary.",
        "4.RL.1 Refer to details and examples in a text when explaining what the text says explicitly and when drawing inferences from the text.",
        "4.RL.2 Determine a theme of a story. drama, or poem from details in the text: summarize the text.",
        "4.RL.3 Describe in depth a character. setting. or event in a story or drama. drawing on specific details in the text (e.a.. a character's thoughts. words. or actions).",
        "4.RL.5 Explain major differences between poems, drama, and prose, and refer to the structural elements of poems (e.g., verse, rhythm, meter) and drama (e.g., casts of characters, settings, descriptions, dialogue, stage directions) when writing or speaking about a text.",
        "4.RL.6 Compare and contrast the point of view from which different stories are narrated, including the difference between first- and third-person narrations.",
        "4.RL.7 Make connections between the text of a story or drama and a visual or oral presentation of the text, identifying where each version reflects specific descriptions and directions in the text.",
        "4.RL.9 Compare and contrast the treatment of similar themes and topics (e.g., opposition of good and evil) and patterns of events (e.g., the quest) in stories, myths, and traditional literature from different cultures.",
        "4.RL.1 By the end of the year, read and comprehend literature, including stories, dramas, and poetry, in the grades 4-5 text complexity band proficiently, with scaffolding as needed at the high end of the range.",
        "4.RI.1 Refer to details and examples in a text when explaining what the text says explicitly and when drawing inferences from the text.",
        "4.RI.2 Determine the main idea of a text and explain how it is supported by key details; summarize the text.",
        "4.RI.3 Explain events, procedures, ideas, or concepts in a historical, scientific, or technical text, including what happened and why, based on specific information in the text.",
        "4.RI.5 Describe the overall structure (e.g., chronology, comparison, cause/effect, problem/solution) of events, ideas, concepts, or information in a text or part of a text.",
        "4.RI.6 Compare and contrast a firsthand and secondhand account of the same event or topic; describe the differences in focus and the information provided.",
        "4.RI.7 Interpret information presented visually, orally, or quantitatively (e.g., in charts, graphs, diagrams, time lines, animations, or interactive elements on Web pages) and explain how the information contributes to an understanding of the text in which it appears.",
        "4.RI.8 Explain how an author uses reasons and evidence to support particular points in a text.",
        "4.RI.10 By the end of year, read and comprehend informational texts, including history/social studies, science, and technical texts, in the grades 4-5 text complexity band proficiently, with scaffolding as needed at the high end of the range.",

    ],
    "vocab_fill": [
        "3.L.1b Form and use regular and irregular plural nouns.",
        "3.L.1d Form and use regular and irregular verbs.",
        "3.L.1e Form and use the simple (e.g., I walked; I walk; I will walk) verb tenses.",
        "3.L.1f Ensure subject-verb and pronoun-antecedent agreement.",
        "3.L.1g Form and use comparative and superlative adjectives and adverbs, and choose between them depending on what is to be modified.",
        "3.L.1h Use coordinating and subordinating conjunctions.",
        "3.L.2d Form and use possessives.",
        "3.L.2e Use conventional spelling for high-frequency and other studied words and for adding suffixes to base words (e.g., sitting, smiled, cries, happiness).",
        "3.L.2f Use spelling patterns and generalizations (e.g., word families, position-based spellings, syllable patterns, ending rules, meaningful word parts) in writing words.",
        "3.L.2g Consult reference materials, including beginning dictionaries, as needed to check and correct spellings.",
        "3.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "3.L.4 Determine or clarify the meaning of unknown and multiple-meaning words and phrases based on grade 3 reading and content, choosing flexibly from a range of strategies.",
        "3.L.4a Use sentence-level context as a clue to the meaning of a word or phrase.",
        "3.L.4b Determine the meaning of the new word formed when a known affix is added to a known word (e.g., agreeable/disagreeable, comfortable/uncomfortable, care/careless, heat/preheat).",
        "3.L.4c Use a known root word as a clue to the meaning of an unknown word with the same root (e.g., company, companion).",
        "3.L.4d Use glossaries or beginning dictionaries, both print and digital, to determine or clarify the precise meaning of key words and phrases.",
        "3.L.5 Demonstrate understanding of word relationships and nuances in word meanings.",
        "3.L.5a Distinguish the literal and nonliteral meanings of words and phrases in context (e.g., take steps).",
        "3.L.5b Identify real-life connections between words and their use (e.g., describe people who are friendly or helpful).",
        "3.L.5c Distinguish shades of meaning among related words that describe states of mind or degrees of certainty (e.g., knew, believed, suspected, heard, wondered).",
        "3.L.6 Acquire and use accurately grade-appropriate conversational, general academic, and domain-specific words and phrases, including those that signal spatial and temporal relationships (e.g., After dinner that night we went looking for them).",
        "3.RE.3 Know and apply grade-level phonics and word analysis skills in decoding words.",
        "3.RF.3a Identify and know the meaning of the most common prefixes and derivational suffixes.",
        "3.RF.3b Decode words with common Latin suffixes.",
        "3.RF.3c Decode multisyllable words.",
        "3.RF.3d Read grade-appropriate irregularly spelled words.",
        "3.RF.4c Use context to confirm or self-correct word recognition and understanding, rereading as necessary.",
        "3.RL.4 Determine the meaning of words and phrases as they are used in a text, distinguishing literal from nonliteral language.",
        "3.RI.4 Determine the meaning of general academic and domain-specific words and phrases in a text relevant to a grade 3 topic or subject area.",
        "4.L.1b Form and use the progressive (e.g., I was walking; I am walking; I will be walking) verb tenses.",
        "4.L.1c Use modal auxiliaries (e.g., can, may, must) to convey various conditions.",
        "4.L.1d Order adjectives within sentences according to conventional patterns (e.g., a small red bag rather than a red small bag).",
        "4.L.1g Correctly use frequently confused words (e.g., to, too, two; there, their).",
        "4.L.2d Spell grade-appropriate words correctly, consulting references as needed.",
        "4.L.3а Choose words and phrases to convey ideas precisely.",
        "4.L.4 Determine or clarify the meaning of unknown and multiple-meaning words and phrases based on grade 4 reading and content, choosing flexibly from a range of strategies.",
        "4.L.4a Use context (e.g., definitions, examples, or restatements in text) as a clue to the meaning of a word or phrase.",
        "4.L.4b Use common, grade-appropriate Greek and Latin affixes and roots as clues to the meaning of a word (e.g., telegraph, photograph, autograph).",
        "4.L.4c Consult reference materials (e.g., dictionaries, glossaries, thesauruses), both print and digital, to find the pronunciation and determine or clarify the precise meaning of key words and phrases.",
        "4.L.5b Recognize and explain the meaning of common idioms, adages, and proverbs.",
        "4.L.5c Demonstrate understanding of words by relating them to their opposites (antonyms) and to words with similar but not identical meanings (synonyms)."
        "4.RF.3 Know and apply grade-level phonics and word analysis skills in decoding words.",
        "4.RF.3a Use combined knowledge of all letter-sound correspondences, syllabication patterns, and morphology (e.g., roots and affixes) to read accurately unfamiliar multisyllabic words in context and out of context.",
        "4.RF.4c Use context to confirm or self-correct word recognition and understanding, rereading as necessary.",
        "4.RL.4 Determine the meaning of words and phrases as they are used in a text, including those that allude to significant characters found in mythology (e.g., Herculean).",
        "4.RI.4 Determine the meaning of general academic and domain-specific words or phrases in a text relevant to a grade 4 topic or subject area.",
    ],
    "arg_essay": [
        "3.W.10 Write routinely over extended time frames (time for research, reflection, and revision) and shorter time frames (a single sitting or a day or two) for a range of discipline-specific tasks, purposes, and audiences.",
        "3.L.1 Demonstrate command of the conventions of standard English grammar and usage when writing or speaking.",
        "3.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "3.L.3a Choose words and phrases for effect.",
        "3.SL.3 Ask and answer questions about information from a speaker, offering appropriate elaboration and detail.",
        "3.SL.1 Engage effectively in a range of collaborative discussions (one-on-one, in groups, and teacher-led) with diverse partners on grade 3 topics and texts, building on others' ideas and expressing their own clearly.",
        "3.SL.1a Come to discussions prepared, having read or studied required material; explicitly draw on that preparation and other information known about the topic to explore ideas under discussion.",
        "3.SL.1b Follow agreed-upon rules for discussions (e.g., gaining the floor in respectful ways, listening to others with care, speaking one at a time about the topics and texts under discussion).",
        "3.SL.1c Ask questions to check understanding of information presented, stay on topic, and link their comments to the remarks of others.",
        "3.SL.1d Explain their own ideas and understanding in light of the discussion.",
        "3.RL.6 Distinguish their own point of view from that of the narrator or those of the characters.",
        "3.RI.6 Distinguish their own point of view from that of the author of a text.",
        "3.W.1 Write opinion pieces on topics or texts, supporting a point of view with reasons.",
        "3.W.1a Introduce the topic or text they are writing about, state an opinion, and create an organizational structure that lists reasons.",
        "3.W.1b Provide reasons that support the opinion.",
        "3.W.1c Use linking words and phrases (e.g., because, therefore, since, for example) to connect opinion and reasons.",
        "3.W.1d Provide a concluding statement or section.",
        "3.W.2b Develop the topic with facts, definitions, and details.",
        "3.W.2d Provide a concluding statement or section.",
        "4.L.6 Acquire and use accurately grade-appropriate general academic and domain-specific words and phrases, including those that signal precise actions, emotions, or states of being (e.g., quizzed, whined, stammered) and that are basic to a particular topic (e.g., wildlife, conservation, and endangered when discussing animal preservation).",
        "4.SL.1 Engage effectively in a range of collaborative discussions (one-on-one, in groups, and teacher-led) with diverse partners on grade 4 topics and texts, building on others' ideas and expressing their own clearly.",
        "4.SL.1a Come to discussions prepared, having read or studied required material; explicitly draw on that preparation and other information known about the topic to explore ideas under discussion.",
        "4.SL.1b Follow agreed-upon rules for discussions and carry out assigned roles.",
        "4.SL.1c Pose and respond to specific questions to clarify or follow up on information, and make comments that contribute to the discussion and link to the remarks of others.",
        "4.SL.1d Review the key ideas expressed and explain their own ideas and understanding in light of the discussion.",
        "4.SL.6 Differentiate between contexts that call for formal English (e.g., presenting ideas) and situations where informal discourse is appropriate (e.g., small-group discussion); use formal English when appropriate to task and situation. (See grade 4 Language standards 1 and 3 for specific expectations.)",
        "4.W.5 With guidance and support from peers and adults, develop and strengthen writing as needed by planning, revising, and editing. (Editing for conventions should demonstrate command of Language standards 1-3 up to and including grade 4.)",
        "4.W.6 With some guidance and support from adults, use technology, including the Internet, to produce and publish writing as well as to interact and collaborate with others; demonstrate sufficient command of keyboarding skills to type a minimum of one page in a single sitting.",
        "4.W.10 Write routinely over extended time frames (time for research, reflection, and revision) and shorter time frames (a single sitting or a day or two) for a range of discipline-specific tasks, purposes, and audiences.",
        "4.L.1 Demonstrate command of the conventions of standard English grammar and usage when writing or speaking.",
        "4.L.1a Use relative pronouns (who, whose, whom, which, that) and relative adverbs (where, when, why).",
        "4.L.1e Form and use prepositional phrases.",
        "4.L.1f Produce complete sentences, recognizing and correcting inappropriate fragments and run-ons.",
        "4.L.1g Correctly use frequently confused words (e.g., to, too, two; there, their).",
        "4.L.2 Demonstrate command of the conventions of standard English capitalization, punctuation, and spelling when writing.",
        "4.L.2a Use correct capitalization.",
        "4.L.2b Use commas and quotation marks to mark direct speech and quotations from a text.",
        "4.L.2c Use a comma before a coordinating conjunction in a compound sentence.",
        "4.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "4.L.3a Choose words and phrases to convey ideas precisely.",
        "4.L.3b Choose punctuation for effect.",
        "4.L.3c Differentiate between contexts that call for formal English (e.g., presenting ideas) and situations where informal discourse is appropriate (e.g., small-group discussion).",
        "4.W.1 Write opinion pieces on topics or texts, supporting a point of view with reasons and information.",
        "4.W.1a Introduce a topic or text clearly, state an opinion, and create an organizational structure in which related ideas are grouped to support the writer's purpose.",
        "4.W.1b Provide reasons that are supported by facts and details.",
        "4.W.1c Link opinion and reasons using words and phrases (e.g., for instance, in order to, in addition).",
        "4.W.1d Provide a concluding statement or section related to the opinion presented."
    ],
    "inf_essay": [
        "3.L.1c Use abstract nouns (e.g., childhood).",
        "3.L.1i Produce simple, compound, and complex sentences.",
        "3.L.2 Demonstrate command of the conventions of standard English capitalization, punctuation, and spelling when writing.",
        "3.L.2a Capitalize appropriate words in titles.",
        "3.L.2b Use commas in addresses.",
        "3.L.2c Use commas and quotation marks in dialogue.",
        "3.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "3.W.4 With guidance and support from adults, produce writing in which the development and organization are appropriate to task and purpose. (Grade-specific expectations for writing types are defined in standards 1-3 above.)",
        "3.W.5 With guidance and support from peers and adults, develop and strengthen writing as needed by planning, revising, and editing. (Editing for conventions should demonstrate command of Language standards 1-3 up to and including grade 3.)",
        "3.W.6 With guidance and support from adults, use technology to produce and publish writing (using keyboarding skills) as well as to interact and collaborate with others.",
        "3.W.7 Conduct short research projects that build knowledge about a topic.",
        "3.W.8 Recall information from experiences or gather information from print and digital sources; take brief notes on sources and sort evidence into provided categories.",
        "3.W.10 Write routinely over extended time frames (time for research, reflection, and revision) and shorter time frames (a single sitting or a day or two) for a range of discipline-specific tasks, purposes, and audiences.",
        "3.L.1 Demonstrate command of the conventions of standard English grammar and usage when writing or speaking.",
        "3.SL.4 Report on a topic or text, tell a story, or recount an experience with appropriate facts and relevant, descriptive details, speaking clearly at an understandable pace.",
        "3.SL.5 Create engaging audio recordings of stories or poems that demonstrate fluid reading at an understandable pace; add visual displays when appropriate to emphasize or enhance certain facts or details.",
        "3.SL.6 Speak in complete sentences when appropriate to task and situation in order to provide requested detail or clarification. (See grade 3 Language standards 1 and 3 for specific expectations.)",
        "3.RL.1 Ask and answer questions to demonstrate understanding of a text, referring explicitly to the text as the basis for the answers.",
        "3.RL.3 Describe characters in a story (e.g., their traits, motivations, or feelings) and explain how their actions contribute to the sequence of events.",
        "3.RL.5 Refer to parts of stories, dramas, and poems when writing or speaking about a text, using terms such as chapter, scene, and stanza; describe how each successive part builds on earlier sections.",
        "3.RL.9 Compare and contrast the themes, settings, and plots of stories written by the same author about the same or similar characters (e.g., in books from a series).",
        "3.RI.3 Describe the relationship between a series of historical events, scientific ideas or concepts, or steps in technical procedures in a text, using language that pertains to time, sequence, and cause/effect.",
        "3.RI.5 Use text features and search tools (e.g., key words, sidebars, hyperlinks) to locate information relevant to a given topic efficiently.",
        "3.RI.9 Compare and contrast the most important points and key details presented in two texts on the same topic.",
        "3.W.1d Provide a concluding statement or section.",
        "3.W.2 Write informative/explanatory texts to examine a topic and convey ideas and information clearly.",
        "3.W.2a Introduce a topic and group related information together; include illustrations when useful to aiding comprehension.",
        "3.W.2b Develop the topic with facts, definitions, and details.",
        "3.W.2c Use linking words and phrases (e.g., also, another, and, more, but) to connect ideas within categories of information.",
        "3.W.2d Provide a concluding statement or section.",
        "3.W.3 Write narratives to develop real or imagined experiences or events using effective technique, descriptive details, and clear event sequences.",
        "3.W.3a Establish a situation and introduce a narrator and/or characters; organize an event sequence that unfolds naturally.",
        "3.W.3b Use dialogue and descriptions of actions, thoughts, and feelings to develop experiences and events or show the response of characters to situations.",
        "3.W.3c Use temporal words and phrases to signal event order.",
        "3.W.3d Provide a sense of closure.",
        "4.L.6 Acquire and use accurately grade-appropriate general academic and domain-specific words and phrases, including those that signal precise actions, emotions, or states of being (e.g., quizzed, whined, stammered) and that are basic to a particular topic (e.g., wildlife, conservation, and endangered when discussing animal preservation).",
        "4.SL.4 Report on a topic or text, tell a story, or recount an experience in an organized manner, using appropriate facts and relevant, descriptive details to support main ideas or themes; speak clearly at an understandable pace.",
        "4.W.2e Provide a concluding statement or section related to the information or explanation presented.",
        "4.W.3 Write narratives to develop real or imagined experiences or events using effective technique, descriptive details, and clear event sequences.",
        "4.W.3a Orient the reader by establishing a situation and introducing a narrator and/or characters; organize an event sequence that unfolds naturally.",
        "4.W.3b Use dialogue and description to develop experiences and events or show the responses of characters to situations.",
        "4.W.3c Use a variety of transitional words and phrases to manage the sequence of events.",
        "4.W.3d Use concrete words and phrases and sensory details to convey experiences and events precisely.",
        "4.W.3e Provide a conclusion that follows from the narrated experiences or events.",
        "4.W.4 Produce clear and coherent writing in which the development and organization are appropriate to task, purpose, and audience. (Grade-specific expectations for writing types are defined in standards 1-3 above.)",
        "4.W.5 With guidance and support from peers and adults, develop and strengthen writing as needed by planning, revising, and editing. (Editing for conventions should demonstrate command of Language standards 1-3 up to and including grade 4.)",
        "4.W.6 With some guidance and support from adults, use technology, including the Internet, to produce and publish writing as well as to interact and collaborate with others; demonstrate sufficient command of keyboarding skills to type a minimum of one page in a single sitting.",
        "4.W.7 Conduct short research projects that build knowledge through investigation of different aspects of a topic.",
        "4.W.8 Recall relevant information from experiences or gather relevant information from print and digital sources; take notes and categorize information, and provide a list of sources.",
        "4.W.9 Draw evidence from literary or informational texts to support analysis, reflection, and research.",
        "4.W.9a Apply grade 4 Reading standards to literature (e.g., 'Describe in depth a character, setting, or event in a story or drama, drawing on specific details in the text [e.g., a character's thoughts, words, or actions].').",
        "4.W.9b Apply grade 4 Reading standards to informational texts (e.g., 'Explain how an author uses reasons and evidence to support particular points in a text').",
        "4.W.10 Write routinely over extended time frames (time for research, reflection, and revision) and shorter time frames (a single sitting or a day or two) for a range of discipline-specific tasks, purposes, and audiences.",
        "4.L.1 Demonstrate command of the conventions of standard English grammar and usage when writing or speaking.",
        "4.L.1a Use relative pronouns (who, whose, whom, which, that) and relative adverbs (where, when, why).",
        "4.L.1e Form and use prepositional phrases.",
        "4.L.1f Produce complete sentences, recognizing and correcting inappropriate fragments and run-ons.",
        "4.L.1g Correctly use frequently confused words (e.g., to, too, two; there, their).",
        "4.L.2 Demonstrate command of the conventions of standard English capitalization, punctuation, and spelling when writing.",
        "4.L.2a Use correct capitalization.",
        "4.L.2b Use commas and quotation marks to mark direct speech and quotations from a text.",
        "4.L.2c Use a comma before a coordinating conjunction in a compound sentence.",
        "4.L.3 Use knowledge of language and its conventions when writing, speaking, reading, or listening.",
        "4.L.3a Choose words and phrases to convey ideas precisely.",
        "4.L.3c Differentiate between contexts that call for formal English (e.g., presenting ideas) and situations where informal discourse is appropriate (e.g., small-group discussion).",
        "4.RI.6 Compare and contrast a firsthand and secondhand account of the same event or topic; describe the differences in focus and the information provided.",
        "4.RI.7 Interpret information presented visually, orally, or quantitatively (e.g., in charts, graphs, diagrams, time lines, animations, or interactive elements on Web pages) and explain how the information contributes to an understanding of the text in which it appears.",
        "4.RI.9 Integrate information from two texts on the same topic in order to write or speak about the subject knowledgeably.",
        "4.W.2 Write informative/explanatory texts to examine a topic and convey ideas and information clearly.",
        "4.W.2a Introduce a topic clearly and group related information in paragraphs and sections; include formatting (e.g., headings), illustrations, and multimedia when useful to aiding comprehension.",
        "4.W.2b Develop the topic with facts, definitions, concrete details, quotations, or other information and examples related to the topic.",
        "4.W.2c Link ideas within categories of information using words and phrases (e.g., another, for example, also, because).",
        "4.W.2d Use precise language and domain-specific vocabulary to inform about or explain the topic."
    ]
}
