import pytest
from unittest.mock import patch, MagicMock

# ✅ SUCCESS TEST
@patch("service.common_agents.translate_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
@patch("service.common_agents.translate_agent.make_openai_langfuse")
@patch("service.common_agents.translate_agent.langfuse.get_prompt")
def test_translate_assignment_agent_success(mock_get_prompt, mock_openai_call):
    from service.common_agents.translate_agent import translate_assignment_agent

    test_json = {
        "assignment": {
            "title": "Gravity",
            "questions": ["What is gravity?"]
        }
    }

    # Mock Langfuse prompt and config
    mock_prompt = MagicMock()
    mock_prompt.compile.return_value = "compiled_prompt"
    mock_prompt.config = {
        "openai": {
            "temperature": 0.5,
            "model": "gpt-4.1",
            "max_tokens": 4096
        }
    }
    mock_get_prompt.return_value = mock_prompt

    # Mock OpenAI call
    mock_openai_call.return_value = {
        "translated": True,
        "content": "Contenido traducido"
    }

    result = translate_assignment_agent(
        json_response=test_json,
        nb=1,
        assignment_type="essay",
        tools={},
        language="spanish"
    )

    assert result["translated"] is True
    assert result["content"] == "Contenido traducido"


# ❌ FAILURE TEST - simulate Langfuse prompt failure
@patch("service.common_agents.translate_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
@patch("service.common_agents.translate_agent.make_openai_langfuse")
@patch("service.common_agents.translate_agent.langfuse.get_prompt")
def test_translate_assignment_agent_failure(mock_get_prompt, mock_openai_call):
    from service.common_agents.translate_agent import translate_assignment_agent

    test_json = {
        "assignment": {
            "title": "Photosynthesis",
            "questions": ["How does photosynthesis work?"]
        }
    }

    # Simulate Langfuse compile failure
    mock_prompt = MagicMock()
    mock_prompt.compile.side_effect = Exception("Prompt error")
    mock_prompt.config = {
        "openai": {
            "temperature": 0.5,
            "model": "gpt-4.1",
            "max_tokens": 4096
        }
    }
    mock_get_prompt.return_value = mock_prompt

    result = translate_assignment_agent(
        json_response=test_json,
        nb=1,
        assignment_type="science",
        tools={},
        language="french"
    )

    assert result == test_json  # Should fallback to original input
