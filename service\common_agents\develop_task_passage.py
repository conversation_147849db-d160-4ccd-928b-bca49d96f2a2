from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse, logger
import json
from typing import List, Dict
from service.common_functions import vocab_examples

def develop_tasks_passage_agent(refined_passage: str, sub_standard: list, grade: str, nb_of_questions: int, assignment_type: str) -> List[Dict]:
    """
    Generates `nb_of_questions` passage-based tasks with per-task solvability checks and targeted retries.
    Only retries the task that fails solvability; previously accepted tasks are preserved.
    """
    # Config
    MAX_RETRIES_PER_TASK = 2  # how many times to retry an unsolvable task before skipping to the next sub-standard
    MAX_TOTAL_ATTEMPTS_MULTIPLIER = 6  # safety cap: nb_of_questions * multiplier

    # Additional instruction by assignment type
    if assignment_type == 'history_fact':
        additional_instruction = (
            "Generate only fact-based questions. "
            "Each question must have a definite, unambiguous answer found explicitly in the provided passage. "
            "Do not generate questions that require opinion, inference, interpretation, speculation, or external knowledge. "
            "Avoid open-ended, evaluative, or hypothetical questions. "
            "Use only the facts presented in the passage, and format the questions so that each answer can be found as a clear statement within the text."
        )
    elif assignment_type == 'vocab_fill':
        vocab_list = vocab_examples(grade)
        additional_instruction = f"""
Create vocabulary-based questions that rely only on words that actually appear in the passage.
Use vocab_list solely to gauge grade-appropriate difficulty and style; do not pull words from it unless they also occur in the passage.
All target words, question stems, answer choices, definitions, and distractors must come strictly from the passage text.
If a word is not present in the passage, do not use it.
vocab_list (reference only): {vocab_list}
"""
    else:
        additional_instruction = ""

    # Handle tuple case for nb_of_questions
    if isinstance(nb_of_questions, tuple):
        nb_of_questions = nb_of_questions[0]

    # Ensure sub_standard is a list
    if isinstance(sub_standard, str):
        try:
            sub_standard = json.loads(sub_standard)
        except Exception as e:
            logger.error(f"Failed to convert sub_standard JSON: {e}")
            sub_standard = []

    total_substandards = len(sub_standard)
    if total_substandards == 0:
        logger.warning("No sub-standards provided; returning empty list.")
        return []

    tasks_generated: List[Dict] = []

    # Prompts and tool schemas
    develop_prompt = langfuse.get_prompt("develop_tasks_passage", type="chat", label="latest")
    confirmation_prompt = langfuse.get_prompt("question_eligibility_confirmation", type="chat", label="latest")

    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_tasks",
                "description": (
                    "Produces an ordered list of skill objects, each containing "
                    "a skill_number and a task"
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "skills": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill_number' field and a 'task' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {
                                        "type": "string",
                                        "description": "The number of the skill"
                                    },
                                    "task": {
                                        "type": "string",
                                        "description": "A task that measures the skill."
                                    }
                                },
                                "required": ["skill_number", "task"]
                            }
                        }
                    },
                    "required": ["skills"]
                }
            }
        }
    ]

    confirmation_tool = [
        {
            "type": "function",
            "function": {
                "name": "assess_solvability",
                "description": (
                    "Assess whether a given student question is solvable using only the provided context. "
                    "Do not solve the question. Return only a boolean 'solvable' and a concise 'reasoning' (2–6 sentences) "
                    "explaining why it is or isn't solvable."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "solvable": {
                            "type": "boolean",
                            "description": "True if the question can be uniquely and verifiably solved with the provided context; false otherwise."
                        },
                        "reasoning": {
                            "type": "string",
                            "description": (
                                "A concise justification (1–5 sentences) for the solvability assessment. "
                                "Do not provide solutions or solution steps."
                            )
                        }
                    },
                    "required": ["solvable", "reasoning"],
                }
            }
        }
    ]

    # Model params (primary prompt)
    temperature = develop_prompt.config.get("openai", {}).get("temperature", 0.5)
    model = develop_prompt.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = develop_prompt.config.get("openai", {}).get("max_tokens", 4096)

    # Decide what to pass as "context" for the solvability agent
    if assignment_type in ('vocab_fill', 'reading_comp_gen', 'history_fact'):
        eligibility_context = refined_passage
    else:
        logger.warning(f"Unrecognized assignment_type: {assignment_type}")

    logger.info(f"inside develop_tasks_passage_agent: nb_of_questions={nb_of_questions}")

    # Rotating pointer over sub-standards so we do not restart from the beginning
    sub_idx = 0

    # Safety stop: avoid infinite loops if questions keep failing
    max_total_attempts = max(nb_of_questions * MAX_TOTAL_ATTEMPTS_MULTIPLIER, nb_of_questions + 5)
    attempts = 0

    # Helper to extract the first task from the tool output
    def _extract_single_task(result_obj: Dict) -> Dict:
        try:
            skills = result_obj.get("skills", [])
            if not isinstance(skills, list) or len(skills) == 0:
                return {}
            # Use the first skill as the candidate
            first = skills[0] if isinstance(skills[0], dict) else {}
            if "skill_number" in first and "task" in first and isinstance(first["task"], str):
                return first
            return {}
        except Exception:
            return {}

    while len(tasks_generated) < nb_of_questions and attempts < max_total_attempts:
        attempts += 1

        current_sub = sub_standard[sub_idx]
        sub_idx = (sub_idx + 1) % total_substandards

        develop_chat = develop_prompt.compile(
            passage=refined_passage,
            sub_standard=[current_sub],  
            grade=grade,
            additional_instruction=additional_instruction,
            already_generated_questions=tasks_generated
        )
        gen_result = make_openai_langfuse(
            develop_chat,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            tools=tools
        )

        candidate = _extract_single_task(gen_result)
        if not candidate:
            logger.warning("No valid task returned by model for current sub-standard; retrying next.")
            continue

        # Per-task retry loop if not solvable
        retries = 0
        while retries <= MAX_RETRIES_PER_TASK:
            # Run solvability check on this single question
            confirm_chat = confirmation_prompt.compile(
                question=candidate.get("task", ""),
                context=eligibility_context
            )
            decision = make_openai_langfuse(
                confirm_chat,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                tools=confirmation_tool
            )

            solvable = None
            reasoning = ""
            if isinstance(decision, dict):
                solvable = decision.get("solvable", None)
                reasoning = decision.get("reasoning", "")

            logger.info(f"Solvability decision: solvable={solvable}, reasoning={reasoning}")

            if solvable is True:
                tasks_generated.append(candidate)
                break  # accepted, move to next sub-standard

            # Not solvable: retry just this task for the same sub-standard
            logger.info("Question is not solvable; attempting targeted retry for the same sub-standard.")
            retries += 1
            if retries > MAX_RETRIES_PER_TASK:
                logger.warning("Max retries hit for this sub-standard; moving on.")
                break

            # Regenerate a fresh candidate for the same sub-standard
            gen_result = make_openai_langfuse(
                develop_chat,  # same compiled chat with same single sub-standard
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                tools=tools
            )
            candidate = _extract_single_task(gen_result)
            if not candidate:
                logger.warning("Retry failed to produce a valid task; trying again if retries remain.")

    if attempts >= max_total_attempts and len(tasks_generated) < nb_of_questions:
        logger.warning(f"Stopped after {attempts} attempts with {len(tasks_generated)} tasks. Consider increasing limits or reviewing prompts.")

    return tasks_generated[:nb_of_questions]