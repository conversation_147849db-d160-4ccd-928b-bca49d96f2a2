# import pytest
# from unittest.mock import patch, MagicMock


# # ✅ SUCCESS CASE
# @patch("service.common_agents.kindergarten_adjustments_agent.get_guideline_by_level")
# @patch("service.common_agents.kindergarten_adjustments_agent.langfuse.get_prompt")
# @patch("service.common_agents.kindergarten_adjustments_agent.make_openai_langfuse")
# @patch("service.common_agents.kindergarten_adjustments_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
# def test_kindergarten_adjustment_agent_success(mock_make_openai_langfuse, mock_get_prompt, mock_get_guideline):
#     from service.common_agents.kindergarten_adjustments_agent import kindergarten_adjustment_agent

#     # Setup input and mock returns
#     student = {"name": "John", "grade": "K"}
#     json_response = {"assignment": "Original assignment content"}
#     nb = 2
#     tools = {"tool": "mocked_tool"}

#     mock_get_guideline.return_value = "Use very simple words and short sentences."
    
#     mock_prompt = MagicMock()
#     mock_prompt.compile.return_value = "compiled_prompt"
#     mock_prompt.config = {
#         "openai": {
#             "temperature": 0.5,
#             "model": "gpt-4.1",
#             "max_tokens": 4096
#         }
#     }
#     mock_get_prompt.return_value = mock_prompt

#     mock_make_openai_langfuse.return_value = {"assignment": "Adjusted for kindergarten"}

#     # Run the agent
#     result = kindergarten_adjustment_agent(json_response, student, nb, tools, "reading")

#     # Assert adjusted result
#     assert result["assignment"] == "Adjusted for kindergarten"


# # ❌ FAILURE CASE: Simulate guideline fetch failure, fallback to original
# @patch("service.common_agents.kindergarten_adjustments_agent.get_guideline_by_level")
# @patch("service.common_agents.kindergarten_adjustments_agent.langfuse.get_prompt")
# @patch("service.common_agents.kindergarten_adjustments_agent.make_openai_langfuse")
# @patch("service.common_agents.kindergarten_adjustments_agent.aisdk_object.cache_result", lambda *a, **kw: lambda f: f)
# def test_kindergarten_adjustment_agent_failure_returns_original(mock_make_openai_langfuse, mock_get_prompt, mock_get_guideline):
#     from service.common_agents.kindergarten_adjustments_agent import kindergarten_adjustment_agent

#     # Setup input
#     student = {"name": "Jane", "grade": "K"}
#     json_response = {"assignment": "Original fallback content"}
#     nb = 1
#     tools = {}

#     # Mocking an exception in guideline fetching
#     mock_get_guideline.side_effect = Exception("Guideline fetch failed")

#     # Run the agent
#     result = kindergarten_adjustment_agent(json_response, student, nb, tools, "math")

#     # Assert fallback to original content
#     assert result == json_response
