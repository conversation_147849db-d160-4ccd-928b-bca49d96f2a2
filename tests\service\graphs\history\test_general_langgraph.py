# import pytest
# from unittest.mock import MagicMock, patch
# from service.gen_data_models import GeneratePipelineContext, GenerateRequests
# from aisdk.data_models.people import Student
# from service.graphs.history import general_langgraph


# def get_mock_context():
#     student = Student(
#         first_name="<PERSON>",
#         last_name="<PERSON><PERSON>",
#         student_id="123",
#         age=10,
#         is_dyslexic=False,
#         strengths=["curiosity"],
#         working_style="analytical",  # must be one of the allowed values
#         reading_level="grade_4",
#         grade="5"
#     )

#     request = GenerateRequests(
#         assignment_type="essay",
#         add_info=True
#     )

#     return GeneratePipelineContext(
#         student=student,
#         request=request,
#         request_id="test-req-001",
#         assignment_state="",
#         previous_assignments=[]
#     )


# @patch("service.graphs.history.general_langgraph.StateGraph.compile")
# def test_history_general_langgraph_success(mock_compile):
#     mock_chain = MagicMock()
#     mock_chain.invoke.return_value = {"assignment": {"title": "Mock History Homework"}}
#     mock_compile.return_value = mock_chain

#     context = get_mock_context()
#     result = general_langgraph.history_general_langgraph(context)
#     assert "assignment" in result
#     assert result["assignment"]["title"] == "Mock History Homework"


# @patch("service.graphs.history.general_langgraph.StateGraph.compile")
# def test_history_general_langgraph_missing_assignment_key(mock_compile):
#     # Simulate missing 'assignment' key
#     mock_chain = MagicMock()
#     mock_chain.invoke.return_value = {"unexpected": "data"}
#     mock_compile.return_value = mock_chain

#     context = get_mock_context()
#     result = general_langgraph.history_general_langgraph(context)
#     assert "assignment" not in result  # Failing test


# @patch("service.graphs.history.general_langgraph.StateGraph.compile", side_effect=Exception("Graph failed"))
# def test_history_general_langgraph_compile_failure(mock_compile):
#     context = get_mock_context()
#     with pytest.raises(Exception, match="Graph failed"):
#         general_langgraph.history_general_langgraph(context)


# @patch("service.graphs.history.general_langgraph.StateGraph.compile")
# def test_history_general_langgraph_invoke_failure(mock_compile):
#     mock_chain = MagicMock()
#     mock_chain.invoke.side_effect = Exception("Invoke error")
#     mock_compile.return_value = mock_chain

#     context = get_mock_context()
#     with pytest.raises(Exception, match="Invoke error"):
#         general_langgraph.history_general_langgraph(context)
