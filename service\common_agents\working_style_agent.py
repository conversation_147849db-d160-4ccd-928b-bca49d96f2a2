from service.openai_utils.gpt_call import make_openai_langfuse
from service.common_functions import contingent_working_style
from service.context_pipeline import logger
from service.context_pipeline import langfuse

def identify_working_style_inclusion(initial_assignment: dict, working_style: str, assignment_type: str, nb:int) -> dict:
    """
    Core agent functionality that evaluates an assignment to determine whether it
    effectively incorporates a student's specified working style.

    Args:
        json_response: The JSON object containing the assignment data.
        working_style: The student's working style to be evaluated within the assignment.

    Returns:
        dict: A response indicating whether the assignment appropriately includes
             the student's working style (True/False).
    """
    logger.info(f"Starting identify_working_style_inclusion for working style: {working_style}")
    try:
        working_style=contingent_working_style(working_style)
        logger.debug("Creating system and user messages for working style evaluation")
        messages = langfuse.get_prompt("identify_working_style", label="latest")
        complete_chat_prompt = messages.compile(
            working_style = working_style,
            initial_assignment=initial_assignment
        )

        logger.debug("Setting up tools for working style evaluation")
        tools=[
            {
                "type": "function",
                "function": {
                    "name": "assess_assignment_working_style",
                    "description": "Assess whether a homework assignment appropriately reflects the student's preferred working style based on task structure, engagement, and clarity.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "supports_working_style": {
                                "type": "boolean",
                                "description": "Indicates whether the assignment aligns well with the student’s working style."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": "Clear and concise explanation of the determination regarding working style integration."
                            },
                            "next_steps": {
                                "type": "string",
                                "description": "Detailed explanation of what actions should be taken to maintain or improve working style alignment."
                            }
                        },
                        "required": ["supports_working_style", "reasoning", "next_steps"]
                    }
                }
            }
        ]

        logger.debug("Making OpenAI request for working style evaluation")
       
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)

        result = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens)
        logger.info("Successfully evaluated working style inclusion")
        return result
    
    except Exception as e:
        logger.error(f"Error in identify_working_style_inclusion: {e}")
        return {"adjust_working_style": False}