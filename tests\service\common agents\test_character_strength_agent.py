import unittest
from unittest.mock import patch, MagicMock

# Patch BEFORE importing the function
patcher = patch('service.common_agents.character_strength_agent.langfuse.get_prompt')
patcher.start()
patcher2 = patch('service.common_agents.character_strength_agent.make_openai_langfuse')
patcher2.start()

from service.common_agents.character_strength_agent import assess_strengths

class TestAssessStrengths(unittest.TestCase):

    @patch('service.common_agents.character_strength_agent.make_openai_langfuse')
    @patch('service.common_agents.character_strength_agent.langfuse.get_prompt')
    def test_assess_strengths_with_valid_input(self, mock_get_prompt, mock_make_openai_langfuse):
        # Mock prompt config and compile
        mock_prompt = MagicMock()
        mock_prompt.compile.return_value = "compiled_prompt"
        mock_prompt.config = {
            "openai": {
                "temperature": 0.5,
                "model": "gpt-4.1",
                "max_tokens": 4096
            }
        }
        mock_get_prompt.return_value = mock_prompt

        # Mock OpenAI response
        mock_make_openai_langfuse.return_value = {
            "supports_strengths": True,
            "reasoning": "The assignment integrates creativity and perseverance.",
            "next_steps": "Continue embedding opportunities for expression and challenge."
        }

        initial_assignment = {
            "title": "Design a robot to solve a real-world problem.",
            "instructions": "Use your imagination and engineering skills."
        }
        character_strengths = "creativity, perseverance"
        nb_of_questions = 1
        assignment_type = "project"

        result = assess_strengths(initial_assignment, character_strengths, nb_of_questions, assignment_type)

        assert isinstance(result, dict)
        assert result["supports_strengths"] is True
        assert "reasoning" in result
        assert "next_steps" in result

    @patch('service.common_agents.character_strength_agent.make_openai_langfuse')
    @patch('service.common_agents.character_strength_agent.langfuse.get_prompt')
    def test_assess_strengths_handles_exception(self, mock_get_prompt, mock_make_openai_langfuse):
        # Trigger exception to test fallback behavior
        mock_get_prompt.side_effect = Exception("Langfuse error")

        result = assess_strengths({}, "curiosity", 1, "essay")

        assert isinstance(result, dict)
        assert result["supports_strengths"] is True  # fallback behavior

if __name__ == '__main__':
    unittest.main()

# Stop patching after imports
patcher.stop()
patcher2.stop()
