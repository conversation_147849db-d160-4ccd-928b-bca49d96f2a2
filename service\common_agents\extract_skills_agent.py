from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger

def extract_skills_agent(understand_standard_output:str,grade: str,standard_id: str,common_core_standard:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
       
    messages = langfuse.get_prompt('extract_skills', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        understand_standard_output=understand_standard_output,
        grade=grade,
        standard_id=standard_id,
        common_core_standard=common_core_standard
        )


    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_skill_demonstrations",
                "description": "Produces an ordered list of skill objects, each containing a skill description",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "blueprint": {
                            "type": "array",
                            "description": (
                                "A list of skill objects. Each object must include a "
                                "'skill' field and a 'demonstration_example' field."
                            ),
                            "items": {
                                "type": "object",
                                "properties": {
                                    "skill_number": {
                                        "type": "string",
                                        "description": "The number of the skill"
                                    },
                                    "skill": {
                                        "type": "string",
                                        "description": "A concise description of the competency or task."
                                    },
                                },
                                "required": ["skill_number","skill"]
                            }
                        }
                    },
                    "required": ["blueprint"]
                }
            }
        }
    ]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)

    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    return result