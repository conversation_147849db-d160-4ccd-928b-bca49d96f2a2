# service/supporting_graph_functions.py
from service.gen_data_models import GeneratePipelineContext
from langgraph.graph import END
from service.context_pipeline import logger

def start(state: GeneratePipelineContext):
    return state

def route_final_steps(state: GeneratePipelineContext):
    logger.info("--- Routing: Final Steps ---")

    language = state.request.language
    done = state.adjustments_done

    # if language != "english" and not done["translated"]:
    #     return ["translate_assignment"]

    return [END]


def check_if_translation_needed(state: GeneratePipelineContext):
    language = state.request.language
    if language != "english":
        return "Yes"
    return "No"
def check_if_kindergarten(state: GeneratePipelineContext):
    logger.info(f"Checking if grade is kindergarten: {state.student.grade}")
    if state.student.grade == "kindergarten":
        return "Yes"
    return "No"

def check_if_passage_refinement_needed(state: GeneratePipelineContext):
    logger.info(f"Checking if passage refinement is needed: {state.refine_passage}")
    if state.refine_passage.lower()=='fully addresses':
        return "No"
    return "Yes"

def update_passages_manually(assignment,enriched_tasks_passages):
    """
    Replace the 'passage' field in every element of `targets`
    with the matching passage found in `sources`, using 'skill_number'
    as the key.

    Parameters
    ----------
    sources : list of dict
        Each dict **must** contain the keys
            'skill_number' (str or int) and 'passage'.
    targets : list of dict
        Each dict **must** contain the key
            'skill_number' (str or int).
        If 'passage' is present it will be overwritten.

    Returns
    -------
    list of dict
        The same list object that was supplied as `targets`,
        now updated.
    """
    challenges=assignment['homework']['challenges']
    passage_lookup = {
        str(item["index"]): item["passage"]
        for item in enriched_tasks_passages
        if "index" in item and "passage" in item
    }

    for record in challenges:
        key = str(record.get("index"))
        if key in passage_lookup:
            record["passage"] = passage_lookup[key]
    assignment['homework']['challenges']=challenges
    logger.info(f'assignment after update: {assignment}')
    return assignment
def updated_passages_manually_node(state: GeneratePipelineContext):
    logger.info(f"Updated passages manually: {state.ai_passage}")
    assignment=state.assignment
    enriched_tasks_passages=state.enriched_tasks_passages
    assignment=update_passages_manually(assignment,enriched_tasks_passages)
    return {"assignment": assignment}