from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.initial_assignments.initial_math_assignments import generate_initial_math_assignment

def generate_initial_math_assignment_node(state: GeneratePipelineContext):
    """Extracts inputs from state and passes them to the generator function."""
    logger.info("---Initial Assignment---")

    grade = state.student.grade
    first_name = state.student.first_name
    working_style = state.student.working_style
    reading_level = state.student.reading_level
    strengths = state.student.strengths
    state_standard = state.request.state_standard
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    nb_of_questions = state.request.nb_of_questions
    difficulty = state.request.difficulty
    interest=state.student.interest
    tasks_enriched_with_interest = state.enriched_tasks
    logger.info("Students data extracted")

    try:
        output, tools = generate_initial_math_assignment(
        grade=grade,
        first_name=first_name,
        working_style=working_style,
        reading_level=reading_level,
        interests=interest,
        strengths=strengths,
        state_standard=state_standard,
        standard=standard,
        assignment_type=assignment_type,
        nb_of_questions=nb_of_questions,
        difficulty=difficulty,
        tasks_enriched_with_interest=tasks_enriched_with_interest
    )
        logger.info(f"Initial assignment generated: {output}")
        if 'homework' in output and hasattr(state,'challenges'):
            output['homework']['challenges']=state.challenges
        elif hasattr(state,'challenges'):
            output['challenges']=state.challenges
        else:
            logger.error("Challenges not found in state")

        return {"assignment": output, "tools": tools, 'assignment_state': 'processing_assignment', 'attempts': 0}

    except Exception as e:
        logger.error(f"Error in generate_initial_math_assignment_node: {e}")
        return {"error": "Failed to generate initial assignment."}
