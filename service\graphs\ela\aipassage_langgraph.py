# service/graphs/ela/aipassage_langgraph.py
from langgraph.graph import StateGraph
from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.nodes.initial_assignments.initial_ela_node import generate_initial_ela_passage_node, generate_initial_ela_assignment_node
from service.nodes.common_agents.align_additional_info_node import check_additional_info_node
from service.nodes.common_agents.character_strength_node import assess_strengths_node
from service.nodes.common_agents.working_style_node import working_style_inclusion_node
from service.nodes.common_agents.identify_difficulty_node import adjust_assignment_difficulty_node
from service.nodes.common_agents.interest_alignment_node import interest_inclusion_node,interest_search_node
from service.nodes.common_agents.standard_node import assess_standard_node
from service.nodes.common_agents.aggregator_node import aggregator_node, aggregator_passage_node
from service.nodes.common_agents.generate_rubrics_node import rubric_questions_node
from service.nodes.common_agents.generate_rubrics_hints_node import rubric_hints_node
from service.nodes.common_agents.kindergarten_adjustment_node import kindergarten_adjustment_node
from service.nodes.common_agents.translate_assignment_node import translate_assignment_node
from service.nodes.common_agents.dyslexia_node import dyslexia_agent_node
from service.supporting_graph_functions import route_final_steps, start,check_if_passage_refinement_needed,check_if_kindergarten,check_if_translation_needed
from service.nodes.reading_level.reading_level_node import check_reading_level_node, align_reading_level_node
from service.nodes.common_agents.answer_key_verification_node import answer_key_verification_node
from service.nodes.common_agents.inspect_passage_substandard_alignment_node import inspect_passage_substandard_alignment_node
from service.nodes.common_agents.develop_task_passage_node import develop_tasks_passage_node
from service.nodes.common_agents.rewrite_passage_to_align_substandard_node import rewrite_passage_to_align_substandard_node
from service.nodes.common_agents.standard_passage_alignment_node import standard_passage_alignment_node
def ela_passage_langgraph(ptx:GeneratePipelineContext,langfuse_handler):
    logger.info("Creating Passage Based Assignment Graph (Example Pattern Version)")
    try:
        workflow = StateGraph(GeneratePipelineContext)
        workflow.add_node("generate_initial_ela_passage_node", generate_initial_ela_passage_node)
        workflow.add_node("adjust_difficulty_passage", adjust_assignment_difficulty_node)
        workflow.add_node("align_to_interest_passage", interest_inclusion_node)
        workflow.add_node("align_reading_level_passage", align_reading_level_node)
        workflow.add_node("assess_strengths_passage", assess_strengths_node)
        workflow.add_node("working_style_inclusion_passge", working_style_inclusion_node)
        workflow.add_node("aggregator_passage", aggregator_passage_node)
        workflow.add_node("dyslexia_agent_passage", dyslexia_agent_node)
        workflow.add_node("generate_initial_ela_assignment_node", generate_initial_ela_assignment_node)
        workflow.add_node("check_additional_info", check_additional_info_node)
        workflow.add_node("assess_strengths", assess_strengths_node)
        workflow.add_node("working_style_inclusion", working_style_inclusion_node)
        workflow.add_node("adjust_difficulty", adjust_assignment_difficulty_node)
        workflow.add_node("align_to_interest", interest_inclusion_node)
        workflow.add_node("assess_standard", assess_standard_node)
        workflow.add_node("aggregator", aggregator_node)
        workflow.add_node("generate_rubric_questions", rubric_questions_node)
        workflow.add_node("generate_rubrics_hints", rubric_hints_node)
        workflow.add_node("kindergarten_adjustment", kindergarten_adjustment_node)
        workflow.add_node("translate_assignment", translate_assignment_node)
        workflow.add_node("dyslexia_agent", dyslexia_agent_node)
        workflow.add_node("align_reading_level", align_reading_level_node)
        workflow.add_node("answer_key_verification", answer_key_verification_node)
        workflow.add_node("start", start)
        workflow.add_node("interest_search_node", interest_search_node)
        workflow.add_node("inspect_passage_substandard_alignment_node", inspect_passage_substandard_alignment_node)
        workflow.add_node("rewrite_passage_to_align_substandard_node", rewrite_passage_to_align_substandard_node)
        workflow.add_node("develop_tasks_passage_node", develop_tasks_passage_node)
        workflow.add_node("standard_passage_alignment_node", standard_passage_alignment_node)
        workflow.set_entry_point("start")
        workflow.add_edge("start", "generate_initial_ela_passage_node")
        workflow.add_edge("start",'interest_search_node')
        workflow.add_edge("interest_search_node", "align_to_interest_passage")

        workflow.add_edge("generate_initial_ela_passage_node", "adjust_difficulty_passage")
        workflow.add_edge("generate_initial_ela_passage_node", "assess_strengths_passage")
        workflow.add_edge("generate_initial_ela_passage_node", "working_style_inclusion_passge")

        if ptx.student.is_dyslexic:
            workflow.add_edge("generate_initial_ela_passage_node", "dyslexia_agent_passage")
            workflow.add_edge("dyslexia_agent_passage", "aggregator_passage")

        workflow.add_edge("assess_strengths_passage", "aggregator_passage")
        workflow.add_edge("working_style_inclusion_passge", "aggregator_passage")
        workflow.add_edge("adjust_difficulty_passage", "aggregator_passage")
        workflow.add_edge("align_to_interest_passage", "aggregator_passage")
        workflow.add_edge("aggregator_passage", "inspect_passage_substandard_alignment_node")
        workflow.add_conditional_edges("inspect_passage_substandard_alignment_node",check_if_passage_refinement_needed,{ "Yes":"standard_passage_alignment_node","No":"develop_tasks_passage_node"})
        workflow.add_edge("standard_passage_alignment_node", "rewrite_passage_to_align_substandard_node")
        workflow.add_edge("rewrite_passage_to_align_substandard_node", "develop_tasks_passage_node")

        workflow.add_edge("develop_tasks_passage_node", "align_reading_level_passage")
        workflow.add_conditional_edges("align_reading_level_passage", check_reading_level_node, {"Pass": "generate_initial_ela_assignment_node", "Fail": "align_reading_level_passage"} )

        # workflow.add_edge("generate_initial_ela_assignment_node", "assess_strengths")
        # workflow.add_edge("generate_initial_ela_assignment_node", "working_style_inclusion")
        # workflow.add_edge("generate_initial_ela_assignment_node", "adjust_difficulty")
        # #workflow.add_edge("generate_initial_ela_assignment_node", "align_to_interest")
        # workflow.add_edge("generate_initial_ela_assignment_node", "assess_standard")
        
        # if ptx.request.add_info:
        #     workflow.add_edge("generate_initial_ela_assignment_node", "check_additional_info")
        #     workflow.add_edge("check_additional_info", "aggregator")
        
        # if ptx.student.is_dyslexic:
        #     workflow.add_edge("generate_initial_ela_assignment_node", "dyslexia_agent")
        #     workflow.add_edge("dyslexia_agent", "aggregator")
        

        # workflow.add_edge("assess_strengths", "aggregator")
        # workflow.add_edge("working_style_inclusion", "aggregator")
        # workflow.add_edge("adjust_difficulty", "aggregator")
        # #workflow.add_edge("align_to_interest", "aggregator")
        # workflow.add_edge("assess_standard", "aggregator")
        workflow.add_conditional_edges("generate_initial_ela_assignment_node", check_if_translation_needed, {"Yes": "translate_assignment", "No": "answer_key_verification"})
        # workflow.add_edge("generate_initial_ela_assignment_node", "translate_assignment")
        # workflow.add_edge("translate_assignment","answer_key_verification")
        workflow.add_edge("translate_assignment", "answer_key_verification")
        workflow.add_conditional_edges("answer_key_verification", check_if_kindergarten, {"Yes": "kindergarten_adjustment", "No": "generate_rubric_questions"})
        
        workflow.add_edge("kindergarten_adjustment", "generate_rubric_questions")
        #workflow.add_edge("generate_rubric_questions", "generate_rubrics_hints")
        workflow.add_conditional_edges("generate_rubric_questions", route_final_steps)

        chain = workflow.compile()
        logger.info("Graph compiled successfully (Example Pattern Version)")

    except Exception as e:
        logger.error(f"Error creating graph: {e}", exc_info=True)
        raise

    try:
        logger.info("Invoking graph...")
        output = chain.invoke(input=ptx, config={
                "callbacks": [langfuse_handler]
            })
        logger.info(f"Graph invocation complete.:{output}")
        # Check if 'assignment' key exists before returning
        if output and 'assignment' in output:
            if 'ai_passage' in output:
                logger.info(f"AI Passage Generated: {output['ai_passage']}")
                if 'passage' in output['ai_passage']:
                    output['ai_passage']=output['ai_passage']['passage']
            output['assignment']['passage'] = "\n\n".join(output['ai_passage'])
            if 'homework' in output['assignment']:
                output['assignment']['substandards_data']=output['substandards_data']
                output['assignment']['standard']=output['request'].standard
                return output['assignment']
            else:
                logger.info("No homework in assignment")
                return {"homework": output['assignment'], "substandards_data": output['substandards_data'],"standard": output['request'].standard}
        else:
            logger.error(f"Graph finished but 'assignment' key missing in final output: {output}")
            return {"homework": None, "error": "Assignment data missing in final state"}

    except Exception as e:
        logger.error(f"Error invoking graph: {e}", exc_info=True) 
        raise
        return {"homework": None, "error": str(e)}


def check_if_kindergarten(state: GeneratePipelineContext):
    logger.info(f"Checking if grade is kindergarten: {state.student.grade}")
    if state.student.grade == "kindergarten":
        return "Yes"
    return "No"
