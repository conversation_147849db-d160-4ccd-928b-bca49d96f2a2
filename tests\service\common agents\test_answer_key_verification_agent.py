# import unittest
# from unittest.mock import patch, MagicMock

# # Patch the decorator BEFORE importing the function
# patcher = patch('service.common_agents.answer_key_verification_agent.aisdk_object.cache_result', lambda *a, **kw: lambda f: f)
# patcher.start()

# from service.common_agents.answer_key_verification_agent import answer_key_verification

# class TestAnswerKeyVerification(unittest.TestCase):

#     @patch('service.common_agents.answer_key_verification_agent.make_openai_langfuse')
#     @patch('service.common_agents.answer_key_verification_agent.langfuse.get_prompt')
#     def test_answer_key_verification_math(self, mock_get_prompt, mock_make_openai_langfuse):
#         mock_messages = MagicMock()
#         mock_messages.compile.return_value = "compiled_math_prompt"
#         mock_messages.config = {
#             "openai": {
#                 "temperature": 0.3,
#                 "model": "gpt-4.1",
#                 "max_tokens": 3000
#             }
#         }
#         mock_get_prompt.return_value = mock_messages

#         mock_make_openai_langfuse.return_value = {
#             "verified_assignment": "Updated assignment with correct answer keys."
#         }

#         json_response = {"homework": [{"question": "1+1", "answer": "2"}]}
#         assignment_type = "math"
#         nb = 1
#         tools = {}

#         result = answer_key_verification(json_response, assignment_type, nb, tools)
#         self.assertIn("verified_assignment", result)

#     @patch('service.common_agents.answer_key_verification_agent.make_openai_langfuse')
#     @patch('service.common_agents.answer_key_verification_agent.langfuse.get_prompt')
#     def test_answer_key_verification_essay(self, mock_get_prompt, mock_make_openai_langfuse):
#         mock_messages = MagicMock()
#         mock_messages.compile.return_value = "compiled_essay_prompt"
#         mock_messages.config = {
#             "openai": {
#                 "temperature": 0.5,
#                 "model": "gpt-4",
#                 "max_tokens": 2048
#             }
#         }
#         mock_get_prompt.return_value = mock_messages

#         mock_make_openai_langfuse.return_value = {
#             "verified_assignment": "Verified essay assignment with key points"
#         }

#         json_response = {"essay": "Discuss climate change solutions"}
#         assignment_type = "essay"
#         nb = 2
#         tools = {}

#         result = answer_key_verification(json_response, assignment_type, nb, tools)
#         self.assertIn("verified_assignment", result)


# if __name__ == '__main__':
#     unittest.main()

# # Stop the patcher after test run
# patcher.stop()
