from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object

def check_additional_info_assignment_agent(grade,add_info,initial_assignment, nb_of_questions, assignment_type):
    messages = langfuse.get_prompt('check_additionalinfo_inclusion', type='chat', label="latest")
    complete_chat_prompt = messages.compile(
        grade=grade,
        additional_information=add_info,
        generated_assignment=initial_assignment,
    )
    tools = [
    {
        "type": "function",
        "function": {
            "name": "check_additional_information_presence",
            "description": "Check whether the generated assignment includes specified additional information or context.",
            "parameters": {
                "type": "object",
                "properties": {
                    "additional_info_inclusion": {
                        "type": "boolean",
                        "description": "Indicates whether the specified additional information is present in the assignment."
                    },
                    "reasoning": {
                        "type": "string",
                        "description": "Clear and concise explanation for why the information is considered present or missing."
                    },
                    "next_steps": {
                        "type": "string",
                        "description": "Detailed explanation of what actions should be taken to maintain or better include the additional information element." 
                    }
                },
                "required": ["additional_info_inclusion", "reasoning", "next_steps"]
            }
        }
    }
]

    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
    result = make_openai_langfuse(complete_chat_prompt, tools=tools, temperature=temperature, model=model, max_tokens=max_tokens)
    return result