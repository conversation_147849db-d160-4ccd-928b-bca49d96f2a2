from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.character_strength_agent import assess_strengths
from service.context_pipeline import logger

def assess_strengths_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles assessment of character strengths in assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting assess_strengths_node")

    character_strengths = state.student.strengths
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type
    
    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif state.assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    try:
        strengths_assessment = assess_strengths(
            initial_assignment=initial_content,
            character_strengths=character_strengths,
            nb_of_questions=nb,
            assignment_type=assignment_type
        )
        logger.debug(f"Strengths assessment response: {strengths_assessment}")

        next_steps = strengths_assessment.get('next_steps', '').strip()
        if next_steps:
            state.adjustment_instructions['character_strengths'] = next_steps
            logger.info("Character strengths adjustment instructions updated.")
        else:
            logger.warning("Adjustment indicated but no 'next_steps' provided.")
        
        return {"adjustment_instructions": state.adjustment_instructions}

    except Exception as e:
        logger.error(f"Error in assess_strengths_node: {e}")
        # Return no adjustments explicitly in case of error
        return {}