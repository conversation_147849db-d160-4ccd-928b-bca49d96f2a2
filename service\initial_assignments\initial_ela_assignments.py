from service.openai_utils.gpt_call import make_openai_langfuse
from service.common_functions import contingent_working_style, reading_level_example, character_strength_explanations,life_skills_explanations
from service.context_pipeline import logger, aisdk_object
from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import langfuse
from service.common_functions import vocab_examples
from service.common_functions import character_strength_explanations
import copy

def generate_initial_ela_passage( grade, first_name, working_style, reading_level, life_skills, passage_style, interest, state_standard, standard, assignment_type, add_info, difficulty, strengths, nb_of_questions):
    """Generates the initial passage using extracted inputs (no dependency on state)."""
    ls_explanations = life_skills_explanations(life_skills, standard)
    cs_explanations = character_strength_explanations(strengths)

    passage_explanations = (
        f"Please write a {passage_style} passage aimed at a {difficulty} difficulty level. "
        f"The language, sentence structure, and vocabulary should be appropriate for a(n) "
        f"{reading_level} reading level. This passage must align with the educational standard: "
        f"'{standard}'. Ensure that the style, complexity, and content are all suitable for "
        f"the specified reading level and help students practice skills outlined in {standard}."
    )

    standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')
    working_style_data = contingent_working_style(working_style)
    example_reading_level = reading_level_example(reading_level)

    if assignment_type == 'reading_comp_gen':
        logger.info("Generating reading comp gen passage")
        augmentation_prompt = langfuse.get_prompt('reading_comp_gen_passage_generation', type='chat', label="latest")

        add_info_instructions = ""
        additional_instructions = ""

        if add_info:
            add_info_instructions += (
                f"\nAdditionally, incorporate the following information into the reading passage to further enhance the relevance and engagement for {first_name}: {add_info}. "
                f"Ensure that this information is seamlessly integrated into the storyline and educational objectives."
            )

        if grade == "kindergarten" or '1' in grade:
            additional_instructions += "\nPlease ensure the reading passage is specifically written for kindergarten or first-grade students, using only age-appropriate vocabulary and simple sentence structure. The passage must not exceed 100 words in total. It should be highly engaging for young children and must be limited to no more than 2 short paragraphs. Absolutely avoid any content, concepts, or language that could be confusing or unsuitable for early readers. Do not go over the 100-word limit or more than 2 paragraphs under any circumstances."
        else:
            additional_instructions += f"\nPlease ensure the reading passage is suitable for {grade} grade. Ensure that the generated passage is at least 100 words."
        additional_instructions += f"Please ensure that the passage has enough context to be able to generate {nb_of_questions} distinct questions"
        complete_chat_prompt = augmentation_prompt.compile(
            first_name=first_name,
            grade=grade,
            state_standard=state_standard,
            standard=standard_data,
            interests=interest,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            ls_explanations=ls_explanations,
            passage_explanations=passage_explanations,
            add_info_instructions=add_info_instructions,
            additional_instruction=additional_instructions,
            strengths=strengths,
        )

        logger.info(f"complete chat prompt: {complete_chat_prompt}")

        tools = [{
        "type": "function",
        "function": {
            "name": "generate_reading_passage",
            "description": "Generates a reading passage based on the students interest",
            "parameters": {
                "type": "object",
                "properties": {
                    "passage": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the generated reading passage"
                        }
                    }
                },
                "required": ["passage"]
            }
        }
    }]
    elif assignment_type == 'vocab_fill':
        logger.info("Generating reading passage")
        words = vocab_examples(grade)

        augmentation_prompt = langfuse.get_prompt('vocab_fill_passage_generation', type='chat', label="latest")

        add_info_instructions = ""
        additional_instructions = ""

        if add_info:
            add_info_instructions += (
                f"\nAdditionally, incorporate the following information into the reading passage to further enhance the relevance and engagement for {first_name}: {add_info}. "
                f"Ensure that this information is seamlessly integrated into the storyline and educational objectives."
            )

        if grade == "kindergarten" or '1' in grade:
            additional_instructions += "\nPlease ensure the reading passage is suitable for a kindergarten/first-grade student and short enough to maintain engagement, try to stay below 100 words."
        else:
            additional_instructions += f"\nPlease ensure the reading passage is suitable for {grade} grade. Ensure that the generated passage is at least 100 words."

        complete_chat_prompt = augmentation_prompt.compile(
            first_name=first_name,
            grade=grade,
            words=words,
            interests=interest,
            state_standard=state_standard,
            standard=standard_data,
            reading_level=reading_level,
            working_style=working_style,
            strengths=strengths,
            reading_level_example=example_reading_level,
            working_style_data=working_style_data,
            cs_explanations=cs_explanations,
            add_info_instructions=add_info_instructions,
            additional_instruction=additional_instructions,
        )

        logger.info(f"complete chat prompt: {complete_chat_prompt}" )

        tools = [{
        "type": "function",
        "function": {
            "name": "generate_reading_passage",
            "description": "Generates a reading passage based on the provided vocabulary words and students interest",
            "parameters": {
                "type": "object",
                "properties": {
                    "passage": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the generated reading passage"
                        }
                    }
                },
                "required": ["passage"]
            }
        }
    }]
    model=augmentation_prompt.config.get("openai", {}).get("model","gpt-4.1")
    temperature=augmentation_prompt.config.get("openai", {}).get("temperature",0.5)
    max_tokens=augmentation_prompt.config.get("openai", {}).get("max_tokens",4096)
    ai_passage = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens)
    logger.info(f"AI passage: {ai_passage}")

    return ai_passage['passage'],tools

def generate_initial_ela_assignment( grade, first_name, working_style, reading_level, life_skills, strengths, state_standard, standard, assignment_type, nb_of_questions, difficulty, ai_passage, interest, passage,enriched_tasks):
    """Generates initial assignment using extracted inputs (no dependency on state)."""
    logger.info("INITIAL ASSIGNMENT")
    
    cs_explanations = character_strength_explanations(strengths)
    ls_explanations = life_skills_explanations(life_skills, standard)
    standard_data = standard.get('common_core_standard') if standard.get('common_core_standard') else standard.get('cluster_statement')
    working_style_data = contingent_working_style(working_style)
    example_reading_level = reading_level_example(reading_level)

    if assignment_type == 'reading_comp_gen':
        augmentation_prompt = langfuse.get_prompt('reading_comp_gen_questions_generation', type='chat', label="latest")
        
        complete_chat_prompt = augmentation_prompt.compile(
            nb_of_questions=nb_of_questions,
            passage=ai_passage,
            state_standard=state_standard,
            standard=standard_data,
            grade=grade,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            first_name=first_name,
            difficulty=difficulty,
            strengths=strengths,
            ls_explanations=ls_explanations,
            cs_explanations=cs_explanations,
            enriched_tasks=enriched_tasks
        )

        tools = [
            {
                "type": "function",
                "function": {
                    "name": "create_reading_comprehension",
                    "description": "Creates a series of reading comprehension activities for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "homework": {
                                "type": "object",
                                "properties": {
                                    "title": {"type": "string", "description": "The title of the homework assignment"},
                                    "introduction": {"type": "string", "description": "An introduction to the reading assignment"},
                                    #"passage": { "type": "array", "items": { "type": "string", "description": "Paragraphs of the generated reading passage"}},
                                    "challenges": {
                                        "type": "array",
                                        "items": {
                                            "type": "object",
                                            "properties": {
                                                "index": {"type": "string", "description": "The question number"},
                                                "task": {"type": "string", "description": "The reading comprehension question"},
                                                #"description": {"type": "string", "description": "Explanation or additional detail about the question"},
                                                # "task_skills": {"type": "string", "description": "Skills measured by the task ('Critical Thinking', etc.)"},
                                                "skill_number": {"type": "string", "description": "The skill number"}
                                            },
                                            "required": ["index", "task", "skill_number"]
                                        },
                                        "description": "A list of reading comprehension questions related to the passage"
                                    },
                                    "conclusion": {"type": "string", "description": "Conclusion or wrap-up of the assignment"},
                                    "reflection": {"type": "string", "description": "Reflection question about the reading material"},
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the reading comprehension assignment",
                                        "items": {"type": "string"}
                                    },
                                    "estimatedCompletionTime": {"type": "string", "description": "Estimated time to complete the assignment"}
                                },
                                "required": ["title", "introduction", "conclusion", "reflection", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    elif assignment_type == 'vocab_fill':
        words=vocab_examples(grade)
        augmentation_prompt = langfuse.get_prompt('vocab_fill_questions_generation', type='chat', label="production")
        
        complete_chat_prompt = augmentation_prompt.compile(
            nb_of_questions=nb_of_questions,
            words=words,
            passage=ai_passage,
            state_standard=state_standard,
            standard=standard_data,
            grade=grade,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            first_name=first_name,
            difficulty=difficulty,
            strengths=strengths,
            cs_explanations=cs_explanations,
            enriched_tasks=enriched_tasks
        )

        tools = [
                {
                    "type": "function",
                    "function": {
                        "name": "generate_vocabulary_questions",
                        "description": "Generates vocabulary-focused questions for a reading comprehension activity",
                        "parameters": {
                            "type": "object",
                            "properties": {
                                "homework": {
                                    "type": "object",
                                    "properties": {
                                        "title": {
                                            "type": "string",
                                            "description": "The title of the vocabulary activity"
                                        },
                                        # "passage": {
                                        #         "type": "array",
                                        #         "items": {
                                        #             "type": "string",
                                        #             "description": "Paragraphs of the generated reading passage"
                                        #         }
                                        #     },
                                        "challenges": {
                                            "type": "array",
                                            "description": "A set of vocabulary-focused questions based on specific parts of the text",
                                            "items": {
                                                "type": "object",
                                                "properties": {
                                                    "index": {
                                                        "type": "string",
                                                        "description": "The question number"
                                                    },
                                                    "task": {
                                                        "type": "string",
                                                        "description": "The vocabulary question prompt"
                                                    },
                                                    # "description": {
                                                    #     "type": "string",
                                                    #     "description": "Specific paragraph or sentence context for the question"
                                                    # },
                                                   "skill_number": {"type": "integer", "description": "The skill number"}
                                                },
                                                "required": ["index", "task", "context", "skill_number"]
                                            }
                                        },
                                        "instructions": {
                                            "type": "array",
                                            "description": "Instructions for completing the vocabulary activity",
                                            "items": {
                                                "type": "string"
                                            }
                                        },
                                        "estimatedCompletionTime": {
                                            "type": "string",
                                            "description": "The estimated time to complete the vocabulary activity"
                                        }
                                    },
                                    "required": ["title", "challenges", "instructions", "estimatedCompletionTime"]
                                }
                            }
                        }
                    }
                }
            ]
    elif assignment_type == 'arg_essay':
        augmentation_prompt = langfuse.get_prompt('arg_essay', type='chat', label="latest")
        
        complete_chat_prompt = augmentation_prompt.compile(
            first_name=first_name,
            grade=grade,
            state_standard=state_standard,
            standard=standard_data,
            interests=interest,
            strengths=strengths,
            passage=passage,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            cs_explanations=cs_explanations
        )

        tools = [
                {
                "type": "function",
                "function": {
                    "name": "generate_essay_prompt",
                    "description": "Generates an informational essay prompt for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "essay": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the essay prompt"
                                    },
                                    "prompt": {
                                        "type": "string",
                                        "description": "The informational essay prompt"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the essay",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the essay"
                                    }
                                },
                                "required": ["title","passage", "prompt", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    elif assignment_type == 'inf_essay':
        augmentation_prompt = langfuse.get_prompt('inf_essay', type='chat', label="latest")
        
        complete_chat_prompt = augmentation_prompt.compile(
            first_name=first_name,
            grade=grade,
            state_standard=state_standard,
            standard=standard_data,
            interests=interest,
            strengths=strengths,
            passage=passage,
            working_style=working_style,
            working_style_data=working_style_data,
            reading_level=reading_level,
            reading_level_example=example_reading_level,
            cs_explanations=cs_explanations
        )

        tools = [
                {
                "type": "function",
                "function": {
                    "name": "generate_essay_prompt",
                    "description": "Generates an informational essay prompt for students",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "essay": {
                                "type": "object",
                                "properties": {
                                    "title": {
                                        "type": "string",
                                        "description": "The title of the essay prompt"
                                    },
                                    "prompt": {
                                        "type": "string",
                                        "description": "The informational essay prompt"
                                    },
                                    "instructions": {
                                        "type": "array",
                                        "description": "Instructions for completing the essay",
                                        "items": {
                                            "type": "string"
                                        }
                                    },
                                    "estimatedCompletionTime": {
                                        "type": "string",
                                        "description": "The estimated time to complete the essay"
                                    }
                                },
                                "required": ["title","passage", "prompt", "instructions", "estimatedCompletionTime"]
                            }
                        }
                    }
                }
            }
        ]
    model=augmentation_prompt.config.get("openai", {}).get("model","gpt-4.1")
    temperature=augmentation_prompt.config.get("openai", {}).get("temperature",0.5)
    max_tokens=augmentation_prompt.config.get("openai", {}).get("max_tokens",4096)
    temp_tools = copy.deepcopy(tools)
    temp_tools[0]['function']['parameters']['properties']['homework']['properties'].pop('challenges', None)
    assignment = make_openai_langfuse(messages=complete_chat_prompt, tools=temp_tools, model=model, temperature=temperature, max_tokens=max_tokens, nb_of_questions=nb_of_questions)

    return assignment,tools
