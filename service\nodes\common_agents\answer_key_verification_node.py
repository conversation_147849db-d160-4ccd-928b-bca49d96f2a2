from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.answer_key_verification_agent import answer_key_verification
from service.context_pipeline import logger

def answer_key_verification_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles verification and correction of answer keys.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting answer_key_verification_node")

    # Extract required inputs from state
    json_response = state.assignment
    tools = state.tools
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type
    if state.ai_passage:
        passage=state.ai_passage
        if 'passage' in passage:
            passage=passage['passage']
    else:
        passage=""
    # Call the core agent functionality
    try:
        content = answer_key_verification(
            json_response=json_response,
            assignment_type=assignment_type,
            nb=nb,
            tools=tools,
            passage=passage
        )
        logger.info("Completed answer_key_verification_node successfully")
    except Exception as e:
        logger.error(f"Error in answer_key_verification_node: {e}")
        content = json_response  # Return original content on error

    # Return updated state
    return {"assignment": content}