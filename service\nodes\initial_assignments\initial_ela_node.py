from service.gen_data_models import GeneratePipelineContext
from service.context_pipeline import logger
from service.initial_assignments.initial_ela_assignments import generate_initial_ela_passage, generate_initial_ela_assignment

def generate_initial_ela_passage_node(state: GeneratePipelineContext):
    """Extracts inputs from state and passes them to the generator function."""
    logger.info("---Initial Assignment---")

    grade = state.student.grade
    first_name = state.student.first_name
    working_style = state.student.working_style
    reading_level = state.student.reading_level
    life_skills = state.request.life_skills
    passage_style = state.request.passage_style
    interest = state.student.interest
    state_standard = state.request.state_standard
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    add_info = state.request.add_info
    difficulty = state.request.difficulty
    strengths=state.student.strengths

    try:
        state.assignment_state='processing_passage'
        logger.info("State set to processing_passage.")
        
        passage,tools = generate_initial_ela_passage(
            grade=grade,
            first_name=first_name,
            working_style=working_style,
            reading_level=reading_level,
            life_skills=life_skills,
            passage_style=passage_style,
            interest=interest,
            state_standard=state_standard,
            standard=standard,
            assignment_type=assignment_type,
            add_info=add_info,
            difficulty=difficulty,
            strengths=strengths,
        )

        return {"ai_passage": passage, "tools": tools,'assignment_state': 'processing_passage','attempts': 0}
    
    except Exception as e:
        logger.error(f"Error in reading comp gen intial_passage_node: {e}")
        return {"error": "Failed to generate initial passage."}
    
def generate_initial_ela_assignment_node(state: GeneratePipelineContext):
    """Extracts inputs from state and passes them to the generator function."""
    logger.info("---Initial Assignment---")
    logger.info("Extract all inputs needed by this function")

    grade = state.student.grade
    first_name = state.student.first_name
    working_style = state.student.working_style
    reading_level = state.student.reading_level
    life_skills = state.request.life_skills
    strengths = state.student.strengths
    state_standard = state.request.state_standard
    standard = state.request.standard
    assignment_type = state.request.assignment_type
    nb_of_questions = state.request.nb_of_questions
    difficulty = state.request.difficulty
    ai_passage = state.ai_passage 
    passage=state.request.passage
    interest=state.student.interest
    enriched_tasks=state.enriched_tasks
    logger.info("Students data extracted")

    try:
        state.assignment_state='processing_assignment'
        logger.info("State set to processing_assignment.")

        output, tools = generate_initial_ela_assignment(
            grade=grade,
            first_name=first_name,
            working_style=working_style,
            reading_level=reading_level,
            life_skills=life_skills,
            strengths=strengths,
            state_standard=state_standard,
            standard=standard,
            assignment_type=assignment_type,
            nb_of_questions=nb_of_questions,
            difficulty=difficulty,
            ai_passage=ai_passage,
            interest=interest,
            passage=passage,
            enriched_tasks=enriched_tasks
        )

        logger.info(f"Initial assignment generated: {output}") 
        if 'homework' in output:
            output['homework']['challenges']=state.challenges
        else:
            output['challenges']=state.challenges
        return {"assignment": output, "tools": tools,'assignment_state': 'processing_assignment','attempts': 0}

    except Exception as e:
        logger.error("Error in reading comp gen extract_assignment_inputs")
        return {"error": "Failed to generate initial assignment."}
