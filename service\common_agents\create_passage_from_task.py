from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# def create_passage_from_tasks_agent(substandard: list, grade: str,assignment_type: str, main_topic:str='' ) -> dict:
#     """
#     Generates a passage based on the provided tasks.

#     Behaviour:
#     for each task, call the model to generate a passage

#     Args:
#         substandard (list | str): List of sub-standard objects.
#         grade (str): Grade level.
#         assignment_type (str): Type of assignment.
#         main_topic (str): Main topic of the assignment.

#     Returns:
#         dict: Generated passage.

#     """
#     grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
#     additional_instruction = ""

#     if 'history' in assignment_type:
#         if main_topic:
#             additional_instruction += f"The main topic of the assignment is {main_topic}."

#     if isinstance(extract_skills_output, str):
#         try:
#             extract_skills_output = json.loads(extract_skills_output)
#         except Exception as e:
#             logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
#             extract_skills_output = []


#     # Prepare prompt template & OpenAI config once
#     messages = langfuse.get_prompt("create_passage_from_task", type="chat", label="latest")
#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "generate_passages",
#                 "description": (
#                     "Produces an ordered list of passage objects, each containing "
#                     "a skill_number for mapping, and a factual, historically accurate passage related to a given history assignment task."
#                 ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "passages": {
#                             "type": "array",
#                             "description": (
#                                 "A list of passage objects. Each object must include a "
#                                 "'skill_number' field for mapping to the assignment, and a 'passage' field. The passage must be grade-appropriate, "
#                                 "relevant to the assignment, and contain only verifiable historical facts."
#                             ),
#                             "items": {
#                                 "type": "object",
#                                 "properties": {
#                                     "skill_number": {
#                                         "type": "string",
#                                         "description": (
#                                             "The skill number that this passage supports or aligns with. Used for mapping to the related task."
#                                         )
#                                     },
#                                     "passage": {
#                                         "type": "string",
#                                         "description": (
#                                             "A passage that is strictly factual, historically accurate, and suitable for the student's grade. "
#                                             "It should directly address the assignment topic and substandard related to the skill, containing no invented or speculative information. "
#                                             "Provide only verifiable historical facts relevant to the input."
#                                         )
#                                     }
#                                 },
#                                 "required": ["skill_number", "passage"]
#                             }
#                         }
#                     },
#                     "required": ["passages"]
#                 }
#             }
#         }
#     ]
#     temperature = messages.config.get("openai", {}).get("temperature", 0.5)
#     model = messages.config.get("openai", {}).get("model", "gpt-4.1")
#     max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
#     complete_chat_prompt = messages.compile(
#         substandard=substandard,
#         grade_info=grade_info,
#         additional_instruction=additional_instruction
#     )
#     # Call the model
#     result = make_openai_langfuse(
#         complete_chat_prompt,
#         model=model,
#         temperature=temperature,
#         max_tokens=max_tokens,
#             tools=tools
#     )

        
#     return result
# def create_passage_from_tasks_agent(
#     substandard: list,
#     grade: str,
#     assignment_type: str,
#     main_topic: str = "",
#     interest_information: str = ""
# ) -> list:
#     """
#     For every dict in `substandard` (now containing 'skill_number' and
#     'enriched_task'), generate a historically accurate passage and add it
#     back into the same dict under key 'passage'.

#     Returns
#     -------
#     list
#         The original list with an added 'passage' for every element.
#     """

#     # ----------------------------- #
#     # 1. Static prompt fragments
#     # ----------------------------- #
#     grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
#     extra = (
#         f"The main topic of the assignment is {main_topic}."
#         if "history" in assignment_type.lower() and main_topic
#         else ""
#     )

#     # ----------------------------- #
#     # 2. Template + tool definition
#     # ----------------------------- #
#     messages = langfuse.get_prompt(
#         "create_passage_from_task", type="chat", label="latest"
#     )

#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "generate_passage",
#                 "description": (
#                     "Return a single, historically accurate, grade-appropriate "
#                     "passage that responds to the provided enriched task."
#                 ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "passage": {
#                             "type": "string",
#                             "description": "The generated passage."
#                         }
#                     },
#                     "required": ["passage"],
#                 },
#             },
#         }
#     ]
#     logger.info(f"substandard in create_passage_from_tasks_agent: {substandard}")
#     cfg         = messages.config.get("openai", {})
#     temperature = cfg.get("temperature", 0.5)
#     model       = cfg.get("model", "gpt-4o")
#     max_tokens  = cfg.get("max_tokens", 1024)

#     # ----------------------------- #
#     # 3. Helper – model call
#     # ----------------------------- #
#     def _fetch_passage(task_dict: dict) -> str:
#         prompt = messages.compile(
#             substandard=[task_dict],      # only this task
#             grade_info=grade_info,
#             additional_instruction=extra,
#             interest_information=interest_information
#         )

#         resp = make_openai_langfuse(
#             prompt,
#             model=model,
#             temperature=temperature,
#             max_tokens=max_tokens,
#             tools=tools,
#         )
#         return resp.get("passage", "")


#     # ----------------------------- #
#     # 4. Parallel execution
#     # ----------------------------- #
#     max_workers = max(1, min(8, len(substandard)))
#     with ThreadPoolExecutor(max_workers=max_workers) as exe:
#         futures = {
#             exe.submit(_fetch_passage, item): idx
#             for idx, item in enumerate(substandard)
#         }

#         for fut in as_completed(futures):
#             idx = futures[fut]
#             passage = fut.result()
#             substandard[idx]["passage"] = passage
#     logger.info(f"create_passage_from_tasks_agent response: {substandard}")
#     return substandard
from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
from concurrent.futures import ThreadPoolExecutor, as_completed

# def create_passage_from_tasks_agent(substandard: list, grade: str,assignment_type: str, main_topic:str='' ) -> dict:
#     """
#     Generates a passage based on the provided tasks.

#     Behaviour:
#     for each task, call the model to generate a passage

#     Args:
#         substandard (list | str): List of sub-standard objects.
#         grade (str): Grade level.
#         assignment_type (str): Type of assignment.
#         main_topic (str): Main topic of the assignment.

#     Returns:
#         dict: Generated passage.

#     """
#     grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
#     additional_instruction = ""

#     if 'history' in assignment_type:
#         if main_topic:
#             additional_instruction += f"The main topic of the assignment is {main_topic}."

#     if isinstance(extract_skills_output, str):
#         try:
#             extract_skills_output = json.loads(extract_skills_output)
#         except Exception as e:
#             logger.error(f"Failed to convert extract_skills_output to dictionary: {e}")
#             extract_skills_output = []


#     # Prepare prompt template & OpenAI config once
#     messages = langfuse.get_prompt("create_passage_from_task", type="chat", label="latest")
#     tools = [
#         {
#             "type": "function",
#             "function": {
#                 "name": "generate_passages",
#                 "description": (
#                     "Produces an ordered list of passage objects, each containing "
#                     "a skill_number for mapping, and a factual, historically accurate passage related to a given history assignment task."
#                 ),
#                 "parameters": {
#                     "type": "object",
#                     "properties": {
#                         "passages": {
#                             "type": "array",
#                             "description": (
#                                 "A list of passage objects. Each object must include a "
#                                 "'skill_number' field for mapping to the assignment, and a 'passage' field. The passage must be grade-appropriate, "
#                                 "relevant to the assignment, and contain only verifiable historical facts."
#                             ),
#                             "items": {
#                                 "type": "object",
#                                 "properties": {
#                                     "skill_number": {
#                                         "type": "string",
#                                         "description": (
#                                             "The skill number that this passage supports or aligns with. Used for mapping to the related task."
#                                         )
#                                     },
#                                     "passage": {
#                                         "type": "string",
#                                         "description": (
#                                             "A passage that is strictly factual, historically accurate, and suitable for the student's grade. "
#                                             "It should directly address the assignment topic and substandard related to the skill, containing no invented or speculative information. "
#                                             "Provide only verifiable historical facts relevant to the input."
#                                         )
#                                     }
#                                 },
#                                 "required": ["skill_number", "passage"]
#                             }
#                         }
#                     },
#                     "required": ["passages"]
#                 }
#             }
#         }
#     ]
#     temperature = messages.config.get("openai", {}).get("temperature", 0.5)
#     model = messages.config.get("openai", {}).get("model", "gpt-4.1")
#     max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
#     complete_chat_prompt = messages.compile(
#         substandard=substandard,
#         grade_info=grade_info,
#         additional_instruction=additional_instruction
#     )
#     # Call the model
#     result = make_openai_langfuse(
#         complete_chat_prompt,
#         model=model,
#         temperature=temperature,
#         max_tokens=max_tokens,
#             tools=tools
#     )

        
#     return result
def create_passage_from_tasks_agent(
    substandard: list,
    grade: str,
    assignment_type: str,
    main_topic: str = "",
    interest_information: str = ""
) -> list:
    """
    For every dict in `substandard` (now containing 'skill_number' and
    'enriched_task'), generate a historically accurate passage and add it
    back into the same dict under key 'passage'.

    Returns
    -------
    list
        The original list with an added 'passage' for every element.
    """

    # ----------------------------- #
    # 1. Static prompt fragments
    # ----------------------------- #
    grade_info = f"for grade level: {grade}" if grade else "for the appropriate grade level"
    extra = (
        f"The main topic of the assignment is {main_topic}."
        if "history" in assignment_type.lower() and main_topic
        else ""
    )

    # ----------------------------- #
    # 2. Template + tool definition
    # ----------------------------- #
    messages = langfuse.get_prompt(
        "create_passage_from_task", type="chat", label="latest"
    )

    tools = [
        {
            "type": "function",
            "function": {
                "name": "generate_passage",
                "description": (
                    "Return a single, historically accurate, grade-appropriate "
                    "passage that responds to the provided enriched task."
                ),
                "parameters": {
                    "type": "object",
                    "properties": {
                        "passage": {
                            "type": "string",
                            "description": "The generated passage."
                        }
                    },
                    "required": ["passage"],
                },
            },
        }
    ]
    logger.info(f"substandard in create_passage_from_tasks_agent: {substandard}")
    cfg         = messages.config.get("openai", {})
    temperature = cfg.get("temperature", 0.5)
    model       = cfg.get("model", "gpt-4o")
    max_tokens  = cfg.get("max_tokens", 1024)

    # ----------------------------- #
    # 3. Helper – model call
    # ----------------------------- #
    def _fetch_passage(task_dict) -> str:
        task_data=task_dict['enriched_task']
        substandard=""
        prompt = messages.compile(
            substandard=substandard,
            assignment_task=[task_dict],      # only this task
            grade=grade_info,
            additional_instruction=extra,
            interest_information=interest_information
        )

        resp = make_openai_langfuse(
            prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            tools=tools,
        )
        return resp.get("passage", "")


    # ----------------------------- #
    # 4. Parallel execution
    # ----------------------------- #
    max_workers = max(1, min(8, len(substandard)))
    index=1
    with ThreadPoolExecutor(max_workers=max_workers) as exe:
        futures = {
            exe.submit(_fetch_passage, item): idx
            for idx, item in enumerate(substandard)
        }

        for fut in as_completed(futures):
            idx = futures[fut]
            passage = fut.result()
            substandard[idx]["passage"] = passage
            substandard[idx]['index'] = index
            index += 1
    logger.info(f"create_passage_from_tasks_agent response: {substandard}")
    return substandard