from service.gen_data_models import GeneratePipelineContext
from service.common_agents.enrich_with_interest_agent import enrich_with_interest_agent
from service.context_pipeline import logger
import json
import random
import ast
def enrich_with_interest_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"enrich_interest_node Starting node execution with state: {state}")

    grade=state.student.grade
    subject=state.request.standard.get('subject','')
    interest_data=state.interest_information

    adjustment_instructions = state.adjustment_instructions
    develop_tasks= adjustment_instructions['develop_tasks']
    assignment_type=state.request.assignment_type
    logger.info(f"enrich_interest_node Extracted parameters - Grade: {grade}, Subject: {subject}")
    logger.debug(f"enrich_interest_node Raw interest_data type: {type(interest_data)}, value: {interest_data}")

    try:
        if type(interest_data) == str:
            logger.debug(f"enrich_interest_node Converting string interest_data to dictionary")
            try:
                interest_data = json.loads(interest_data)
                logger.debug(f"enrich_interest_node Successfully converted interest_data using json.loads")
            except Exception as e:
                logger.warning(f"enrich_interest_node JSON parsing failed for interest_data, trying AST: {e}")
                try:
                    interest_data = ast.literal_eval(interest_data)
                    logger.debug(f"enrich_interest_node Successfully converted interest_data using ast.literal_eval")
                except Exception as e:
                    logger.error(f"enrich_interest_node AST parsing also failed for interest_data: {e}")
        if 'interest_information' in interest_data:
            logger.debug(f"enrich_interest_node Found 'interest_information' key in interest_data")
            interest_list = interest_data["interest_information"]
        else:
            logger.debug(f"enrich_interest_node Using interest_data directly as interest_list")
            interest_list = interest_data
        interest_cycle = iter(interest_list)
        logger.info(f"enrich_interest_node Successfully created interest cycle with {len(interest_list) if hasattr(interest_list, '__len__') else 'unknown'} items")
    except Exception as e:
        logger.error(f"enrich_interest_node Critical failure extracting interest_list from interest_data: {interest_data}, Error: {e}")
    logger.debug(f"enrich_interest_node Raw develop_tasks type: {type(develop_tasks)}, value: {develop_tasks}")

    
    if type(develop_tasks)==list:
        logger.info(f"enrich_interest_node Processing develop_tasks as dictionary with {len(develop_tasks)} items")
        try:
            enriched_data=[]
            substandards_data={}
            for data in develop_tasks:
                key=data.get("skill_number", "")
                logger.debug(f"enrich_interest_node Processing task key: {key}")
                skill = data.get("skill", "")
                task_info = {"task": data.get("task", "")}
                if "title" in data:
                    task_info["title"] = data["title"]
                if "scenario" in data:
                    task_info["scenario"] = data["scenario"]
                logger.info(f"enrich_interest_node Extracted skill: '{skill}', task: '{task_info}'")

                try:
                    interest = next(interest_cycle)
                    logger.debug(f"enrich_interest_node Selected interest: '{interest}' for task key: {key}")
                except StopIteration:
                    logger.info(f"enrich_interest_node Interest cycle exhausted, reshuffling and restarting")
                    random.shuffle(interest_list)
                    interest_cycle = iter(interest_list)
                    interest = next(interest_cycle)
                    logger.debug(f"enrich_interest_node Selected interest after reshuffle: '{interest}' for task key: {key}")

                # Call the agent to infuse the given interest into the task
                logger.debug(f"enrich_interest_node Calling enrich_with_interest_agent for task key: {key}")
                enrichment = enrich_with_interest_agent(grade=grade, subject=subject, skill=skill, task=task_info, interesting_information=interest,assignment_type=assignment_type)
                logger.debug(f"enrich_interest_node Received enrichment response for task key: {key}")

                enriched_data.append({"skill_number": key, "enriched_task": enrichment})
                substandards_data[key] = {
                    **data,
                    "interest": interest,
                    "enriched_task": enrichment
                }
            logger.info(f"enrich_interest_node Successfully enriched {len(enriched_data)} tasks with interests")
        except Exception as e:
            logger.error(f"enrich_interest_node Failed to enrich dictionary tasks with interest: {develop_tasks}, Error: {e}")
            raise
    else:
        logger.info(f"enrich_interest_node Processing develop_tasks as non-dictionary type: {type(develop_tasks)}")
        try:
            logger.debug(f"enrich_interest_node Calling enrich_with_interest_agent with non-dictionary develop_tasks")
            enriched_data = enrich_with_interest_agent(
                grade=grade,
                subject=subject,
                task=develop_tasks,
                skill='',
                interesting_information=interest_data,
                assignment_type=assignment_type,
            )

            logger.info(f"enrich_interest_node Successfully received enrich_with_interest response: {enriched_data}")
            #adjustment_instructions['enrich_with_interest'] = response
        except Exception as e:
            logger.error(f"enrich_interest_node Error processing non-dictionary develop_tasks: {e}")
            raise
    logger.info(f"enrich_interest_node Node execution completed successfully:{enriched_data}")
    #return "substandards_data": substandards_data
    return {"enriched_tasks": enriched_data}