from service.gen_data_models import GeneratePipelineContext
from service.image_generation.image_generation import generate_images_for_assignment_parallel
from service.context_pipeline import logger
import time

def image_generation_node(state: GeneratePipelineContext):
    """
    Node that handles generating images for assignments.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    start_time = time.time()  # Record start time

    logger.info(f"Starting image_generation_node:{state}")

    # Extract required inputs from state
    assignment = state.assignment
    assignment_id = state.request_id
    standard = state.request.standard
    grade = state.student.grade
    processed_standard=state.processed_standard
    if 'skills' in processed_standard:
        processed_standard=processed_standard['skills']
    try:
        results = generate_images_for_assignment_parallel(
            assignment=assignment,
            assignment_id=assignment_id,
            standard=standard,
            grade=grade,
            processed_standard=processed_standard
        )
        logger.info("Completed image_generation_node successfully")
        end_time = time.time()  # Record end time
        logger.info(f"Time taken: {end_time - start_time} seconds")
        return {"assignment": results}
    except Exception as e:
        logger.error(f"Error in image_generation_node: {e}")
        return {"error": "Failed to generate images."}
