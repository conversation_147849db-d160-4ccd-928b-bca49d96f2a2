# import unittest
# from unittest.mock import patch, MagicMock

# # Patch BEFORE importing the decorated function
# cache_patch = patch('service.common_agents.dyslexia_agent.aisdk_object.cache_result', lambda *a, **kw: lambda f: f)
# cache_patch.start()

# from service.common_agents.dyslexia_agent import dyslexia_agent

# class TestDyslexiaAgent(unittest.TestCase):

#     @patch('service.common_agents.dyslexia_agent.make_openai_langfuse')
#     @patch('service.common_agents.dyslexia_agent.langfuse.get_prompt')
#     def test_dyslexia_agent_success(self, mock_get_prompt, mock_make_openai_langfuse):
#         mock_prompt = MagicMock()
#         mock_prompt.compile.return_value = "compiled_prompt"
#         mock_prompt.config = {
#             "openai": {"temperature": 0.3, "model": "gpt-4.1", "max_tokens": 2048}
#         }
#         mock_get_prompt.return_value = mock_prompt

#         mock_make_openai_langfuse.return_value = {
#             "supports_accessibility": True,
#             "reasoning": "Clear layout.",
#             "next_steps": "Use short, simple sentences."
#         }

#         result = dyslexia_agent(
#             initial_assignment={"title": "Math", "instructions": "Solve the equations."},
#             nb_of_questions=3,
#             assignment_type="math"
#         )

#         assert result["supports_accessibility"] is True

#     @patch('service.common_agents.dyslexia_agent.make_openai_langfuse')
#     @patch('service.common_agents.dyslexia_agent.langfuse.get_prompt')
#     def test_dyslexia_agent_exception(self, mock_get_prompt, mock_make_openai_langfuse):
#         mock_get_prompt.side_effect = Exception("Simulated langfuse failure")

#         input_assignment = {"title": "Biology", "instructions": "Describe photosynthesis"}
#         result = dyslexia_agent(input_assignment, nb_of_questions=2, assignment_type="science")

#         # Should fall back to returning original input
#         assert result == input_assignment

# if __name__ == '__main__':
#     unittest.main()

# # Stop patch after tests
# cache_patch.stop()
