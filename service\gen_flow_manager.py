from service.context_pipeline import logger, aisdk_object
from service.graphs.ela.aipassage_langgraph import ela_passage_langgraph
from service.graphs.ela.essay_langgraph import ela_essay_langgraph
from service.graphs.maths.general_langgraph import new_math_langgraph
from service.graphs.history.general_langgraph import history_general_langgraph
from service.graphs.history.history_passage_langgraph import history_passage_langgraph

def route_assignment(ptx,langfuse_handler):
    """
    Selects and calls the appropriate LangGraph function based on assignment_type.

    Args:
        assignment_type: The type of assignment (must be a key in LANGGRAPH_FUNCTION_MAP).
        **kwargs: Keyword arguments to pass directly to the target LangGraph function
                  (e.g., user_input="...", context=None, topic="WWII").

    Returns:
        The result returned by the executed LangGraph function.

    Raises:
        ValueError: If the provided assignment_type is not found in the map.
        TypeError: If the mapped item isn't a callable function.
        Exception: Re-raises any exception originating from the called LangGraph function.
    """
    logger.info(f"Router received type: {ptx}")
    assignment_type=ptx.request.assignment_type
    LANGGRAPH_FUNCTION_MAP = {
    'math_worked_example': new_math_langgraph,
    'math_fact': new_math_langgraph,
    'math_home_dynamic': new_math_langgraph,
    'math_partner': new_math_langgraph,
    'math_word_problem': new_math_langgraph,
    'math_story': new_math_langgraph,
    'reading_comp_gen': ela_passage_langgraph,
    'vocab_fill': ela_passage_langgraph,
    'arg_essay': ela_essay_langgraph,
    'inf_essay': ela_essay_langgraph,
    'history_critical': history_general_langgraph,
    'history_fact': history_passage_langgraph,
    'history_vocab': history_general_langgraph,
    'history_essay': history_general_langgraph,
}
    logger.info(f"Router received type: {assignment_type}")

    # Retrieve the target function object from the map using the assignment_type string
    target_function = LANGGRAPH_FUNCTION_MAP.get(assignment_type)
    try:
        result = target_function(ptx,langfuse_handler)
        logger.info(f"Router finished processing type: {assignment_type}")
        return {'data': result} 
    except Exception as e:
        logger.error(f"Error during execution of function for {assignment_type}")
        logger.error(f"Function: {getattr(target_function, '__name__', 'N/A')}")
        logger.error(f"Error details: {e}")
        raise
