from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.generate_rubrics_hints import generate_rubrics_hints_agent
from service.context_pipeline import logger

def rubric_hints_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node for generating rubric hints for assignments.

    Takes the current state and processes the assignment to add rubric hints.
    """
    logger.info(f"Starting rubric_hints_node:{state}")

    try:
        updated_assignment = generate_rubrics_hints_agent(
            assignment=state.assignment,
            grade=state.student.grade,
            assignment_type=state.request.assignment_type,
            nb = state.request.nb_of_questions
        )
        logger.info("Completed rubric_hints_node successfully")
    except Exception as e:
        logger.error(f"Error in rubric_hints_node: {e}")
        updated_assignment = state.assignment 

    return {"assignment": updated_assignment}