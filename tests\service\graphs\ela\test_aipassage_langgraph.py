import pytest
from unittest.mock import patch, MagicMock
from service.graphs.ela.aipassage_langgraph import ela_passage_langgraph, check_if_kindergarten

# --- Mock context data structures ---
class MockStudent:
    def __init__(self, grade="3", is_dyslexic=False):
        self.grade = grade
        self.is_dyslexic = is_dyslexic

class MockRequest:
    def __init__(self, add_info=False):
        self.add_info = add_info

class MockGeneratePipelineContext:
    def __init__(self, grade="3", is_dyslexic=False, add_info=False):
        self.student = MockStudent(grade, is_dyslexic)
        self.request = MockRequest(add_info)

# --- Success test ---
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.compile")
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.add_node")
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.add_edge")
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.set_entry_point")
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.add_conditional_edges")
def test_ela_passage_langgraph_success(
    mock_conditional_edges,
    mock_entry,
    mock_edge,
    mock_node,
    mock_compile
):
    # Simulate compiled graph chain
    mock_chain = MagicMock()
    mock_compile.return_value = mock_chain
    mock_chain.invoke.return_value = {
        "assignment": {
            "passage": ["Line 1", "Line 2"],
            "questions": ["Q1", "Q2"]
        },
        "ai_passage": "Sample passage"
    }

    ctx = MockGeneratePipelineContext()
    result = ela_passage_langgraph(ctx, langfuse_handler=MagicMock())

    assert "homework" in result
    assert isinstance(result["homework"], dict)
    assert "passage" in result["homework"]
    assert result["homework"]["passage"].startswith("Line 1")

# --- Failure test ---
@patch("service.graphs.ela.aipassage_langgraph.StateGraph.compile", side_effect=Exception("Graph compile error"))
def test_ela_passage_langgraph_failure(mock_compile):
    ctx = MockGeneratePipelineContext()
    with pytest.raises(Exception) as exc_info:
        ela_passage_langgraph(ctx, langfuse_handler=MagicMock())
    assert "Graph compile error" in str(exc_info.value)

# --- check_if_kindergarten tests ---
def test_check_if_kindergarten_yes():
    ctx = MockGeneratePipelineContext(grade="kindergarten")
    assert check_if_kindergarten(ctx) == "Yes"

def test_check_if_kindergarten_no():
    ctx = MockGeneratePipelineContext(grade="5")
    assert check_if_kindergarten(ctx) == "No"
