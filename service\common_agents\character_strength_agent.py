from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger
from service.context_pipeline import langfuse

def assess_strengths(initial_assignment: dict, character_strengths: str, nb_of_questions: int, assignment_type:str) -> dict:
    """
    Core agent functionality for assessing whether an assignment incorporates character strengths.

    Evaluates if a homework assignment effectively incorporates the specified character
    strengths of the student.

    Args:
        json_response: The JSON object containing the assignment data
        character_strengths: A comma-separated list of character strengths to assess

    Returns:
        dict: Assessment indicating whether the assignment integrates the character strengths
    """
    logger.info(f"Starting assess_strengths with character strengths: {character_strengths}")
    try:
        logger.debug(f"Parsing character strengths: {character_strengths}")
        if isinstance(character_strengths, str):
            strengths_list = [strength.strip() for strength in character_strengths.split(',')]
        else:
            strengths_list = character_strengths
        character_strengths = character_strength_details(strengths_list)
        logger.debug(f"Retrieved details for {len(strengths_list)} character strengths")

        logger.debug("Creating system and user messages for character strength assessment")
        messages = langfuse.get_prompt("assess_strengths", label="latest")
        complete_chat_prompt = messages.compile(
            character_strengths=character_strengths,
            initial_assignment=initial_assignment
        )

        logger.debug("Setting up tools for character strength assessment")
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "assess_assignment_character_strengths",
                    "description": "Assess whether a homework assignment meaningfully integrates the student's specified character strengths in a way that enhances learning.",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "supports_strengths": {
                                "type": "boolean",
                                "description": "Indicates whether the assignment meaningfully reflects the student's character strengths."
                            },
                            "reasoning": {
                                "type": "string",
                                "description": "Clear and concise explanation of the determination regarding strengths integration."
                            },
                            "next_steps": {
                                "type": "string",
                                "description": "Detailed explanation of why it supports strengths or what actions should be taken to maintain or better incorporate the strengths."
                            }
                        },
                        "required": ["supports_strengths", "reasoning", "next_steps"]
                    }
                }
            }
        ]

        logger.debug("Making OpenAI request for character strength assessment")
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        result = make_openai_langfuse(messages=complete_chat_prompt, tools=tools, model=model, temperature=temperature, max_tokens=max_tokens)
        logger.info("Successfully completed character strength assessment")
        return result
    
    except Exception as e:
        logger.error(f"Error in assess_strengths: {e}")
        return {"supports_strengths": True}

def character_strength_details(character_strengths):
    """
    This function retrieves detailed descriptions of specified character strengths from a predefined set of strengths
    and their corresponding descriptions. It matches the input strengths with categories and provides relevant details
    about each strength.

    Parameters:
    - character_strengths (list of str): A list of character strength names to be assessed and matched with predefined
      descriptions. The matching is case-insensitive.

    Returns:
    - dict: A dictionary where the keys are the input character strengths and the values are their respective descriptions
      from a predefined set. If a strength is not found, the value will be an empty string.
    """
    logger.debug(f"Getting details for character strengths: {character_strengths}")

    characters={
    ("creativity", "originality", "ingenuity", "innovation"): "This is the process of using one's originality to devise novel ways to positively contribute to one's own life or the lives of others. In a classroom, a creative student might be asked to solve a problem in an unusual way or connect the concept to something personal. Example: A student uses recycled materials to create an art project that also raises awareness about environmental sustainability.",

    ("curiosity", "interest", "novelty-seeking", "openness to experience"): "This trait involves actively recognizing and pursuing new ideas and seeking out new knowledge. A curious student might explore a fun fact related to a math problem or be given a new idea to investigate. Example: In science class, a curious student conducts extra experiments beyond the curriculum to satisfy their thirst for understanding how things work.",

    ("judgment", "open-mindedness", "critical thinking"): "This involves thinking things through and examining them from all sides, willing to consider evidence against one's own beliefs. Students can use the answers to questions to decide what the logical or rational choice is. Example: When presented with a controversial topic, a student might research various perspectives to write a well-rounded essay.",

    ("love of learning"): "This involves enthusiastically studying new skills, topics, and bodies of knowledge. Students could be asked if they feel like they learned something new and to reflect on their understanding. Example: A student spends extra time learning a new language, driven by the joy of mastering it rather than just the need to pass a test.",

    ("perspective", "wisdom"): "This involves a superior level of knowledge and judgment. Students could be tasked to think of a problem in a friend’s life that could be solved using the concept. Example: A senior student might give advice to younger peers on how to balance school work and extracurricular activities effectively.",

    ("bravery", "valor"): "This is the capacity to take action in spite of risks. Brave students might be assigned the most challenging problem to tackle first. Example: A student stands up against bullying in school, despite the risk of being ostracized.",

    ("perseverance", "persistence", "industriousness"): "It is the mental strength necessary to continue striving for one's goals in the face of obstacles. Students could be given a goal or asked to set one for their assignments. Example: Despite several failures, a student continues working on their science project until they achieve success.",

    ("honesty", "integrity", "authenticity"): "This strength is shown by being genuine and honest. Students may be asked about their thoughts on the concept and if they are committed to learning it. Example: A student honestly admits forgetting their homework, maintaining trust with their teachers.",

    ("zest", "vitality", "enthusiasm", "vigor", "energy"): "It is an approach to life marked by an appreciation for excitement and liveliness. Students might be given assignments that match their energy level. Example: A student enthusiastically participates in every school event, adding energy and excitement to the activities.",

    ("love", "Romantic", "familial", "platonic"): "Loving individuals value close relationships. Assignments could involve solving problems for loved ones or emphasizing teamwork. Example: A student organizes a surprise party to show appreciation for their friends' support during tough times.",

    ("kindness", "generosity", "nurturance", "care", "compassion", "altruistic love", "nice-ness"): "This consists of doing favors and good deeds for others. Students might explain a math concept to a classmate or solve real-world problems that involve helping others. Example: A student helps their classmates understand a difficult topic, tutoring them after class.",

    ("social intelligence", "emotional intelligence", "personal intelligence"): "Individuals are aware of the emotions and intentions of themselves and others. Students might organize a group or solve a problem that requires understanding human behavior. Example: During group projects, a student ensures that all voices are heard and integrates shy classmates into the discussion.",

    ("teamwork", "citizenship", "social responsibility", "loyalty"): "It involves working as a member of a group for the common good. Students could be given group problems where appropriate. Example: A student volunteers to stay late to help clean up after a school event, showing dedication to the school community.",

    ("fairness", "equity", "justice"): "This involves treating everyone according to ideals of equality and justice. Students might solve a problem and use the answer to make a fair decision. Example: A teacher organizes a debate where each student gets an equal opportunity to express their opinions.",

    ("leadership"): "Leadership involves motivating and coordinating members of a group to achieve a common goal. Students could be tasked with setting a goal for each assignment. Example: A student leads a fundraiser, organizing tasks efficiently while ensuring every team member's opinion is considered.",

    ("forgiveness", "mercy"): "This strength involves forgiving those who have wronged us. Students might analyze math problems where mistakes were made and discuss how to correct them. Example: After a disagreement, a student decides to forgive their friend and works to rebuild the trust in their friendship.",

    ("humility", "modesty"): "Humility involves letting one's accomplishments speak for themselves. Students might reflect on their learning process after solving a problem. Example: A top student quietly helps others achieve their academic goals without boasting about their own grades.",

    ("prudence"): "Prudence is being careful about one's choices. Students should show all work and consider the long-term consequences of their decisions. Example: A student saves a portion of their allowance each week to buy a much-wanted book later.",

    ("self-regulation", "self-control"): "It is the process of exerting control over oneself. Students might set a schedule to balance study and play. Example: A student sets a schedule to balance study and play, sticking to it to prepare for upcoming exams.",

    ("appreciation of beauty and excellence", "awe", "wonder", "elevation"): "Individuals feel a sense of awe at the beauty around them. Students may be given problems whose context is a beautiful scene. Example: During a field trip, a student is captivated by the natural beauty of a landscape and inspires classmates to appreciate the scene.",

    ("gratitude"): "Gratitude is being thankful for the good things in one's life. Students might be prompted to be grateful for someone who helped them learn a concept. Example: A student writes thank-you notes to teachers at the end of the school year and acknowledges their peers' assistance regularly.",

    ("hope", "optimism", "future-mindedness", "future orientation"): "It is the expectation that good things will happen in the future. Students might set high expectations for themselves. Example: Even after a project fails, a student remains hopeful and motivated, believing that their next attempt will be successful.",

    ("humor", "playfulness"): "This involves an enjoyment of bringing happiness to others. Students might be given assignments that are cheerful and playful. Example: A student uses their sense of humor to keep spirits high during exam preparations by sharing light-hearted jokes.",

    ("spirituality", "religiousness", "faith", "purpose"): "Spirituality involves a knowledge of one's place within the larger scheme of things. Students could reflect on how solving an assignment made them feel. Example: A student reflects on their personal values and the larger purpose of their actions, guiding them in making meaningful life choices."
    }
    try:
        strength_details = {}
        for strength in character_strengths:
            for key, description in characters.items():
                if strength.lower() in key:
                    strength_details[strength] = description
                    logger.debug(f"Found description for strength: {strength}")
                    break
            else:
                strength_details[strength]=''
                logger.warning(f"No description found for strength: {strength}")

        return strength_details
    except Exception as e:
        logger.error(f"Error in character_strength_details: {e}")
        return {}