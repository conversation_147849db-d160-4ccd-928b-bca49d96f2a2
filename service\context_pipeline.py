from aisdk import Aisdk
import logging
from langfuse import Langfuse
import os

aisdk_object=Aisdk('generate','staging')
logger= logging.getLogger()
config_data=aisdk_object.config.config_data
logging.info(f"config data in gen {config_data}")
langfuse_public_key=config_data['LANGFUSE_PUBLIC_KEY']
langfuse_secret_key=config_data['LANGFUSE_SECRET_KEY']
os.environ["LANGFUSE_PUBLIC_KEY"] = langfuse_public_key
os.environ["LANGFUSE_SECRET_KEY"] = langfuse_secret_key

langfuse = Langfuse(
  secret_key=langfuse_secret_key,
  public_key=langfuse_public_key,
  host="https://us.cloud.langfuse.com"
)
#testing langfuse directly:
#messages = langfuse.get_prompt('aggregator_agent', type='chat', label="latest")
#logging.info(f'messages aggregator:{messages}')
GRAPHICS_DICT = {
    "Graphs of Functions & Equations": {
        "python": ["Matplotlib", "NumPy", "SymPy", "Plotly"],
        "javascript": ["Desmos API", "Plotly.js", "D3.js", "JSXGraph"]
    },
    "Data Visualization": {
        "python": ["Matplotlib", "Seaborn", "Plotly", "Altair", "Bokeh"],
        "javascript": ["Chart.js", "D3.js", "Plotly.js", "Highcharts"]
    },
    "Geometric Figures": {
        "python": ["Matplotlib", "Turtle", "Manim"],
        "javascript": ["JSXGraph", "p5.js", "Three.js"]
    },
    "Coordinate Systems & Axes": {
        "python": ["Matplotlib", "Plotly"],
        "javascript": ["D3.js", "Desmos API", "JSXGraph"]
    },
    "Transformations & Symmetry": {
        "python": ["Matplotlib (affine)", "Manim"],
        "javascript": ["JSXGraph", "p5.js"]
    },
    "Diagrams & Logical Structures": {
        "python": ["Graphviz", "NetworkX", "Matplotlib"],
        "javascript": ["Mermaid.js", "Cytoscape.js", "D3.js"]
    },
    "Statistical & Probability Models": {
        "python": ["Seaborn", "SciPy", "Matplotlib"],
        "javascript": ["Plotly.js", "D3.js"]
    },
    "Vector & Field Representations": {
        "python": ["Matplotlib (quiver)", "SymPy", "NumPy"],
        "javascript": ["JSXGraph", "D3.js", "Three.js"]
    },
    "Surface & Space Visualizations": {
        "python": ["Mayavi", "Plotly", "Matplotlib (3D)", "PyVista"],
        "javascript": ["Three.js", "Plotly.js", "MathBox"]
    },
    "Interactive or Dynamic Visuals": {
        "python": ["Plotly (Dash)", "Bokeh (server)", "ipywidgets + Jupyter"],
        "javascript": ["Desmos API", "JSXGraph", "D3.js",
                       "React + Canvas/WebGL"]
    }
}
