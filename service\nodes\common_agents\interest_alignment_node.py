from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.interest_agent import supporting_interest_agent, choose_random_interest_information
from service.context_pipeline import logger

def interest_inclusion_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles evaluating if an assignment effectively incorporates a student's interest.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting interest_inclusion_node")

    interest_information = state.interest_information
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type

    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif state.assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    try:
        result = supporting_interest_agent(
            initial_assignment=initial_content,
            interest_information=interest_information,
            nb= nb,
            assignment_type=assignment_type,
            assignment_state=state.assignment_state
        )
        logger.debug(f"Supporting interest agent result: {result}")
        if 'interest_guidance' in result:
            state.adjustment_instructions['interest'] = result['interest_guidance']
        else:
            state.adjustment_instructions['interest'] = result
        logger.info("Interest adjustment instructions updated.")

        return {"adjustment_instructions": state.adjustment_instructions}

    except Exception as e:
        logger.error(f"Error in interest_inclusion_node: {e}")
        return {}
    
def interest_search_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles searching for interest information.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting interest_search_node")

    interest = state.student.interest
    grade = state.student.grade

    try:
        result = choose_random_interest_information(
            interest=interest,
            grade=grade
        )
        logger.info("Completed interest_search_node successfully")
        
    except Exception as e:
        logger.error(f"Error in interest_search_node: {e}")
        raise

    return {"interest_information": result}
