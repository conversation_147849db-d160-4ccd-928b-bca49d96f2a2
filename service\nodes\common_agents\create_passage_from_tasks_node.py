from service.gen_data_models import GeneratePipelineContext
from service.common_agents.create_passage_from_task import create_passage_from_tasks_agent
from service.context_pipeline import logger
import copy
def create_passage_from_tasks_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting create_passage_from_tasks_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    enriched_task=state.enriched_tasks
    enriched_tasks_data_copy = copy.deepcopy(enriched_task)
    grade=state.student.grade
    main_topic=state.request.main_topic
    assignment_type=state.request.assignment_type
    interest_information=state.interest_information

    try:
        response = create_passage_from_tasks_agent(
            substandard=enriched_tasks_data_copy,
            grade=grade,
            main_topic=main_topic,
            assignment_type=assignment_type,
            interest_information=interest_information
        )
        # lookup = {d['skill_number']: d for d in extract_skills_output}

        # for item in response:
        #     skn = item['skill_number']
        #     if skn in lookup:
        #         item['skill'] = lookup[skn]['skill']
        #         item['demonstration_example'] = lookup[skn]['demonstration_example']
        for item in response:
            if 'enriched_task' in item:
                item['task'] = item.pop('enriched_task')
        logger.info(f"create_passage_from_tasks response: {response}")
        #adjustment_instructions['develop_tasks'] = response
    except Exception as e:
        logger.error(f"Error in create_passage_from_tasks_node: {e}")

    return {"enriched_tasks_passages": response,"challenges":response}