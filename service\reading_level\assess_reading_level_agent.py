import textstat
import re

def kincaid_grade_level_test_english(text):
    """
    Calculates the Flesch-Kincaid grade level for an English text.
    
    Parameters:
    - text (str): The input text for which to calculate the reading level.
    
    Returns:
    float: The Flesch-Kincaid grade level for the given text.
    """
    return textstat.flesch_kincaid_grade(text)

def extract_and_convert_grade(grade_str):
    """
    Extracts a numeric grade level from a string and adjusts it if necessary.
    
    Parameters:
    - grade_str (str or int): The string containing the grade level or the integer itself.
    
    Returns:
    int: The extracted numeric grade level, or None if not found.
    """
    if grade_str == "kindergarten":
        return 0
    if isinstance(grade_str, int):
        return grade_str
    numbers = re.findall(r'\d+', grade_str)
    if numbers:
        # Adjust grade level if necessary
        if numbers[0] == '4':
            numbers[0] = '5'
        return int(numbers[0])
    else:
        return None
