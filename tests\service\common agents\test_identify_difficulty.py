import pytest
from unittest.mock import patch, MagicMock

# Patch BEFORE the module is imported
patch('service.common_agents.identify_difficulty.langfuse.get_prompt').start()
patch('service.common_agents.identify_difficulty.make_openai_langfuse').start()

# Import after patching
from service.common_agents.identify_difficulty import identify_assignment_difficulty, adjust_assignment_difficulty

@patch('service.common_agents.identify_difficulty.langfuse.get_prompt')
@patch('service.common_agents.identify_difficulty.make_openai_langfuse')
def test_identify_assignment_difficulty(mock_make_openai, mock_get_prompt):
    # Setup mocks
    mock_criteria_prompt = MagicMock()
    mock_criteria_prompt.compile.return_value = "mocked_criteria"
    mock_agent_prompt = MagicMock()
    mock_agent_prompt.compile.return_value = "mocked_chat_prompt"
    mock_agent_prompt.config = {
        "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
    }

    mock_get_prompt.side_effect = [mock_criteria_prompt, mock_agent_prompt]

    mock_make_openai.return_value = {
        "difficulty_level": "medium",
        "reasoning": "Appropriate for grade level."
    }

    result = identify_assignment_difficulty(
        assignment_type="math",
        initial_assignment="Solve 5 + 3",
        grade="2",
        standard="CCSS.MATH.CONTENT.2.OA.A.1",
        nb=1
    )

    assert result == "medium"


@patch('service.common_agents.identify_difficulty.langfuse.get_prompt')
@patch('service.common_agents.identify_difficulty.make_openai_langfuse')
def test_adjust_assignment_difficulty(mock_make_openai, mock_get_prompt):
    # Mocks for identify_difficulty and adjustment prompt
    mock_criteria_prompt = MagicMock()
    mock_criteria_prompt.compile.return_value = "mocked_criteria"
    mock_agent_prompt = MagicMock()
    mock_agent_prompt.compile.return_value = "mocked_chat_prompt"
    mock_agent_prompt.config = {
        "openai": {"temperature": 0.5, "model": "gpt-4.1", "max_tokens": 4096}
    }

    mock_get_prompt.side_effect = [
        mock_criteria_prompt,  # for criteria
        mock_agent_prompt,     # for difficulty agent
        MagicMock(compile=MagicMock(return_value="adjust difficulty prompt"))  # for adjustment
    ]

    mock_make_openai.return_value = {
        "difficulty_level": "easy",
        "reasoning": "Too basic for grade level."
    }

    result = adjust_assignment_difficulty(
        intial_assignment="What is 2+2?",
        grade="5",
        standard="MATH.5.NBT.B.5",
        original_difficulty="hard",
        assignment_type="math",
        nb=1
    )

    assert "increase_difficulty" in result
    assert result["increase_difficulty"] == "adjust difficulty prompt"
