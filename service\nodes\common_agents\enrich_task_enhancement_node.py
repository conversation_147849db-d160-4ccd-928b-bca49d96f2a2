from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.context_pipeline import logger
from service.common_agents.enrich_task_enhancement import enrich_task_enhancement_agent
def enrich_task_enhancement_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles enhancing the enriched tasks to reduce repetition.

    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting enrich_task_enhancement_node")

    enriched_tasks = state.enriched_tasks
    interest = state.student.interest
    grade = state.student.grade
    assignment_type=state.request.assignment_type
    try:
        response = enrich_task_enhancement_agent(
            enriched_tasks=enriched_tasks,
            interest=interest,
            grade=grade,
            assignment_type=assignment_type
        )
    except Exception as e:
        logger.error(f"Error in enrich_task_enhancement_node: {e}")
        raise
    logger.info(f"enrich_task_enhancement_node Node execution completed successfully:{response}")
    index=0
    for item in response:
        if 'enriched_task' in item:
            item['task'] = item.pop('enriched_task')
            item['index']= index + 1
            index += 1
    return {"enriched_tasks": response}