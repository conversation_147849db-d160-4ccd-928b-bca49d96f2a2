from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
from service.common_functions import vocab_examples
def rewrite_passage_to_align_substandard_agent(passage:str,sub_standard:dict,alignment_plan:str,assignment_type:str,grade:str,interest:str,nb_of_questions:int) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
            
    messages = langfuse.get_prompt('rewrite_passage_to_align_substandard', type='chat', label="latest")
    additional_instruction=""
    if grade == "kindergarten" or '1' in grade:
        additional_instructions += "\nPlease ensure the reading passage is specifically written for kindergarten or first-grade students, using only age-appropriate vocabulary and simple sentence structure. The passage must not exceed 100 words in total. It should be highly engaging for young children and must be limited to no more than 2 short paragraphs. Absolutely avoid any content, concepts, or language that could be confusing or unsuitable for early readers. Do not go over the 100-word limit or more than 2 paragraphs under any circumstances."
    else:
        additional_instructions += f"\nPlease ensure the reading passage is suitable for {grade} grade. Ensure that the generated passage is at least 100 words."
    additional_instructions += f"Please ensure that the passage has enough context to be able to generate {nb_of_questions} distinct questions"
    complete_chat_prompt = messages.compile(
        passage=passage,
        sub_standard=sub_standard,
        alignment_plan=alignment_plan,
        additional_instruction=additional_instruction,
        interest=interest
    )
    tools = [{
        "type": "function",
        "function": {
            "name": "rewrite_passage",
            "description": "Rewrites a passage to align with the provided substandards.",
            "parameters": {
                "type": "object",
                "properties": {
                    "passage": {
                        "type": "array",
                        "items": {
                            "type": "string",
                            "description": "Paragraphs of the rewritten passage"
                        }
                    }
                },
                "required": ["passage"]
            }
        }
    }]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
    return result['passage']
