# models/pipeline_context.py
from pydantic import Field, Extra
from typing import Dict, Optional, List, Literal,Annotated
from aisdk.data_models.pipeline_context import Requests, PipelineContext
import random
import operator

def dict_update_reducer(a: dict, b: dict) -> dict:
    a = dict(a) 
    a.update(b)
    return a

class GenerateRequests(Requests):
    """Represents the parameters of the request for assignment generation."""
    state_standard: str = Field(..., description="The specific state standard the assignment should align with.")
    standard: Dict = Field(..., description="Detailed standard information (code, subject, grade, description, etc.).")
    nb_of_questions: int = Field(..., description="The desired number of questions or challenges in the assignment.")
    assignment_type: Literal[ # REQUIRED
        'math_worked_example', 'math_fact', 'math_home_dynamic', 'math_partner',
        'math_word_problem', 'math_story', 'reading_comp_gen', 'reading_comp_prov',
        'arg_essay', 'inf_essay', 'vocab_fill',
        'history_critical', 'history_fact', 'history_vocab', 'history_essay'
    ] = Field(..., description="The specific type of assignment to generate.")
    add_info: Optional[str] = Field(default="", description="Optional additional information or context provided by the teacher.")
    main_topic: Optional[str] = Field(default="", description="Optional main topic or theme for the assignment content.")
    passage: Optional[str] = Field(default="", description="Optional predefined passage for the assignment.")
    passage_style: Literal['fiction narrative', 'non-fiction narrative', 'argumentative', 'expository'] = Field(
        default_factory=lambda: random.choice(['fiction narrative', 'non-fiction narrative', 'argumentative', 'expository']),
        description="Desired style for generated passages. Randomly chosen if not specified."
    )
    session_id: str = Field(..., description="Identifier for the specific session or context of the request.")
    language: str = Field(default='english', description="The target language for the assignment content.")
    difficulty: str = Field(default='Medium', description="The desired difficulty level (e.g., Easy, Medium, Hard).")
    life_skills: List[Literal["Critical Thinking", "Creativity", "Problem Solving", "Communication", "Emotional Skills"]] = Field(
        default_factory=lambda: [random.choice(["Critical Thinking", "Creativity", "Problem Solving", "Communication", "Emotional Skills"])],
        description="A life skill focus for the assignment. Randomly chosen if not specified."
    )
    needs_grammar_focus: bool = Field(default=False, description="Indicates if the assignment should have a specific focus on grammar.")
    assignment_name: Optional[str] = Field(default="", description="Name or title of the assignment.")
    class Config:
        extra = Extra.ignore

class GeneratePipelineContext(PipelineContext):
    """Represents the overall context and state of the assignment generation pipeline."""
    assignment: Optional[Dict] = Field(default={}, description="The generated assignment content.")
    processed_standard: Optional[Dict] = Field(default={}, description="Processed and structured standard information.")
    request: GenerateRequests = Field(..., description="The initial request parameters for the assignment.")
    tools: Optional[List[str]] = Field(default=[], description="List of tools or services used during generation.")
    attempts: Optional[int] = Field(default=0, description="Counter for generation attempts (e.g., retries).")
    assignment_state: Optional[str]= Field(default="initial", description="Current state of the pipeline.")
    ai_passage: Optional[List[str]] = Field(default="", description="AI-generated passage content, if applicable.")
    request_id: str = Field(..., description="Unique identifier for the request, if applicable.")
    adjustments_done: Dict[str, bool] = Field(default_factory=lambda: { "kindergarten": False, "translated": False }, description="Flags to prevent duplicate execution of adjustment nodes.")
    inclusion_alignment_done: Dict[str, bool] = Field(default_factory=lambda: { "interest": False, "standard": False, "additional_information": False}, description="Flags to track completion of inclusion adjustments.")
    adjustment_instructions: Annotated[Dict[str, str],dict_update_reducer] = Field( default_factory=dict, description="Instructions on assignment adjustment." )
    interest_information: str = Field(default="", description="Information related to student's interest.")
    enriched_tasks: Dict[str, Dict] = Field(default={}, description="Tasks enriched with student's interest.")
    refine_passage: bool = Field(default=False, description="Flag to decide if passage needs to be rewritten to align with substandard.")
    substandards_data: Dict[str, Dict] = Field(default={}, description="Data related to substandards.")
    enriched_tasks_passages: Dict[str, Dict] = Field(default={}, description="Tasks with passages enriched with student's interest.")
    challenges: Dict[str, Dict] = Field(default={}, description="Challenges generated for the assignment.")