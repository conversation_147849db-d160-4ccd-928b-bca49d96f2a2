from service.openai_utils.gpt_call import make_openai_langfuse
from concurrent.futures import ThreadPoolExecutor
from service.context_pipeline import langfuse
from typing import Union
from service.context_pipeline import logger, aisdk_object

def generate_rubrics_hints_agent(assignment: dict, grade: Union[int, str], assignment_type: str, nb: int) -> dict:
    """
    Generates hints based on rubrics for assignments.

    Uses an LLM to create helpful hints for students based on the assignment rubrics.

    Args:
        assignment: The assignment data containing rubrics
        grade: The student's grade level
        assignment_type: The type of assignment (essay or homework)

    Returns:
        dict: Updated assignment with generated hints in the description fields
    """
    logger.info(f"Starting generate_rubrics_hints_agent for {assignment_type} assignment:{assignment}")
    try:
        logger.debug("Retrieving generate_rubric_hints prompt from langfuse")
        message = langfuse.get_prompt('generate_rubric_hints', type='chat', label="latest")

        if 'essay' in assignment_type:
            logger.debug("Processing essay assignment for rubric hints")
            try:
                user_prompt = f"""
                    The rubric for the assignment and the assignment data are as follows:
                    Rubrics: {assignment['essay']['rubrics']}
                """
                user_prompt += """ *Bullet Formatting*: Format the essay prompt in clear bullet points. Each point must be separated by '\\n\\n' for readability. Ensure both instructions and the main essay prompt are formatted as bullets. Use this structure: - Take a clear stance on the issue.\n\n - Provide specific evidence to support your argument.\n\n - Organize your ideas effectively and logically.\n\n - Express your thoughts clearly and persuasively. """

                messages = message.compile(grade=grade, user_instruction=user_prompt)
                logger.debug("Making OpenAI request for essay rubric hints")
                
                temperature = message.config.get("openai", {}).get("temperature",0.5)
                model = message.config.get("openai", {}).get("model","gpt-4.1")
                max_tokens = message.config.get("openai", {}).get("max_tokens",4096)
                assignment['essay']['description'] = make_openai_langfuse(messages=messages, model=model, temperature=temperature, max_tokens=max_tokens)
                logger.info("Successfully generated hints for essay rubric")
            except Exception as e:
                logger.error(f"Error generating hints for essay: {e}")
        else:
            logger.debug(f"Processing homework assignment with challenges for rubric hints")
            if 'homework' in assignment:
                homework = assignment['homework']
            else:
                homework = assignment
            if 'challenges' in homework:
                challenges=homework['challenges']
            if type(challenges)==dict:
                challenges=[challenges]
            try:
                with ThreadPoolExecutor() as executor:
                    logger.debug("Creating thread pool for processing challenges")
                    futures = []
                    for i, question in enumerate(challenges):
                        logger.debug(f"Preparing rubric hints for challenge {i+1}")
                        user_prompt = f"""
                            The rubric for the assignment and the assignment data are as follows:
                            Rubrics: {question['rubrics']}
                        """
                        messages = message.compile(grade=grade, user_instruction=user_prompt)
                        temperature = message.config.get("openai", {}).get("temperature",0.5)
                        model = message.config.get("openai", {}).get("model","gpt-4.1")
                        max_tokens = message.config.get("openai", {}).get("max_tokens",4096)
                        futures.append(executor.submit(make_openai_langfuse, messages=messages, model=model, temperature=temperature, max_tokens=max_tokens))

                    for i, (question, future) in enumerate(zip(challenges, futures)):
                        try:
                            question['description'] = future.result()
                            logger.debug(f"Successfully generated hints for challenge {i+1}")
                        except Exception as e:
                            logger.error(f"Error generating hints for challenge {i+1}: {e}")
                logger.info("Successfully generated hints for all challenges")
            except Exception as e:
                logger.error(f"Error processing homework challenges: {e}")
        logger.info(f"Completed generate_rubrics_hints_agent successfully:{assignment}")
        return assignment
    except Exception as e:
        error_msg = f"Error in generate_rubrics_hints_agent: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}