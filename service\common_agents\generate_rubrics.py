from service.openai_utils.gpt_call import make_openai_langfuse
from concurrent.futures import ThreadPoolExecutor, as_completed
import json
from service.context_pipeline import langfuse
from service.context_pipeline import logger, aisdk_object

def generate_rubric_questions_agent(assignment: dict, standard: str, assignment_type: str, passage: str, grammar: bool, grade: str, difficulty: str, nb:int) -> dict:
    """
    Generates a list of Yes/No rubric questions to grade an assignment based on the given standard, grade level, and assignment type.

    Parameters:
    - assignment (dict): The JSON object containing the assignment.
    - standard (str): The standard outlining the topic's key components.
    - grade (str): The grade level of the student.
    - assignment_type (str): The type of assignment (e.g., math, reading, essay).
    - passage (str): The main topic or passage for the assignment.
    - grammar (bool): Whether grammar focus is needed.

    Returns:
    - dict: The updated assignment with rubric questions.
    """
    logger.info(f"Starting generate_rubric_questions_agent for {assignment_type} assignment")
    try:
        if not isinstance(assignment, dict):
            logger.debug("Assignment is not a dictionary, attempting to convert")
            try:
                assignment = json.loads(assignment)
                logger.info(f"Successfully converted assignment to dictionary{assignment}")
            except Exception as e:
                logger.error(f"Failed to convert assignment to dictionary: {e}")
                return {"error": "Invalid JSON format provided and could not be converted to a dictionary."}

        logger.debug("Checking assignment structure")
        if "homework" in assignment:
            homework = assignment["homework"]
            logger.debug("Found homework in assignment")
        else:
            homework = assignment
            logger.debug("No homework found in assignment, using assignment as homework")

        if "challenges" in homework:
            challenges = homework["challenges"]
            if type(challenges)==dict:
                logger.debug("Challenges is a dictionary, converting to list")
                challenges=[challenges]
            
            logger.info(f"Processing {len(challenges)} challenges in homework")

            new_challenges_list = [None] * len(challenges)
            with ThreadPoolExecutor() as executor:
                logger.debug("Creating thread pool for processing challenges")
                futures = []
                for index, challenge_data in enumerate(challenges):
                    logger.debug(f"Submitting challenge {index+1} to thread pool")
                    futures.append(executor.submit(create_rubrics_for_single_challenge, challenge_data, standard, grade, assignment_type, passage, grammar, difficulty, index))

                for future in as_completed(futures):
                    index, new_challenges = future.result()
                    new_challenges_list[index] = new_challenges
                    logger.debug(f"Completed processing challenge {index+1}")

            logger.debug("Updating homework with new challenges")
            homework["challenges"] = new_challenges_list
          

            logger.info(f"Successfully generated rubric questions for all challenges{homework}")
            return homework
        else:
            if 'essay' in assignment_type:
                logger.info("Processing essay assignment")
                if 'essay' in assignment:
                    logger.debug("Found essay in assignment")
                    homework = assignment['essay']
                    index, new_homework = create_rubrics_for_single_challenge(homework, standard, grade, assignment_type, passage, grammar, difficulty)
                    assignment['essay'] = new_homework
                    logger.info("Successfully generated rubric questions for essay")
                    return assignment
                else:
                    logger.debug("No essay found in assignment, using assignment as essay")
                    homework = assignment
                    index, new_homework = create_rubrics_for_single_challenge(homework, standard, grade, assignment_type, passage, grammar, difficulty)
                    assignment = new_homework
                    logger.info("Successfully generated rubric questions for assignment")
                    return assignment
            else:
                logger.info("No challenges or essay found in assignment, returning original assignment")
                return assignment

    except Exception as e:
        error_msg = f"Error in generate_rubric_questions_agent: {str(e)}"
        logger.error(error_msg)
        return {"error": error_msg}


def create_learning_outcomes(json_input,passage,grade,standard,difficulty):
    logger.info(f"Starting create_learning_outcomes for challenge")
    try:
        learning_outcomes_tools=[
        {
        "type": "function",
        "function": {
            "name": "generate_learning_outcomes",
            "description": "create a list of learning outcomes for the assignment",
            "parameters": {
                "type": "object",
                'properties': {
                "learning_outcomes": {
                        "type": "array",
                        "items": {
                        "type": "string",
                        "description": "learning outcome for the assignment"
                        },
                    },
                },
                "required":["learning_outcomes"]
                }
            }
        }
    ]
        
        passage_based_instructions=""
        if passage:
            passage_based_instructions += f"This is the passage used for the assignment:{passage}"
            logger.debug(f"Added passage to instructions for challenge")

        logger.debug("Retrieving generate_learning_objectives prompt from langfuse")
        messages=langfuse.get_prompt('generate_learning_objectives',type='chat',label="latest")
        complete_chat_prompt=messages.compile(question=json_input,additional_instructions=passage_based_instructions,grade=grade,standard=standard,difficulty=difficulty)

        logger.debug("Making OpenAI request to generate learning outcomes")
        temperature = messages.config.get("openai", {}).get("temperature",0.5)
        model = messages.config.get("openai", {}).get("model","gpt-4.1")
        max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
        outcomes=make_openai_langfuse(messages=complete_chat_prompt, tools=learning_outcomes_tools, temperature=temperature, model=model, max_tokens=max_tokens)
        logger.info(f"Successfully generated learning outcomes for challenge")
        return outcomes['learning_outcomes']
    except Exception as e:
        logger.error(f"Error in create_learning_outcomes for challenge: {e}")
        return json_input

def create_rubrics_for_single_challenge(assignment,standard,grade,assignment_type,passage,grammar,difficulty, index=0):
    try:
        learning_outcomes=create_learning_outcomes(assignment,passage,grade,standard,difficulty)
        logger.info(f"#*#*#*#*#*#* Simple Rubric questions generated for challenge {index}")
    except Exception as e:
        logger.info(f"Error in simplifying rubric questions for challenge. The error: {e}")

    logger.info(f"challenges recieved:{assignment}")
    rubrics_tool = [{
    "type": "function",
    "function": {
        "name": "generate_rubrics",
        "description": "Generate evaluation rubrics for student submissions",
        "parameters": {
            "type": "object",
            "properties": {
                "rubrics": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                            "rubric_question_number": {
                                "type": "string",
                                "description": "Question number (e.g., Q1, Q2)"
                            },
                            "rubric_question_text": {
                                "type": "string",
                                "description": "Binary Yes/No question for objective evaluation"
                            }
                        },
                        "required": ["rubric_question_number", "rubric_question_text"]
                    }
                }
            },
            "required": ["rubrics"]
        }
    }
}]

    additional_instruction=""
    if passage:
        additional_instruction += f"This is the passage used for the assignment:{passage}"
    if 'math' in assignment_type:
        subject='math'
    elif 'history' in assignment_type:
        subject='history'
    else:
        subject='english'
    grade_specific_instruction=""
    if grade == "kindergarten":
        grade_specific_instruction += """ ### Kindergarten guidelines: The rubric questions for kindergarten should be very leanient and easy to satisfy, Remeber that we are dealing with very young students who are just starting to learn. """
    task=assignment.get('task','')
    answerKey=assignment.get('answerKey','')
    messages=langfuse.get_prompt('generate_rubric_questions_agent',type='chat',label="latest")
    predefined_questions="Does the answer show a genuine attempt from the student to solve the question?"
    if grammar:
        predefined_questions += "\nDoes the student use complete sentences with minor errors that do not hinder readability or comprehension?"
    complete_chat_prompt=messages.compile(subject=subject,grade=grade,grade_specific_instruction=grade_specific_instruction,standard=standard,assignment_type=assignment_type,difficulty=difficulty,learning_objectives=learning_outcomes,predefined_questions=predefined_questions,task=task,answerKeys=answerKey,additional_instruction=additional_instruction)
    temperature = messages.config.get("openai", {}).get("temperature",0.5)
    model = messages.config.get("openai", {}).get("model","gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens",4096)
    rubric_questions=make_openai_langfuse(messages=complete_chat_prompt, tools=rubrics_tool, temperature=temperature, model=model, max_tokens=max_tokens)


    assignment['rubrics']=rubric_questions['rubrics']
    logger.info(f"#*#*#*#*#*#* Initial Rubric questions generated for challenge{index}: {assignment['rubrics']}")


    N = len(assignment['rubrics'])

    assignment['rubrics'].append({"rubric_question_number": f"Q{N+1}", "rubric_question_text": "Does the answer show a genuine attempt from the student to solve the question?"})
    if grammar:
        assignment['rubrics'].append({"rubric_question_number": f"Q{N+2}", "rubric_question_text": "Does the student use complete sentences with minor errors that do not hinder readability or comprehension?"})

    logger.info(f"Rubric questions generated for challenge {index}:,{assignment}")
    return index,assignment
