import json
from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import logger

def adjust_question_for_graphic(assignment_question: dict,
                                standard_used: str,
                                grade_level: str,
                                visual_decision_response: dict):
    """
    Called ONLY when `visual_decision_response["adjust_question"]` is True.
    Makes MINOR edits so the assignment naturally involves the recommended
    graphic, while remaining fully aligned with the original standard and
    appropriate for the stated grade level.

    Returns (OpenAI-function format):
    {
      "adjusted_assignment": {title, scenario, task, answerKey, index},
      "notes": "One concise sentence describing the tweak."
    }
    """
    logger.info("adjust_question_for_graphic")
    recommended_category = visual_decision_response.get("recommended_category", "NONE")
    system_prompt = f"""
ROLE
• You are a mathematics curriculum editor.

PRIMARY GOAL
• Perform MINOR wording or numeric tweaks so the assignment naturally calls for
  or benefits from a {recommended_category} graphic.

MUST-KEEP REQUIREMENTS
1. STANDARD FIDELITY – The adjusted assignment must still address the SAME
   mathematical standard given below (domain, objective, cluster, etc.).
2. GRADE APPROPRIATENESS – Cognitive demand, wording, and numbers must be
   suitable for grade {grade_level}.
3. ORIGINAL LEARNING OBJECTIVE – Do not shift the conceptual focus.

CONSTRAINTS
• Preserve the JSON structure: title, scenario, task, answerKey, index.
• Do NOT reveal or hint at answers inside scenario or task.
• If the answer changes, update answerKey accordingly.
• Add a short sentence in the task telling students they will use / interpret
  the forthcoming {recommended_category} graphic (no answers).

OUTPUT  (STRICT – nothing else)
{{
  "adjusted_assignment": {{ **SAME STRUCTURE AS ORIGINAL ASSIGNMENT** }},
  "notes": "<ONE sentence summarising what changed>"
}}
    """.strip()

    user_prompt = f"""
Original assignment_question (JSON):
{json.dumps(assignment_question, indent=2)}

Educational standard (must be preserved):
{standard_used}

Target grade level:
{grade_level}

Required graphic category:
{recommended_category}

TASK
Make the minimal edits described in the system prompt and output ONLY the JSON
object in the specified schema.
""".strip()


    tools = [{
        "type": "function",
        "function": {
            "name": "adjusted_assignment_output",
            "description": "Return the minimally revised assignment and a brief note.",
            "parameters": {
                "type": "object",
                "properties": {
                    "adjusted_assignment": {
                        "type": "object",
                        "description": ("Revised assignment; must include keys "
                                        "title, scenario, task, answerKey, index.")
                    },
                    "notes": {
                        "type": "string",
                        "description": "One-sentence summary of what changed."
                    }
                },
                "required": ["adjusted_assignment", "notes"]
            }
        }
    }]

    messages = [
        {"role": "system", "content": system_prompt},
        {"role": "user",   "content": user_prompt}
    ]

    response = make_openai_langfuse(
        messages=messages,
        tools=tools,
        model="gpt-4.1-nano",
        temperature=0.35
    )

    logger.info(f"adjust_question_response: {response}")
    return response['adjusted_assignment']