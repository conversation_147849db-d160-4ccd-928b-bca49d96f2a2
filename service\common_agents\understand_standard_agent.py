from service.openai_utils.gpt_call import make_openai_json_response
from service.context_pipeline import langfuse
from service.context_pipeline import logger


def understand_standard_agent(
    cluster_statement: str,
    common_core_standard: str,
    grade: str,
    subject: str,
    topic: str,
    standard_code: str
) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        cluster_statement (str): The cluster statement generated from previous prompts.
        common_core_standard (str): The Common Core standard being referenced.
        grade (str): The grade level.
        subject (str): The subject being taught (e.g., ELA, Math).
        topic (str): The specific topic or learning objective.

    Returns:
        dict: API response from the OpenAI call, or an error payload on failure.
    """

    try:
        # 1. Build the prompt from Langfuse
        messages = langfuse.get_prompt("understand_standard", type="chat", label="latest")

        complete_chat_prompt = messages.compile(
            cluster_statement=cluster_statement,
            common_core_standard=common_core_standard,
            grade=grade,
            subject=subject,
            topic=topic,
            standard_code=standard_code
        )

        # 2. Extract OpenAI configuration (provide sensible defaults)
        config = messages.config.get("openai", {})
        model = config.get("model", "gpt-4.1")
        temperature = config.get("temperature", 0.5)
        max_tokens = config.get("max_tokens", 4096)

        # 3. Call OpenAI through Langfuse wrapper
        result = make_openai_json_response(
            complete_chat_prompt,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
        )
        return result

    except Exception as exc:
        # Capture the full traceback in logs for easier debugging
        logger.exception("Error in understand_standard_agent: %s", exc)

        # Optionally, downstream code can inspect the 'error' key
        return {
            "error": str(exc),
            "message": "Failed to process understand_standard_agent",
        }