from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse
from service.context_pipeline import logger
import json
def inspect_passage_substandard_alignment_agent(passage:str,sub_standard:dict) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
            
    messages = langfuse.get_prompt('inspect_passage_substandard_alignment', type='chat', label="latest")

    complete_chat_prompt = messages.compile(
        passage=passage,
        sub_standard=sub_standard
    )
    tools = [
    {
        "type": "function",
        "function": {
            "name": "generate_passage_alignment_report",
            "description": "Analyzes a reading passage in relation to a specific educational substandard and returns an alignment report.",
            "parameters": {
                "type": "object",
                "properties": {
                    "alignment": {
                        "type": "string",
                        "description": "How well the passage addresses the substandard: 'fully addresses', 'partially addresses', 'does not address', or 'viable context only'.",
                        "enum": [ "fully addresses", "partially addresses", "does not address", "viable context only" ]
                    },
                    "justification": {
                        "type": "string",
                        "description": "A concise rationale explaining the chosen alignment."
                    },
                    "viable_for_questions": {
                        "type": "boolean",
                        "description": "True if the passage can support meaningful follow-up questions or assessment tasks for this substandard; otherwise, False."
                    },
                    "suggestions": {
                        "type": "string",
                        "description": "Instructional suggestions for using or adapting the passage (may be an empty string if not applicable)."
                    }
                },
                "required": [ "alignment", "justification", "viable_for_questions", "suggestions" ]
            }
        }
    }
]
    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens,tools=tools)
    
    return result
