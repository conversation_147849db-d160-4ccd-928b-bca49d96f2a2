from service.gen_data_models import GeneratePipelineContext, GenerateRequests
from typing import Dict
from aisdk.data_models.pipeline_context import PipelineContext
from aisdk.data_models.people import Student
from service.context_pipeline import logger,aisdk_object
import random
from service.common_agents.understand_standard_agent import understand_standard_agent
from service.common_agents.extract_skills_agent import extract_skills_agent
def preprocess_gen_input(data: Dict, request_id: str, default_ctx: PipelineContext) -> GeneratePipelineContext:   
    """
    Validates and preprocesses the input data for assignment generation.

    Args:
        data: The raw input dictionary from the request.
        request_id: The request trace ID for error logging.

    Returns:
        A dictionary containing validated and processed parameters.

    Raises:
        ValueError: If any validation fails.
    """
    assignment_type=data['assignment_type']
    reading_level=data['reading_level']
    standard=data['standard']
    reading_levels_list = [ "kindergarten", "1st", "2nd", "3rd", "4th", "5th", "6th", "7th", "8th", "9th", "10th", "11th", "12th" ]
    try:
        idx = reading_levels_list.index(reading_level)
        if idx==0:
            data['reading_level']='kindergarten'
        else:
            data['reading_level']=reading_levels_list[idx - 1]
    except:
        logger.error(f'failed to reduce reading level for input:{reading_level}')
    try:
        request_body=GenerateRequests(**data)
        logger.info(f"Request body validated: {request_body}")
        default_ctx.request=request_body
    except Exception as e:
        logger.error("Error retrieving student profile: %s", e)
    if 'interests' in data:
        if isinstance(data['interests'], list):
            data['interest'] = random.choice(data['interests'])
        else:
            data['interest'] = data['interests']
    id=standard.get('id','')
    code=standard.get('standard_code','')
    try:
        processed_standard=aisdk_object.firestore.get_processed_standard(doc_id=id)
        if processed_standard is None:
            raise Exception(f"Standard not found for {id}")
        else:
            logger.info(f"Standard found in firestore: {processed_standard}")
    except:
        logger.warning(f"Standard not found in firestore: {id}, trying with {code}")
        try:
            processed_standard=aisdk_object.firestore.get_processed_standard(standard_code=code)
            if processed_standard is not None:
               logger.info(f"Standard found in firestore: {processed_standard}")
            else:
                raise Exception(f"Standard not found for {id},{code}")
        except:
            logger.warning(f"Standard not found in firestore: {code}")
            agent_1_output=understand_standard_agent(standard.get('cluster_statement',''),standard.get('common_core_standard',''),data.get('grade',''),standard.get('subject',''),standard.get('topic',''))
            agent_2_output=extract_skills_agent(agent_1_output)
            processed_standard=agent_2_output
            doc_id=standard.get('id',(standard.get('standard_code','')))
            logger.info(f"Storing standard:{standard} with agents_output={processed_standard} in firestore: {doc_id}")
            aisdk_object.firestore.store_data_with_standard(doc_id=doc_id,standard=standard,data_to_store=processed_standard)
            # add the graph for creating data for this and storing in the database
    if 'standard_code' in processed_standard:
        del processed_standard['standard_code']
    default_ctx_dictionary = default_ctx.dict()
    default_ctx_dictionary['processed_standard']=processed_standard
    default_ctx_dictionary['request_id']=request_id
    default_ctx_dictionary['request']=request_body
    default_ctx_dictionary['student'] = Student(**data)
    
    try:
        ptx=GeneratePipelineContext(**default_ctx_dictionary)
        logger.info(f"GeneratePipelineContext created: {ptx}")
    except Exception as e:
        logger.error(f"Error creating GeneratePipelineContext: {e}")
    return ptx
