from service.gen_data_models import GeneratePipelineContext
from service.common_agents.rewrite_passage_to_align_substandard import rewrite_passage_to_align_substandard_agent
from service.context_pipeline import logger

def rewrite_passage_to_align_substandard_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting rewrite_passage_to_align_substandard_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    passage=state.ai_passage
    assignment_type=state.request.assignment_type
    grade=state.student.grade
    interest=state.student.interest
    nb_of_questions = state.request.nb_of_questions
    if "passage_alignment_planner" in adjustment_instructions:
        alignment_plan=adjustment_instructions['passage_alignment_planner']
    else:
        logger.error("rewrite_passage_substandard_called without passage_alignment_planner")
        return state
    try:
        response = rewrite_passage_to_align_substandard_agent(
            passage=passage,
            sub_standard=sub_standard,
            alignment_plan=alignment_plan,
            assignment_type=assignment_type,
            grade=grade,
            interest=interest,
            nb_of_questions=nb_of_questions
        )
        logger.info(f"rewrite_passage_to_align_substandard response: {response}")
        adjustment_instructions.pop('passage_alignment_planner')
        if 'passage' in response:
            response = response['passage']
        logger.info("Passage successfully revised to align with substandard.")
        return {'ai_passage': response}
    except Exception as e:
        logger.error(f"Error in rewrite_passage_to_align_substandard_node: {e}")
        return {"ai_passage": passage}
