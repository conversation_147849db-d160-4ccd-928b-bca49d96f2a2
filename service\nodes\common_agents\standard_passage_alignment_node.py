from service.gen_data_models import GeneratePipelineContext
from service.common_agents.standard_passage_alignment import standard_passage_alignment_agent
from service.context_pipeline import logger

def standard_passage_alignment_node(state: GeneratePipelineContext):
    """
    
    """
    logger.info(f"Starting standard_passage_alignment_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    sub_standard=state.processed_standard
    passage=state.ai_passage
    assignment_type=state.request.assignment_type
    grade=state.student.grade
    interest=state.student.interest
    nb_of_questions = state.request.nb_of_questions
    if "inspect_passage_justification" in adjustment_instructions:
        justification=adjustment_instructions['inspect_passage_justification']
    else:
        logger.error("rewrite_passage_substandard_called without inspect_passage_justification")
        return state
    try:
        response = standard_passage_alignment_agent(
            passage=passage,
            sub_standard=sub_standard,
            justification=justification,
            assignment_type=assignment_type,
            grade=grade,
            interest=interest,
            nb_of_questions=nb_of_questions
        )
        logger.info(f"standard_passage_alignment response: {response}")
        adjustment_instructions.pop('inspect_passage_justification')
        adjustment_instructions['passage_alignment_planner']=response
        return {'adjustment_instructions': adjustment_instructions}
    except Exception as e:
        logger.error(f"Error in rewrite_passage_to_align_substandard_node: {e}")
        return {"ai_passage": passage}
