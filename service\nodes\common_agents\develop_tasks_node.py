from service.gen_data_models import GeneratePipelineContext
from service.common_agents.develop_tasks_agent import develop_tasks_agent
from service.context_pipeline import logger

def develop_tasks_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting develop_tasks_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    extract_skills_output=state.processed_standard
    grade=state.student.grade
    nb_of_questions = state.request.nb_of_questions
    main_topic=state.request.main_topic
    assignment_type=state.request.assignment_type
    # if not adjustment_instructions:
    #     logger.info("No adjustments provided. develop_tasks node skipped.")
    #     return {}
    if 'skills' in extract_skills_output:
        extract_skills_output=extract_skills_output['skills']
    try:
        response = develop_tasks_agent(
            extract_skills_output=extract_skills_output,
            grade=grade,
            nb_of_questions=nb_of_questions,
            main_topic=main_topic,
            assignment_type=assignment_type
        )
        lookup = {d['skill_number']: d for d in extract_skills_output}

        for item in response:
            skn = item['skill_number']
            if skn in lookup:
                item['skill'] = lookup[skn]['skill']
                item['demonstration_example'] = lookup[skn]['demonstration_example']

        logger.info(f"develop_tasks response: {response}")
        adjustment_instructions['develop_tasks'] = response
    except Exception as e:
        logger.error(f"Error in develop_tasks_node: {e}")
        raise

    return {"adjustment_instructions": adjustment_instructions,"substandards_data": extract_skills_output}