from service.gen_data_models import GeneratePipelineContext
from typing import Dict, Any
from service.common_agents.working_style_agent import identify_working_style_inclusion
from service.context_pipeline import logger

def working_style_inclusion_node(state: GeneratePipelineContext) -> Dict[str, Any]:
    """
    Node that handles evaluating if an assignment effectively incorporates a student's working style.
    Extracts necessary inputs from the state and calls the core agent functionality.
    """
    logger.info("Starting working_style_inclusion_node")

    working_style = state.student.working_style
    nb = state.request.nb_of_questions
    assignment_type = state.request.assignment_type

    if state.assignment_state == 'processing_passage':
        initial_content = state.ai_passage
        logger.info("Adjusting difficulty for passage.")
    
    elif state.assignment_state == 'processing_assignment':
        initial_content = state.assignment
        logger.info("Adjusting difficulty for assignment.")
    
    else:
        return logger.warning(f"Unrecognized assignment_state: {state.assignment_state}")

    try:
        content = identify_working_style_inclusion(
            initial_assignment=initial_content,
            working_style=working_style,
            nb= nb,
            assignment_type=assignment_type
        )
        logger.debug(f"Working style assessment response: {content}")
    
        next_steps = content.get('next_steps', '').strip()
        if next_steps:
            state.adjustment_instructions['working_style'] = next_steps
            logger.info("Working style adjustment instructions updated.")
        else:
            logger.warning("Adjustment indicated but no 'next_steps' provided.")
        
        return {"adjustment_instructions": state.adjustment_instructions}

    except Exception as e:
        logger.error(f"Error in working_style_inclusion_node: {e}")
        return {}