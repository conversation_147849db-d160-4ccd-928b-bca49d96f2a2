from service.openai_utils.gpt_call import make_openai_langfuse
from service.context_pipeline import langfuse, logger
def standard_passage_alignment_agent(passage:str,sub_standard:dict,justification:str,assignment_type:str,grade:str,interest:str) -> dict:
    """
    Aggregates adjustment instructions from all critique agents and revises the assignment accordingly.

    Args:
        original_assignment (str): Original student assignment content.
        adjustment_instructions (dict): Adjustment instructions from critique agents provided only if adjustments are needed.

    Returns:
        dict: Revised assignment and implemented adjustments.
    """
    logger.info("Starting standard_passage_alignment_agent")        
    messages = langfuse.get_prompt('passage_substandard_planner', type='chat', label="latest")
    additional_instruction=""
    complete_chat_prompt = messages.compile(
        passage=passage,
        sub_standard=sub_standard,
        justification=justification,
        additional_instruction=additional_instruction,
        interest=interest
    )

    temperature = messages.config.get("openai", {}).get("temperature", 0.5)
    model = messages.config.get("openai", {}).get("model", "gpt-4.1")
    max_tokens = messages.config.get("openai", {}).get("max_tokens", 4096)
    
    result = make_openai_langfuse(complete_chat_prompt, model=model, temperature=temperature, max_tokens=max_tokens)
    logger.info(f"standard_passage_alignment_agent response: {result}")
    return result
