from service.gen_data_models import GeneratePipelineContext
from service.common_agents.extract_skills_agent import extract_skills_agent
from service.context_pipeline import logger

def extract_skills_node(state: GeneratePipelineContext):
    """
    Node that aggregates all provided adjustment instructions from previous critique agents,
    calls the aggregator_agent, and updates the assignment accordingly.
    """
    logger.info(f"Starting extract_skills_node:{state}")

    adjustment_instructions = state.adjustment_instructions
    understand_standard_output=adjustment_instructions['understand_standard']

    if not adjustment_instructions:
        logger.info("No adjustments provided. extract_skills node skipped.")
        return {}

    try:
        response = extract_skills_agent(
            understand_standard_output=understand_standard_output
        )

        logger.info(f"extract_skills response: {response}")
        adjustment_instructions['extract_skills'] = response
    except Exception as e:
        logger.error(f"Error in extract_skills_node: {e}")
        raise

    return {"adjustment_instructions": adjustment_instructions}